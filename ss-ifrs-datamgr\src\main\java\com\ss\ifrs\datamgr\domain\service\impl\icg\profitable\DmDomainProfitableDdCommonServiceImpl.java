package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.profitable.DmDomainProfitableDdCommonDao;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_COMMON")
public class DmDomainProfitableDdCommonServiceImpl implements DmDomainProfitableCommonService {
    @Autowired
    private DmDomainProfitableDdCommonDao dmDomainProfitableDdCommonDao;

    @Override
    public void prepareQuota(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdCommonDao.deletePrepareQuota(dmIcgProcVo);
        dmDomainProfitableDdCommonDao.prepareQuota(dmIcgProcVo);
    }

    @Override
    public void prepareDevelopQuota(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdCommonDao.deletePrepareDevelopQuota(dmIcgProcVo);
        dmDomainProfitableDdCommonDao.prepareDevelopQuota(dmIcgProcVo);
    }

    @Override
    public void prepareStressLossRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdCommonDao.prepareStressLossRate(dmIcgProcVo);
    }

    @Override
    public void prepareInterestRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdCommonDao.prepareInterestRate(dmIcgProcVo);
    }

    @Override
    public void checkFactor(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdCommonDao.checkQuotaPaa(dmIcgProcVo);
        dmDomainProfitableDdCommonDao.checkQuotaBba(dmIcgProcVo);
        dmDomainProfitableDdCommonDao.checkInterestRatePaa(dmIcgProcVo);
        dmDomainProfitableDdCommonDao.checkInterestRateBba(dmIcgProcVo);
        dmDomainProfitableDdCommonDao.checkStressLossRate(dmIcgProcVo);
    }
}
