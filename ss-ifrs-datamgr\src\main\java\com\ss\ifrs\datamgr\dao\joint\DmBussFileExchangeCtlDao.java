/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-07-06 11:13:32
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.joint;

import com.ss.ifrs.datamgr.pojo.joint.po.DmBussFileExchangeCtl;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-07-06 11:13:32<br/>
 * Description: 文件交换控制表 Dao类<br/>
 * Related Table Name: dm_buss_file_exchange_ctl<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmBussFileExchangeCtlDao extends IDao<DmBussFileExchangeCtl, Long> {

    /**
     * 查询业务日期最大的那条数据
     */
    DmBussFileExchangeCtl queryByMaxBussDate();

    /**
     * 查询带处理的 （status in 0,3）
     */
    List<DmBussFileExchangeCtl> queryPending(String yearMonth);

    void updateAllById(DmBussFileExchangeCtl po);

    /**
     * 统计某业务日期未成功模型的数量
     */
    int countNotYetSuccessModels(Date bussDate);

    /**
     * 查询业务日期下校验成功的模型(去重)
     * @param bussDate
     * @return
     */
    List<String> queryDealSuccessModels(Date bussDate);

    /**
     * 查询待处理的日期
     */
    DmBussFileExchangeCtl queryPendingBussDate(String yearMonth);

    DmBussFileExchangeCtl queryProcessingOrFailed();

    DmBussFileExchangeCtl queryLastFileExchangeCtl();

    DmBussFileExchangeCtl findByBussDate(Date bussDate);
}