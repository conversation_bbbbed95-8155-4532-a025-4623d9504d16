package com.ss.ifrs.datamgr.feign;

import java.util.Map;

import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.TrackWorkFlowActionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;

/**
 * 调用BPL流程管理的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsActProcFeignClient {
    /**
     * "根据流程编码查找流程信息"
     *
     * @param
     * @return 流程定义信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/act/find_track_valid_act")
    BaseResponse<Map<String, ActOverviewVo>> findTrackActProcdef();

    /**
     * "根据流程id查找流程信息"
     *
     * @param procId 流程id
     * @return 流程定义信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/act/find_by_id/{procId}")
    BaseResponse<ActOverviewVo> findById(@PathVariable("procId") Long procId);


    @RequestMapping(method = RequestMethod.POST, value = "/actionLog/save_proc_log")
    BaseResponse<Object> saveProcLog(@RequestBody TrackWorkFlowActionVo actionLogVo);

    /**
     * "综合查询流程节点信息"
     *
     * @return 流程定义信息
     */
    @RequestMapping(method = RequestMethod.POST, value = "/act/find_by_object")
    BaseResponse<Object> findActOverviewByObject(@RequestBody ActOverviewVo actOverviewVo);

    @RequestMapping(method = RequestMethod.GET, value = "/act/find_last_by_code/{systemCode}/{procCode}")
    BaseResponse<ActOverviewVo> findPrevByCode(@PathVariable("systemCode") String systemCode
            , @PathVariable("procCode") String procCode);

}
