package com.ss.ifrs.datamgr.domain.service.impl.joint;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfDrawTypeDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableColumnDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainDataDraw;
import com.ss.ifrs.datamgr.domain.service.joint.DmDataDrawApiService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfControl;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfDrawType;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableColumnVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmTaskCodeAdapterVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDrawColumnParseVo;
import com.ss.ifrs.datamgr.service.conf.DmConfControlService;
import com.ss.ifrs.datamgr.service.conf.DmConfTableService;
import com.ss.ifrs.datamgr.util.JsonUtils;
import com.ss.library.utils.StringUtil;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.InputMismatchException;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * @className: DmDataDrawApiServiceImpl
 * @description: 数据采集Api服务接口实现类
 * @author: yinxh.
 * @createTime: 2021/5/7 18:17
 * @version: 1.0
 */
@Slf4j
@Service(DmConstant.DrawType.API)
@Transactional
public class DmDataDrawApiServiceImpl extends DmDomainDataDraw implements DmDataDrawApiService {

    @Autowired
    BbsConfEntityFeignClient bbsConfEntityFeignClient;
    @Autowired
    DmConfTableColumnDao dmConfTableColumnDao;
    @Autowired
    private DmConfTableService dmConfTableService;

    @Autowired
    DmConfControlService dmConfControlService;
    @Autowired
    DmConfDrawTypeDao dmConfDrawTypeDao;
    /**
     * @param : [jsonParams, dmDuctDrawLogVo]
     * @description: API方式采集数据
     * @return: void
     * @throws:
     * @author: yinxh.
     * @createTime: 2021/5/7 19:09
     */
    @Override
    public void dataDrawAction(JSONObject jsonParams, DmDataDrawVo dmDataDrawVo) throws Exception {

        // 总控开关
        DmConfControl dmConfControl = dmConfControlService.searchConfigControl();
        // API开关
        DmConfDrawType dmConfDrawType = dmConfDrawTypeDao.findDataByType(DmConstant.DrawType.API);
        if ( DmConstant.ControlType.Close.equals(dmConfControl.getOpenedIs())
                || DmConstant.ControlType.Close.equals(dmConfDrawType.getOpenedIs()) ){
            return;
        }
        // 获取业务单位编码ID
        String entityCode = jsonParams.getString("entityCode");
        BbsConfEntityVo bbsConfEntityVo = bbsConfEntityFeignClient.findByEntityCode(entityCode);
        dmDataDrawVo.setEntityId(bbsConfEntityVo.getEntityId());

        DmTaskCodeAdapterVo dmTaskCodeAdapterVo = new DmTaskCodeAdapterVo();
        dmTaskCodeAdapterVo.setEntityId(dmDataDrawVo.getEntityId());
        dmTaskCodeAdapterVo.setDrawType(dmDataDrawVo.getDrawType());
        dmTaskCodeAdapterVo.setYearMonth(dmDataDrawVo.getYearMonth());
        String taskCode = this.getTaskCode(dmTaskCodeAdapterVo);

        // 获取I17保批单业务信息表业务表配置及元数据配置 bizCode
        String bizCode = jsonParams.getString("bizCode");
        DmConfTableVo dmConfTableVo = dmConfTableService.findVoByBizCode(bizCode);
        List<DmConfTableColumnVo> listByColTypeCode = dmConfTableColumnDao.findListByBizTypeId(dmConfTableVo.getBizTypeId());

        // 遍历组装业务表结构list
        List<DmDrawColumnParseVo> columnParseList = new ArrayList<>();
        DmDrawColumnParseVo dmDrawColumnParseVo = null;
        for (DmConfTableColumnVo dmConfTableColumnVo : listByColTypeCode) {
            dmDrawColumnParseVo = new DmDrawColumnParseVo();
            dmDrawColumnParseVo.setColCode(dmConfTableColumnVo.getColCode());
            dmDrawColumnParseVo.setColType(dmConfTableColumnVo.getColType());
            dmDrawColumnParseVo.setColLength(dmConfTableColumnVo.getColLength());
            dmDrawColumnParseVo.setNeedIs(dmConfTableColumnVo.getNeedIs());
            columnParseList.add(dmDrawColumnParseVo);
        }

        // 解析jsonObject
        List<List<DmDrawColumnParseVo>> columnLists = JsonUtils.parseJson(jsonParams, columnParseList);

        if (StringUtil.isNotEmpty(columnLists) && columnLists.size() > 0) {
            // 额外处理增加字段属性
            List<DmDrawColumnParseVo> exColumnParseVos;
            for (List<DmDrawColumnParseVo> dmParseExcelVos : columnLists) {
                exColumnParseVos = this.createDefaultValue(taskCode, dmDataDrawVo.getCreatorId(), dmConfTableVo.getSrcSeqName().toLowerCase());
                dmParseExcelVos.addAll(exColumnParseVos);
            }

            try {
                //保存到数据库
                this.saveDrawData(columnLists, DmConstant.DbStruct.ODS_TABLE_PREFIX + dmConfTableVo.getBizCode());
            } catch (Exception ex) {
                log.error("Input irregularity : ", ex);
                throw new InputMismatchException("Input irregularity : " + ex.getCause());
            }

            queueDataVerify(dmDataDrawVo);

        } else {
            throw new NoSuchElementException("Cannot deal an empty data!");
        }
    }
}
