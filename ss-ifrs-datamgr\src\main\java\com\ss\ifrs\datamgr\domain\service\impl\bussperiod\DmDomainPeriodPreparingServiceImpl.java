package com.ss.ifrs.datamgr.domain.service.impl.bussperiod;

import com.ss.ifrs.datamgr.domain.abst.DmDomainPeriod;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodDetailService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfBussPeriod;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodService;
import com.ss.library.utils.DateUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service(CommonConstant.BussPeriod.PeriodStatus.PREPARING)
public class DmDomainPeriodPreparingServiceImpl extends DmDomainPeriod implements DmDomainPeriodService {

    String state = CommonConstant.BussPeriod.PeriodStatus.PREPARING;
    Long userId = 1L;

    @Autowired
    private DmDomainBussPeriodService domainBussPeriodService;

    @Autowired
    private DmDomainPeriodDetailService bussPeriodDetailService;

    @Autowired
    private DmConfBussPeriodService confBussPeriodService;

    /**
     * 执行方法 业务期间：0-准备中
     */
    @Override
    public void execution(Long entityId, String yearMonth) {
        /**
         *执行逻辑：
         * 1、检查是否有准备中的业务期间；(有且仅能有一个在准备中的业务期间)
         * 2、如有：退出并提示异常
         * 3、如无：新增当前最大业务期间的下一个业务期间进行准备；
         * */

        //检验是否存在准备中的业务期间
        String checkYearMonth = domainBussPeriodService.getPreparingYearMonth(entityId);
        if (StringUtil.isNotEmpty(checkYearMonth)) {
            return;
        }

        DmConfBussPeriod confBussPeriod = new DmConfBussPeriod();
        confBussPeriod.setEntityId(entityId);
        // 获取当前最大业务期间并增加下一个业务期间
        checkYearMonth = domainBussPeriodService.getMaxYearMonth(entityId);
        String nextYearMonth;
        if (StringUtil.isEmpty(checkYearMonth)) {
            nextYearMonth = DateUtil.nowStr("yyyyMM");
        } else {
            nextYearMonth = DateUtil.addAnyMonth(checkYearMonth, 1);
        }
        confBussPeriod.setYearMonth(nextYearMonth);
        confBussPeriod.setPeriodState(this.state);
        confBussPeriod.setCreatorId(this.userId);
        confBussPeriodService.add(confBussPeriod);
    }

    /**
     * 执行方法 业务期间：0-准备中
     */
    @Override
    public void executionDetail(Long entityId, String yearMonth, String bizCode) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setYearMonth(yearMonth);
        bussPeriodReqVo.setPeriodState(this.state);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.PREPARING);
        //bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.INPUT);
        bussPeriodReqVo.setUpdatorId(this.userId);
        bussPeriodReqVo.setUpdateTime(new Date());
        bussPeriodDetailService.update(bussPeriodReqVo);
    }
}
