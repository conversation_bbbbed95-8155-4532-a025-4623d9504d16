package com.ss.ifrs.datamgr.dao.joint;

import com.ss.ifrs.datamgr.pojo.joint.po.DmOdsDataPushSignal;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmEtlTaskTodoVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmOdsDataPushSignalVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.pojo.com.vo.DataSyncReqVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12). Create Date:
 * 2022-02-14 16:28:52<br/>
 * Description: DmSrcModelSignal|模型信号表，用于记录源数据供应和处理情况 Dao类<br/>
 * Related Table Name: odsuser.ods_data_push_signal<br/>
 * <br/>
 * Remark: 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface OdsDataPushSignalDao extends IDao<DmOdsDataPushSignal, Long> {
    List<DmEtlTaskTodoVo> findTodoList(DmOdsDataPushSignal dmOdsDataPushSignal);

    /**
     * @return com.ss.platform.mybatis.model.Page<com.ss.ifrs.datamgr.pojo.other.po.DmOdsDataPushSignal>
     * @throws
     * @description ETL推送信息日志分页查询
     * @params [dmOdsDataPushSignalVo, pageParam]
     * <AUTHOR>
     * @date 2022/7/11 10:43
     */
    Page<DmOdsDataPushSignal> fuzzySearchPage(DmOdsDataPushSignalVo dmOdsDataPushSignalVo, Pageable pageParam);


    void deleteEtlData(HashMap<String, Object> map);

    void updateEtlStatus(HashMap<String, Object> map);

    List<String> pushSignalGetTaskCode(@Param("vo") DataSyncReqVo vo);

    /**
     * 同步文件交换信号数据到ODS信号表
     */
    void addDataPushSignal(@Param("vo") DmOdsDataPushSignalVo vo);

    List<String> queryTaskCodePushSignal(@Param("yearMonth") String yearMonth, @Param("pushModel") String pushModel, @Param("taskStatus") String taskStatus);

    List<String> getRestStatTaskCode(@Param("yearMonth") String yearMonth, @Param("pushModel") String pushModel);

    List<String> getTaskCodeByYearMonthAndStatusAndDrawType(@Param("yearMonth") String yearMonth, @Param("status") String status, @Param("drawType") String drawType);

}