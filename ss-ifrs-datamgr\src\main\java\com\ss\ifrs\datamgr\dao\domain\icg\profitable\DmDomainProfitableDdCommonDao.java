package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmDomainProfitableDdCommonDao {

    /*
     *QR002-非跟单IACF%,QR003-预期维持费用率%,BE013-预期赔付率%,QR008-非金融风险调整%
     */
    void deletePrepareQuota(DmIcgProcVo dmIcgProcVo);

    void prepareQuota(DmIcgProcVo dmIcgProcVo);

    /*
     *QP001:(预期赔付模式%)
     */
    void deletePrepareDevelopQuota(DmIcgProcVo dmIcgProcVo);

    void prepareDevelopQuota(DmIcgProcVo dmIcgProcVo);

    /*
     *loss_rate压力情景损失率
     */
    void prepareStressLossRate(DmIcgProcVo dmIcgProcVo);

    /*
     *D005 无风险曲线率
     */
    void prepareInterestRate(DmIcgProcVo dmIcgProcVo);

    void checkQuotaPaa(DmIcgProcVo dmIcgProcVo);
    void checkQuotaBba(DmIcgProcVo dmIcgProcVo);

    void checkStressLossRate(DmIcgProcVo dmIcgProcVo);

    void checkInterestRatePaa(DmIcgProcVo dmIcgProcVo);
    void checkInterestRateBba(DmIcgProcVo dmIcgProcVo);

}
