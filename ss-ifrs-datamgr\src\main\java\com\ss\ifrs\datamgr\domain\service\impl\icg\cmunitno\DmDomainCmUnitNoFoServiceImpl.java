package com.ss.ifrs.datamgr.domain.service.impl.icg.cmunitno;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.DmDomainIcgFacOutwardDao;
import com.ss.ifrs.datamgr.domain.service.icg.cmunitno.DmDomainCmUnitNoService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmUnitNoAdapterVo;
import com.ss.library.utils.StringUtil;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_FAC_OUT)
public class DmDomainCmUnitNoFoServiceImpl implements DmDomainCmUnitNoService {

    @Autowired
    BbsConfEntityFeignClient bbsConfEntityFeignClient;

    @Autowired
    DmDomainIcgFacOutwardDao dmDomainIcgFacOutwardDao;

    @Override
    public String getCmUnitNo(DmCmUnitNoAdapterVo cmUnitNoAdapterVo) {
        BbsConfEntityVo entityVo = bbsConfEntityFeignClient.findByEntityId(cmUnitNoAdapterVo.getEntityId());
        StringBuffer sb = new StringBuffer();
        sb.append(entityVo.getEntityCode());
        sb.append(DmConstant.BusinessModel.MODEL_FAC_OUT);
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment1st())) {
            sb.append(cmUnitNoAdapterVo.getSegment1st());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment2nd())) {
            sb.append(cmUnitNoAdapterVo.getSegment2nd());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment3rd())) {
            sb.append(cmUnitNoAdapterVo.getSegment3rd());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment4th())) {
            sb.append(cmUnitNoAdapterVo.getSegment4th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment5th())) {
            sb.append(cmUnitNoAdapterVo.getSegment5th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment6th())) {
            sb.append(cmUnitNoAdapterVo.getSegment6th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment7th())) {
            sb.append(cmUnitNoAdapterVo.getSegment7th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment8th())) {
            sb.append(cmUnitNoAdapterVo.getSegment8th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment9th())) {
            sb.append(cmUnitNoAdapterVo.getSegment9th());
        }
        Long cmunitNoSeqNo = dmDomainIcgFacOutwardDao.getCmunitNoSeqNo();
        sb.append(StringUtil.lpad(cmunitNoSeqNo.toString(), 11, "0"));
        return sb.toString();
    }

    @Override
    public String getCmUnitNoSql(DmCmUnitNoAdapterVo cmUnitNoAdapterVo) {
        BbsConfEntityVo entityVo = bbsConfEntityFeignClient.findByEntityId(cmUnitNoAdapterVo.getEntityId());
        StringBuffer sbSql = new StringBuffer();
        sbSql.append("'").append(entityVo.getEntityCode());
        sbSql.append(DmConstant.BusinessModel.MODEL_FAC_OUT).append("'");


        String segment1st = dmDomainIcgFacOutwardDao.getSegment1st();
        if (StringUtil.isNotEmpty(segment1st)) {
            sbSql.append(segment1st);
        }

        String segment2nd = dmDomainIcgFacOutwardDao.getSegment2nd();
        if (StringUtil.isNotEmpty(segment2nd)) {
            sbSql.append(segment2nd);
        }

        String segment3rd = dmDomainIcgFacOutwardDao.getSegment3rd();
        if (StringUtil.isNotEmpty(segment3rd)) {
            sbSql.append(segment3rd);
        }

        String segment4th = dmDomainIcgFacOutwardDao.getSegment4th();
        if (StringUtil.isNotEmpty(segment4th)) {
            sbSql.append(segment4th);
        }

        String segment5th = dmDomainIcgFacOutwardDao.getSegment5th();
        if (StringUtil.isNotEmpty(segment5th)) {
            sbSql.append(segment5th);
        }

        String segment6th = dmDomainIcgFacOutwardDao.getSegment6th();
        if (StringUtil.isNotEmpty(segment6th)) {
            sbSql.append(segment6th);
        }
        String segment7th = dmDomainIcgFacOutwardDao.getSegment7th();
        if (StringUtil.isNotEmpty(segment7th)) {
            sbSql.append(segment7th);
        }

        String segment8th = dmDomainIcgFacOutwardDao.getSegment8th();
        if (StringUtil.isNotEmpty(segment8th)) {
            sbSql.append(segment8th);
        }
        String segment9th = dmDomainIcgFacOutwardDao.getSegment9th();
        if (StringUtil.isNotEmpty(segment9th)) {
            sbSql.append(segment9th);
        }
        
        String cmunitNoSeqSql = dmDomainIcgFacOutwardDao.getCmunitNoSeqSql();
        return sbSql.append(cmunitNoSeqSql).toString();
    }
}
