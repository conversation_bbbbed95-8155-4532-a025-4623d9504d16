package com.ss.ifrs.datamgr.domain.service.impl.bussperiod;

import com.ss.ifrs.datamgr.domain.abst.DmDomainPeriod;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodDetailService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfBussPeriod;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service(CommonConstant.BussPeriod.PeriodStatus.PROCESSING)
public class DmDomainPeriodProcessingServiceImpl extends DmDomainPeriod implements DmDomainPeriodService {

    String state = CommonConstant.BussPeriod.PeriodStatus.PROCESSING;
    Long userId = 1L;

    @Autowired
    private DmDomainBussPeriodService domainBussPeriodService;

    @Autowired
    private DmDomainPeriodDetailService bussPeriodDetailService;

    @Override
    public void execution(Long entityId, String yearMonth) {

        /**
         *执行逻辑：
         * 1、检查是否有处理中的业务期间；
         * 2、如有：退出并提示
         * 3、如无：判断是全部明细都为已准备
         * 3.1、如非所有明细为已准备，退出并提示
         * 3.2、如所有明细为都为已准备，调整当前业务期间为处理中；
         * */

        // 检验是否存在处理中业务期间
        String checkYearMonth = domainBussPeriodService.getCurrentYearMonth(entityId);
        if (StringUtil.isNotEmpty(checkYearMonth)) {
            return;
            //throw new BusinessException(ResponseEnum.ERROR_PERIOD_NOT_PREPARED_TO_PROCESS);
        }

        // 检验是否存在已准备业务期间
        checkYearMonth = domainBussPeriodService.getPreparedYearMonth(entityId);
        if (ObjectUtils.isEmpty(checkYearMonth)) {
            return;
            //throw new BusinessException(ResponseEnum.ERROR_PERIOD_NOT_PREPARED_TO_PROCESS);
        }
        // 验证输入明细数据状态是否己准备
        if (bussPeriodDetailService.checkExistsPreparingInput(entityId, checkYearMonth)) {
            return;
            //throw new BusinessException(ResponseEnum.ERROR_PERIOD_DETAIL_INPUT_NOT_READY);
        }

        DmConfBussPeriod confBussPeriod = new DmConfBussPeriod();
        confBussPeriod.setEntityId(entityId);
        confBussPeriod.setYearMonth(checkYearMonth);
        confBussPeriod.setPeriodState(this.state);
        confBussPeriod.setUpdatorId(this.userId);
        this.update(confBussPeriod);
    }

    @Override
    public void executionDetail(Long entityId, String yearMonth, String bizCode) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setYearMonth(yearMonth);
        bussPeriodReqVo.setPeriodState(this.state);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.PREPARING);
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.OUTPUT);
        bussPeriodReqVo.setUpdatorId(this.userId);
        bussPeriodReqVo.setUpdateTime(new Date());
        bussPeriodDetailService.update(bussPeriodReqVo);
    }
}
