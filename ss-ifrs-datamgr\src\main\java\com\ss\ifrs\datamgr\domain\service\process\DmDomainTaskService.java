package com.ss.ifrs.datamgr.domain.service.process;

import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.BussActionStateVo;

public interface DmDomainTaskService {


    /**
     *  临时接入旧版日志状态
     * @param bussActionStateVo
     */
    void mergeDmProcessState(BussActionStateVo bussActionStateVo);

    void refreshBussActionState(BussActionStateVo bussActionStateVo);

    /**
     * 配置检查
     * @param dmConfBussPeriodVo
     */
    void configCheck(DmConfBussPeriodVo dmConfBussPeriodVo);


    /**
     *  综合查询获取流程节点信息
     */
    ActOverviewVo getActOverviewVoByObject(ActOverviewVo actOverviewVo);

}
