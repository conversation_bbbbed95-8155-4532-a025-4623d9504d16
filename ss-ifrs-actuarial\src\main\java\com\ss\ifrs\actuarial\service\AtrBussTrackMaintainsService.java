package com.ss.ifrs.actuarial.service;

import com.ss.platform.pojo.bms.track.vo.TrackMaintainsReqVo;
import com.ss.platform.pojo.bms.track.vo.TrackMaintainsVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

public interface AtrBussTrackMaintainsService {

    Page<TrackMaintainsReqVo> fuzzySearchPage(TrackMaintainsVo emailTempVo, Pageable pageParam) throws Exception;
}
