package com.ss.ifrs.datamgr.domain.service.impl.bussperiod;

import com.ss.ifrs.datamgr.domain.abst.DmDomainPeriod;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodDetailService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfBussPeriod;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service(CommonConstant.BussPeriod.PeriodStatus.PREPARED)
public class DmDomainPeriodPreparedServiceImpl extends DmDomainPeriod implements DmDomainPeriodService {

    String state = CommonConstant.BussPeriod.PeriodStatus.PREPARED;
    Long userId = 1L;

    @Autowired
    private DmDomainBussPeriodService domainBussPeriodService;

    @Autowired
    private DmDomainPeriodPreparingServiceImpl bussPeriodPreparingService;

    @Autowired
    private DmDomainPeriodProcessingServiceImpl bussPeriodProcessingService;

    @Autowired
    private DmDomainPeriodDetailService bussPeriodDetailService;

    @Override
    public void execution(Long entityId, String yearMonth) {
        /**
         *执行逻辑：
         * 1、判断是否所有明细为已准备
         * 1.1、如否，退出并提示
         * 1.2、如是：调整当前业务期间为已准备；
         * 2、增加下一个准备中业务期间；
         * 3、判断是否有处理中的业务期间，如无：调整当前业务期间为处理中
         * */

        // 验证所有输入数据明细状态是否己准备
        if (bussPeriodDetailService.checkExistsPreparingInput(entityId, yearMonth)) {
            return;
            //throw new BusinessException(ResponseEnum.ERROR_PERIOD_DETAIL_INPUT_NOT_READY);
        }

        DmConfBussPeriod confBussPeriod = new DmConfBussPeriod();
        confBussPeriod.setEntityId(entityId);
        confBussPeriod.setYearMonth(yearMonth);
        confBussPeriod.setPeriodState(this.state);
        confBussPeriod.setUpdatorId(this.userId);
        this.update(confBussPeriod);

        //增加下个准备中业务期间
        bussPeriodPreparingService.execution(entityId, null);

        // 检验是否存在处理中业务期间
        String checkYearMonth = domainBussPeriodService.getCurrentYearMonth(entityId);
        if (StringUtil.isEmpty(checkYearMonth)) {
            //修改当前业务期间状态为处理中
            bussPeriodProcessingService.execution(entityId, yearMonth);
        }
    }

    @Override
    public void executionDetail(Long entityId, String yearMonth, String bizCode) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setYearMonth(yearMonth);
        bussPeriodReqVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PREPARING);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.READIED);
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.INPUT);
        bussPeriodReqVo.setUpdatorId(this.userId);
        bussPeriodReqVo.setUpdateTime(new Date());
        bussPeriodDetailService.update(bussPeriodReqVo);

        //明细数据已准备，调整业务期间为已准备
        this.execution(entityId, yearMonth);
    }
}
