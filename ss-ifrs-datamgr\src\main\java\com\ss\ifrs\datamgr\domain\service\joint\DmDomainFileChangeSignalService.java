package com.ss.ifrs.datamgr.domain.service.joint;

import com.ss.ifrs.datamgr.pojo.joint.po.DmBussFileExchangeCtl;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelLogQueryVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelLogVo;
import com.ss.library.mybatis.model.Page;

import javax.servlet.http.HttpServletResponse;

public interface DmDomainFileChangeSignalService {

    void checkFileChangeTask();

    /**
     * 文件交换日志查询
     * @param vo
     * @param _pageNo
     * @param _pageSize
     * @return
     */
    Page<DmBussFileExchangeModelLogVo> enquiryLog(DmBussFileExchangeModelLogQueryVo vo, int _pageNo, int _pageSize);

    /**
     * 文件交换待处理日期查询
     * @return
     */
    DmBussFileExchangeCtl enquiryPendingBussDate();

    /**
     * 文件交换单模型数据校验
     * @param entityId
     * @param taskCode
     * @param bizTypeId
     * @param userId
     */
    void callVerify(Long entityId, String taskCode, long bizTypeId, Long userId);

    /**
     * 导出信号文件
     * @param response
     * @param signalId
     */
    void exportSignalBackFiles(HttpServletResponse response, Long signalId);

    /**
     * 导出模型文件
     * @param response
     * @param modelId
     */
    void exportModelBackFiles(HttpServletResponse response, Long modelId);
}
