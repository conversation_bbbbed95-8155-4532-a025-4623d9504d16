package com.ss.ifrs.datamgr.domain.service.impl.verify;

import com.ss.ifrs.datamgr.domain.service.verify.DmDomainVerifyService;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmBussVerifyCompareResultVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfVerifyRuleVo;
import com.ss.ifrs.datamgr.service.verify.DmVerifyCompareService;
import com.ss.ifrs.datamgr.service.verify.DmVerifyNormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

@Service
@Slf4j
public class DmDomainVerifyServiceImpl implements DmDomainVerifyService {

    @Autowired
    private DmVerifyNormService verifyNormService;

    @Autowired
    private DmVerifyCompareService verifyCompareService;

    @Resource
    private DataSource dataSource;


    @Override
    public void runVerify(String yearMonth) {
        //查询所有生效的核对指标
        List<DmConfVerifyRuleVo> ruleList = verifyNormService.queryValidNormRules();

        //根据yearMonth生成月初和月末时间
        String[] result = getMonthStartAndEnd(yearMonth);
        String beginDate ="TO_DATE('"+result[0]+"', 'YYYY-MM-DD')";
        String endDate = "TO_DATE('"+result[1]+"', 'YYYY-MM-DD')";

        for (DmConfVerifyRuleVo rule : ruleList) {
            //替换核对指标的时间条件
            rule.setRuleSql(rule.getRuleSql().replace("#YEAR_MONTH#", yearMonth));
            rule.setRuleSql(rule.getRuleSql().replace("#BEGIN_DATE#", beginDate));
            rule.setRuleSql(rule.getRuleSql().replace("#END_DATE#", endDate));

            //执行指标sql
            BigDecimal sqlResult = verifyNormService.executeNormSql(rule.getRuleSql());


            DmBussVerifyCompareResultVo dmBussVerifyCompareResultVo = verifyCompareService.queryByYearMonthAndRuleCode(yearMonth, rule.getRuleCode());
            if(null != dmBussVerifyCompareResultVo){
                //更新结果
                dmBussVerifyCompareResultVo.setQueryValue(sqlResult);
                verifyCompareService.updateDmBussCoreValue(dmBussVerifyCompareResultVo);
            }else{
                //新增结果
                dmBussVerifyCompareResultVo = new DmBussVerifyCompareResultVo();
                dmBussVerifyCompareResultVo.setRuleCode(rule.getRuleCode());
                dmBussVerifyCompareResultVo.setCoreValue(new BigDecimal("0"));
                dmBussVerifyCompareResultVo.setQueryValue(sqlResult);
                dmBussVerifyCompareResultVo.setYearMonth(yearMonth);
                verifyCompareService.addDmBussCoreValue(dmBussVerifyCompareResultVo);
            }

        }










    }

    public static String[] getMonthStartAndEnd(String yearMonth) throws DateTimeParseException {
        // 1. 定义日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMM");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 2. 解析输入字符串为 YearMonth 对象
        YearMonth ym = YearMonth.parse(yearMonth, inputFormatter);

        // 3. 获取月初日期（当月的第一天）
        String startOfMonth = ym.atDay(1).format(outputFormatter);

        // 4. 获取月末日期（当月的最后一天）
        String endOfMonth = ym.atEndOfMonth().format(outputFormatter);

        // 5. 返回结果
        return new String[]{startOfMonth, endOfMonth};
    }


    /**
     * 获取当前连接的数据库类型
     *
     * @return
     */
    private String getDatabaseProductName() {
        String databaseProductName;
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            databaseProductName = metaData.getDatabaseProductName();
            return databaseProductName;
        } catch (SQLException e) {
            e.printStackTrace();
            return "";
        }
    }
}
