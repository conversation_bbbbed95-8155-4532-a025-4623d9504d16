package com.ss.ifrs.datamgr.listener;

import com.ss.platform.core.listener.BaseAfterConfigListener;
import org.springframework.boot.context.config.ConfigFileApplicationListener;
import org.springframework.context.ApplicationEvent;

public class AfterConfigListener extends BaseAfterConfigListener {

    /**
     *  指定支持哪些类型的事件
     */
    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
        return super.supportsEventType(eventType);
    }

    /**
     *  指定支持发生事件所在的类型
     */
    @Override
    public boolean supportsSourceType(Class<?> aClass) {
        return super.supportsSourceType(aClass);
    }

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        super.onApplicationEvent(event);
    }

    @Override
    public int getOrder() {
        /* 设置该监听器 在加载配置文件之后执行 */
        return (ConfigFileApplicationListener.DEFAULT_ORDER + 1);
    }
}