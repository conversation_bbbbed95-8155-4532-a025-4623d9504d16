package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfAccountSetVo;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 现行账套Feign调用
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsConfAccountSetClient {

    @ApiOperation(value = "根据CODE查找数据")
    @RequestMapping(value = "/bbs_account_set/find_by_entityCode2/{entityCode}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BbsConfAccountSetVo findByEntityCode2(@PathVariable("entityCode") String entityCode);

    @ApiOperation(value = "根据业务单位ID查找数据")
    @RequestMapping(value = "/bbs_account_set/find_by_entityId/{entityId}", method = RequestMethod.GET)
    BaseResponse<BbsConfAccountSetVo> findByCenterId(@PathVariable("entityId") Long centerId);
}
