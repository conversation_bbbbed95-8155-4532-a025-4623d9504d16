package com.ss.ifrs.actuarial.util;

import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.library.constant.ExcelConstant;
import com.ss.platform.core.constant.LocalesConstanst;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/3/15
 * 生成excel文件
 */
public class AtrExcelGenerateUtils {

    private static XSSFWorkbook generateWorkbook(List<AtrExcelCellVo> dmExcelCellVo) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");
        XSSFCellStyle row1_style = createCellStyle(workbook, (short) 10, XSSFFont.COLOR_NORMAL, (short) 200, "宋体", HorizontalAlignment.CENTER);
        // 设置样式
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置样式
//        style.setFillForegroundColor(IndexedColors.SKY_BLUE.index);
//        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        style.setBorderBottom(BorderStyle.THIN);
//        style.setBorderLeft(BorderStyle.THIN);
//        style.setBorderRight(BorderStyle.THIN);
//        style.setBorderTop(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //换行
        //style.setWrapText(true);
        // 生成一种字体
        XSSFFont font = workbook.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 11);
        //设置字体加粗
        font.setBold(true);
        // 在样式中引用这种字体
        style.setFont(font);

        generateTableHeader(workbook, sheet, style, dmExcelCellVo);

        return workbook;
    }

    private static XSSFWorkbook generateWorkbook(XSSFWorkbook workbook, List<AtrExcelCellVo> dmExcelCellVo, Integer sheetNo) {
        if (ObjectUtils.isEmpty(workbook)) {
            workbook = new XSSFWorkbook();
        }
        XSSFSheet sheet = workbook.createSheet("Sheet" + sheetNo);
        // 设置样式
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置样式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //换行
        //style.setWrapText(true);
        // 生成一种字体
        XSSFFont font = workbook.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 11);
        //设置字体加粗
        font.setBold(true);
        // 在样式中引用这种字体
        style.setFont(font);

        generateTableHeader(workbook, sheet, style, dmExcelCellVo);

        return workbook;
    }

    public static void generateExcelWithHeader(HttpServletResponse response, List<List<AtrExcelCellVo>> atrExcelCellVoLists, String fileName, String filePath)  throws IOException {
        if (atrExcelCellVoLists.size() <= 0) {
            return;
        }
        XSSFWorkbook  workbook = null;
        for (int i = 0 ; i < atrExcelCellVoLists.size() ; i++) {
            workbook = generateWorkbook(workbook, atrExcelCellVoLists.get(i), i + 1);
        }

        // 获取输出流
        OutputStream os = response.getOutputStream();

        // 重置输出流
        response.reset();
        // 设定输出文件头
        response.setHeader("Content-disposition",
                "attachment; filename=" + new String(fileName.getBytes("GB2312"), "8859_1") + ".xls");
        // 定义输出类型
        response.setContentType("application/msexcel");
        workbook.write(os);
        os.close();
    }



/*    public static void generateExcelWithHeader(HttpServletResponse response, List<AtrExcelCellVo> dmExcelCellVo, String fileName) throws IOException {
        if (dmExcelCellVo.size() <= 0) {
            return;
        }

        XSSFWorkbook workbook = generateWorkbook(dmExcelCellVo);

        // 获取输出流
        OutputStream os = response.getOutputStream();
        // 重置输出流
        response.reset();
        // 设定输出文件头
        response.setHeader("Content-disposition",
                "attachment; filename=" + new String(fileName.getBytes("GB2312"), "8859_1") + ".xlsx");
        // 定义输出类型
        response.setContentType("application/msexcel");

        workbook.write(os);

        os.close();
    }*/

    public static void generateExcelWithHeader(HttpServletRequest request, HttpServletResponse response, List<AtrExcelCellVo> dmExcelCellVo, String fileName, String filePath) throws IOException {
        if (CollectionUtils.isEmpty(dmExcelCellVo)) {
            throw new NoSuchElementException("Generate excel fail : not find template data");
        }

        XSSFWorkbook workbook = generateWorkbook(dmExcelCellVo);

        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        FileOutputStream fos = new FileOutputStream(filePath + "//" + fileName + ".xlsx");

        workbook.write(response.getOutputStream());

    }

    /**
     * 生成颜色
     *
     * @param workbook
     * @param xssfColor
     * @return
     */
    private static XSSFFont colorFont(XSSFWorkbook workbook, XSSFColor xssfColor) {
        XSSFFont font = workbook.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 11);
        //设置字体加粗
        font.setBold(true);
        //设置颜色
        font.setColor(xssfColor);
        return font;
    }


    /**
     * 生成表格头
     *
     * @param workbook
     * @param sheet
     * @param cellStyle
     * @param cell
     */
    private static void generateTableHeader(XSSFWorkbook workbook, XSSFSheet sheet, XSSFCellStyle cellStyle, List<AtrExcelCellVo> cell) {
        XSSFFont font = cellStyle.getFont();
        XSSFFont redFont = colorFont(workbook, new XSSFColor(IndexedColors.RED, null));

        //创建绘图对象
        XSSFDrawing patriarch = sheet.createDrawingPatriarch();

        for (int i = 0; i < cell.size(); i++) {
            XSSFCellStyle cellStyleFormat = workbook.createCellStyle();
            AtrExcelCellVo dmExcelCellVo = cell.get(i);
            dmExcelCellVo.setHssfCellStyle(cellStyle);
            XSSFRow xssfRow = sheet.getRow(dmExcelCellVo.getRow());

            if (ObjectUtils.isEmpty(xssfRow)) {
                xssfRow = sheet.createRow(dmExcelCellVo.getRow());
            }

            sheet.setDefaultColumnStyle(dmExcelCellVo.getColumn(), cellStyleFormat);
            XSSFCell xssfCell = xssfRow.getCell(dmExcelCellVo.getColumn());
            if (xssfCell == null) {
                xssfCell = xssfRow.createCell(dmExcelCellVo.getColumn());
                xssfCell.setCellType(CellType.NUMERIC);
            }

            //长度
            String colLength = dmExcelCellVo.getColLength();
            //语言
            String language = dmExcelCellVo.getLanguage();

            //设置当前列的单元格格式
            XSSFDataFormat format = workbook.createDataFormat();
            String columnCellType = dmExcelCellVo.getColumnCellType();
            StringBuffer commentBuffer = new StringBuffer();

            if (ExcelConstant.ExcelCellType.STRING.equals(columnCellType)) {
                cellStyleFormat.setDataFormat(format.getFormat("@"));
                //增加批注
                if(LocalesConstanst.Language.ZH.equals(language)){
                    commentBuffer.append("文本长度("+colLength+")\n");
                }else if (LocalesConstanst.Language.TN.equals(language)){
                    commentBuffer.append("文本長度("+colLength+")\n");
                }else {
                    commentBuffer.append("Text Length("+colLength+")\n");
                }

            } else if (ExcelConstant.ExcelCellType.INTEGER.equals(columnCellType)) {
                //cellStyleFormat.setDataFormat(format.getFormat("0"));
                //增加批注
                if(LocalesConstanst.Language.ZH.equals(language)){
                    commentBuffer.append( "数值长度("+colLength+")\n");
                }else if (LocalesConstanst.Language.TN.equals(language)){
                    commentBuffer.append( "數值長度("+colLength+")\n");
                }else {
                    commentBuffer.append( "Number Length("+colLength+")\n");
                }
            } else if (ExcelConstant.ExcelCellType.DOUBLE.equals(columnCellType)) {
                StringBuffer stringBuffer = new StringBuffer("0.");
                String[] split = colLength.split(",");
                if(split.length > 1){
                    Integer precisionLength = Integer.valueOf(split[1]);
                    for (int j = 0; j < precisionLength; j++) {
                        stringBuffer.append("0");
                    }
                }
                //cellStyleFormat.setDataFormat(format.getFormat(stringBuffer.toString()));
                //增加批注
                if(LocalesConstanst.Language.ZH.equals(language)){
                    commentBuffer.append( "数值长度("+colLength+")\n");
                }else if (LocalesConstanst.Language.TN.equals(language)){
                    commentBuffer.append( "數值長度("+colLength+")\n");
                }else {
                    commentBuffer.append( "Number Length("+colLength+")\n");
                }
            } else if (ExcelConstant.ExcelCellType.DATE.equals(columnCellType)) {
                cellStyleFormat.setDataFormat(format.getFormat("m/d/yy"));
                //增加批注
                if(LocalesConstanst.Language.ZH.equals(language)){
                    commentBuffer.append("日期格式('yyyy/MM/DD')\n");
                }else if (LocalesConstanst.Language.TN.equals(language)){
                    commentBuffer.append("日期格式('yyyy/MM/DD')\n");
                }else {
                    commentBuffer.append("Date Format('yyyy/MM/DD')\n");
                }
            }

            //合并单元格
            if (dmExcelCellVo.isMergerCell()) {
                for (int j = dmExcelCellVo.getMergerRowStart(); j <= dmExcelCellVo.getMergerRowEnd(); j++) {
                    for (int k = dmExcelCellVo.getMergerColumnStart(); k <= dmExcelCellVo.getMergerColumnEnd(); k++) {
                        XSSFRow tempRow = sheet.getRow(j);
                        if (tempRow == null) {
                            tempRow = sheet.createRow(j);
                        }
                        XSSFCell tempCell = tempRow.getCell(k);
                        if (tempCell == null) {
                            tempCell = tempRow.createCell(k);
                        }
                        tempCell.setCellStyle(dmExcelCellVo.getHssfCellStyle());
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(dmExcelCellVo.getMergerRowStart(), dmExcelCellVo.getMergerRowEnd(), dmExcelCellVo.getMergerColumnStart(), dmExcelCellVo.getMergerColumnEnd()));
            }

            //设置单元格的值
            Object cellValue = dmExcelCellVo.getValue();
            if (cellValue instanceof Boolean) {
                xssfCell.setCellValue((Boolean) cellValue);
            } else if (cellValue instanceof Date) {
                xssfCell.setCellValue((Date) cellValue);
            } else if (cellValue instanceof String) {
                xssfCell.setCellValue((String) cellValue);
                String content = (String) cellValue;
                XSSFRichTextString xts = new XSSFRichTextString(content);
                int index = content.indexOf("*");
                if (index != -1) {
                    xts.applyFont(index, index + 1, redFont);
                    xts.applyFont(index + 1, content.length(), font);
                    /*String string = xts.getString();*/
                    xssfCell.setCellValue(xts);
                } else {
                    xssfCell.setCellValue((String) cellValue);
                }
            } else if (cellValue instanceof Double) {
                xssfCell.setCellValue((Double) cellValue);
            } else if (cellValue instanceof Calendar) {
                xssfCell.setCellValue((Calendar) cellValue);
            } else if (cellValue instanceof RichTextString) {
                xssfCell.setCellValue((RichTextString) cellValue);
            }

            //设置单元格的样式
            if (ObjectUtils.isNotEmpty(dmExcelCellVo.getHssfCellStyle())) {
                xssfCell.setCellStyle(dmExcelCellVo.getHssfCellStyle());
            }
            //设置批注
            commentBuffer.append("\n");
            if(StringUtils.isNotEmpty(dmExcelCellVo.getComment())){
                commentBuffer.append(dmExcelCellVo.getComment());
            }
            xssfCell.setCellComment(createComment(patriarch,xssfCell,commentBuffer.toString()));

            // 根据字段长度自动调整列的宽度
            //   sheet.autoSizeColumn(i, true);
            // 根据字段长度自动调整列的宽度
                //根据表头的字节长度*256
                String cellValueStr = (String) cellValue;
                if (StringUtils.isNotEmpty(cellValueStr)){
                    sheet.setColumnWidth(i,getStrLength(cellValueStr)*256);
                }
        }
    }

    /**
     * 获取正确字符的长度
     * @param str
     * @return
     */
    private static int getStrLength(String str){
        if(StringUtils.isEmpty(str)){
            return 0;
        }
        //中文匹配
        Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]");
        int lengthPtn = 0;
        int lengthNOtPtn = 0;
        char[] array = str.toCharArray();

        for (int i = 0; i < array.length; i++) {
            Matcher matcher = pattern.matcher(String.valueOf(array[i]));
            if (matcher.matches()){
                lengthPtn++;
            }
        }
        lengthNOtPtn = array.length-lengthPtn;
        return lengthPtn*3+lengthNOtPtn*2;
    }

    /**
     *   生成批注对象，XSSFClientAnchor定义批注大小
     */
    public static XSSFComment createComment ( XSSFDrawing patriarch,XSSFCell xssfCell ,String commentValue){
        XSSFComment comment = patriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) xssfCell.getColumnIndex(), xssfCell.getRowIndex(), (short) xssfCell.getColumnIndex() + 5, xssfCell.getRowIndex() + 6));
        comment.setString(commentValue);
        return comment;
    }


    /**
     * 生成样式
     *
     * @param workbook
     * @param fontHeightInPoints
     * @param color
     * @param fontHeight
     * @param fontName
     * @param align
     * @return
     */
    private static XSSFCellStyle createCellStyle(XSSFWorkbook workbook, short fontHeightInPoints, short color, short fontHeight, String fontName, HorizontalAlignment align) {
        XSSFFont font = workbook.createFont();
        // 表头字体大小
        if (fontHeightInPoints != 0) {
            //font.setFontHeightInPoints((short) 6);
            font.setFontHeightInPoints(fontHeightInPoints);
        }

        //字体颜色
        if (color != 0) {
            //font.setColor(XSSFFont.COLOR_NORMAL);
            font.setColor(color);
        }

        if (fontHeight != 0) {
            // font.setFontHeight((short) 200);
            font.setFontHeight(fontHeight);
        }

        // 表头字体名称
        //font.setFontName("宋体");
        if (StringUtils.isNotEmpty(fontName)) {
            font.setFontName(fontName);
        }

        XSSFCellStyle cellStyle = workbook.createCellStyle();
        if (ObjectUtils.isNotEmpty(align)) {
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
        }

        cellStyle.setFont(font);
        return cellStyle;
    }
}
