package com.ss.ifrs.datamgr.domain.service.impl.joint;

import com.ss.ifrs.datamgr.conf.AppFilePathConfig;
import com.ss.ifrs.datamgr.dao.conf.DmConfBussPeriodDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableDao;
import com.ss.ifrs.datamgr.dao.joint.DmBussFileExchangeCtlDao;
import com.ss.ifrs.datamgr.dao.joint.DmBussFileExchangeModelDao;
import com.ss.ifrs.datamgr.dao.joint.DmBussFileExchangeSignalDao;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainDataVerifyService;
import com.ss.ifrs.datamgr.domain.service.joint.DmDomainFileChangeSignalService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.feign.BmsMessageFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.joint.po.DmBussFileExchangeCtl;
import com.ss.ifrs.datamgr.pojo.joint.po.DmBussFileExchangeModel;
import com.ss.ifrs.datamgr.pojo.joint.po.DmBussFileExchangeSignal;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelLogQueryVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelLogVo;
import com.ss.ifrs.datamgr.util.FilePathUtils;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.FilterUtil;
import com.ss.library.utils.HttpResponseUtil;
import com.ss.library.utils.StringUtil;
import com.ss.library.utils.ZipUtil;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class DmDomainFileChangeSignalServiceImpl implements DmDomainFileChangeSignalService {
    @Resource
    private DmBussFileExchangeCtlDao dmBussFileExchangeCtlDao;

    @Resource
    private DmBussFileExchangeModelDao dmBussFileExchangeModelDao;

    @Resource
    private DmBussFileExchangeSignalDao dmBussFileExchangeSignalDao;

    @Resource
    private BmsMessageFeignClient bmsMessageFeignClient;

    @Resource
    private DmConfBussPeriodDao dmConfBussPeriodDao;

    @Resource
    private BbsConfEntityFeignClient bbsConfEntityFeignClient;

    @Resource
    private AppFilePathConfig appFilePathConfig;

    @Autowired
    DmDomainDataVerifyService dmDomainDataVerifyService;

    @Resource
    private DmConfTableDao dmConfTableDao;

    @Override
    public void checkFileChangeTask() {
        DmBussFileExchangeCtl dmBussFileExchangeCtl = dmBussFileExchangeCtlDao.queryLastFileExchangeCtl();
        if(dmBussFileExchangeCtl.getStatus().equals("0")&&null == dmBussFileExchangeCtl.getErrorMsg()){
            return;
        }
        String errMessage = null;
        if(dmBussFileExchangeCtl.getErrorMsg().equals("incomplete")){
            DmBussFileExchangeSignal lastSignal = dmBussFileExchangeSignalDao.queryLastSignalByBussDateAndStatus(dmBussFileExchangeCtl.getBussDate(), "3");
            errMessage = lastSignal.getErrorMsg()+",task_code:"+lastSignal.getTaskCode();
        }else{
            errMessage = dmBussFileExchangeCtl.getErrorMsg();
        }
        errMessage = errMessage.replace("'"," ");
        String sql = "select '"+ DateFormatUtils.format(dmBussFileExchangeCtl.getBussDate(), "yyyyMMdd")+"' as 交换日期,'"+errMessage+"' as 异常信息";
        bmsMessageFeignClient.fileChangeNotice(sql);
    }

    @Override
    public Page<DmBussFileExchangeModelLogVo> enquiryLog(DmBussFileExchangeModelLogQueryVo vo, int _pageNo, int _pageSize) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        // 模糊查询把*替换成%, 如查H开头的编号，H*,
        vo.setTaskCode(FilterUtil.transitionSearch(vo.getTaskCode()));
        vo.setBizCode(FilterUtil.transitionSearch(vo.getBizCode()));
        vo.setYearMonth(FilterUtil.transitionSearch(vo.getYearMonth()));
        return dmBussFileExchangeModelDao.queryLog(vo, pageParam);
    }

    @Override
    public DmBussFileExchangeCtl enquiryPendingBussDate() {
        List<DmConfBussPeriodVo> periods = dmConfBussPeriodDao.findBussPeriodVoForJob(1L, "0");
        DmConfBussPeriodVo period = periods.stream()
                .min(Comparator.comparing(DmConfBussPeriodVo::getYearMonth))
                .orElse(null);
        if (period == null) {
            throw new RuntimeException("Business periods are not configured\n");
        }
        DmBussFileExchangeCtl ctl = dmBussFileExchangeCtlDao.queryPendingBussDate(period.getYearMonth());
        if (ctl == null) {
            ctl = new DmBussFileExchangeCtl();
            ctl.setYearMonth(period.getYearMonth());
        }
        return ctl;
    }

    @Override
    public void callVerify(Long entityId, String taskCode, long bizTypeId, Long userId) {
        String entityCode = taskCode.substring(3, 7);
        BbsConfEntityVo entityInfo = bbsConfEntityFeignClient.findByEntityCode(entityCode);
        entityId = (null == entityInfo ? 1L : entityInfo.getEntityId());
        userId = (userId == null ? 1L : userId);

        DmConfTableVo tableInfo = dmConfTableDao.findVoById(bizTypeId);
        DmBussDataVerifyVo dmBussDataVerifyVo = new DmBussDataVerifyVo();
        dmBussDataVerifyVo.setEntityId(entityId);
        dmBussDataVerifyVo.setTaskCode(taskCode);
        dmBussDataVerifyVo.setPushModel(tableInfo.getBizCode());
        dmBussDataVerifyVo.setUserId(userId);
        dmBussDataVerifyVo.setDrawType("1");
        dmBussDataVerifyVo.setBizTypeId(bizTypeId);
        dmBussDataVerifyVo.setBizCode(tableInfo.getBizCode());
        if (StringUtil.isNotEmpty(taskCode)) {
            dmBussDataVerifyVo.setYearMonth(taskCode.substring(7, 13));
        }
        dmDomainDataVerifyService.dataVerify(dmBussDataVerifyVo);
    }

    /**
     * 输出信号级别的回写文件
     */
    @Override
    public void exportSignalBackFiles(HttpServletResponse response, Long signalId) {
        DmBussFileExchangeSignal signal = dmBussFileExchangeSignalDao.findById(signalId);

        DmBussFileExchangeModel modelQ = new DmBussFileExchangeModel();
        modelQ.setSignalId(signalId);
        List<DmBussFileExchangeModel> models = dmBussFileExchangeModelDao.findList(modelQ);

        List<String> srcFileNames = new ArrayList<>();
        srcFileNames.add(signal.getSignalFileName());
        models.forEach(t -> srcFileNames.add(t.getFileName()));

        List<String> tgtFileNames = new ArrayList<>();
        for (String srcFileName : srcFileNames) {
            tgtFileNames.add(srcFileName + ".success");
            tgtFileNames.add(srcFileName + ".fail");
        }

        String tgtDir = getTgtDir(srcFileNames.get(0),signal.getBussDate());
        List<File> files = new ArrayList<>();
        for (String fileName : tgtFileNames) {
            String path = StringUtil.joinPaths(tgtDir, fileName);
            File file = new File(path);
            if (file.exists()) {
                files.add(file);
            }
        }

        byte[] bytes = ZipUtil.zipFiles(files.toArray(new File[0]));
        HttpResponseUtil.zip(response, signal.getTaskCode() + ".zip", bytes);
    }

    /**
     * 输出模型级别的回写文件
     */
    @Override
    public void exportModelBackFiles(HttpServletResponse response, Long modelId) {
        DmBussFileExchangeModel model = dmBussFileExchangeModelDao.findById(modelId);
        Long signalId = model.getSignalId();
        DmBussFileExchangeSignal signal = dmBussFileExchangeSignalDao.findById(signalId);
        String fileName = model.getFileName() + ".fail";
        String tgtDir = getTgtDir(fileName,signal.getBussDate());
        String path = StringUtil.joinPaths(tgtDir, fileName);
        File file = new File(path);
        if (file.exists()) {
            try {
                HttpResponseUtil.txt(response, fileName, FileUtils.readFileToByteArray(file));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            HttpResponseUtil.txt(response, fileName, "".getBytes(StandardCharsets.UTF_8));
        }
    }


    private String getTgtDir(String fileName,Date bussDate) {
        String privatePath = StringUtil.joinPaths(FilePathUtils.getBasePath(appFilePathConfig),
                FilePathUtils.getDataFilePathTextImportPath(appFilePathConfig) + FilePathUtils.getBussPathFileExchangePathPrivate(appFilePathConfig));
        String bussDateStr = DateFormatUtils.format(bussDate, "yyyyMMdd");
        String yearPath = bussDateStr.substring(0, 4);
        String parentDirName = bussDateStr.substring(0, 6);
        String batchNo = getBatchNo(fileName);
        //xxxx/yyyy/yyyyMM/001/
        String newFilePath = joinPaths(privatePath, yearPath, parentDirName, batchNo, fileName);

        if (StringUtils.isBlank(newFilePath)
                || !new File(newFilePath).isDirectory()
                || !new File(newFilePath).canWrite()
                || !new File(newFilePath).canRead()) {
            throw new RuntimeException("文件交换根目录(private)没有正常配置");
        }

        return newFilePath;
    }

    //字符串拼接
    private String joinPaths(String... paths) {
        return StringUtil.joinPaths(paths);
    }

    private String getBatchNo(String fileName){
        int lastIndex = fileName.indexOf('.');
        return fileName.substring(lastIndex - 3, lastIndex);
    }
}
