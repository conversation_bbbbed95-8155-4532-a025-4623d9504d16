/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-09-28 11:49:09
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.datamgr.dao.icg;


import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitDirect;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitFacOutwards;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitFacOutwardsVo;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectSelectVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmunitMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitDirectMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitTreatyMajorTestVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  MyBatis Generator(v1.2.12).
 * Create Date: 2021-09-28 11:49:09<br/>
 * Description: 临分分出计量单元 Dao类<br/>
 * Related Table Name: dm_buss_cmunit_fac_outwards<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmBussCmunitFacOutwardsDao extends IDao<DmBussCmunitFacOutwards, Long> {

    Page<DmBussCmunitDirectSelectVo> findDataRecord(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo, Pageable pageParam);

    DmBussCmunitFacOutwards findCmunitVoByqueryMethod(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo);

    String findDmOwActionLog(DmBussCmunitFacOutwardsVo dmBussCmunitFacOutwardsVo);

    List<String> findPolicyList(DmDataSearchVo dmDataSearchVo);

    Page<DmBussCmunitFacOutwardsVo> findDmBussCmunit(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmBussCmunitFacOutwardsVo> findDmBussCmunitProt(DmDataSearchVo searchVo, Pageable pageParam);

    /**
     * 计量单元划分
     */
    void insertCmunitIdentify(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateCmunitOtherField(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);


    void updateCmunitAdapterLoaSuccess(DmIcgProcVo dmIcgProcVo);

    void updateCmunitAdapterWithoutCode(DmIcgProcVo dmIcgProcVo,SliceAttributes sliceAttributes);


    void updateCmunitAdapterWithCode(DmIcgProcVo dmIcgProcVo,SliceAttributes sliceAttributes);

    void updateCmunitAdapterLoaFail(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);


    /**
     * 重大风险测试
     */
    void updateMajorRisk(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 重大风险测试 全部通过
     */
    void updateMajorRiskAllPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 重大风险测试配置+规则方案,查不到配置或配置为否默认全部通过
     */
    void updateMajorRiskNoConfPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 重大风险测试不通过的处理方案
     */

    void updateMajorRiskBussNoPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    Integer getMajorRiskBussNoPassCount(DmIcgProcVo dmIcgProcVo);

    void updateMajorNoPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 评估方法适配
     */
    void updateEvaluateApproach(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateEvaluateApproachFail(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 合同组合划分
     */
    void updatePortfolioNo(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 盈亏判定
     */
    void updateProfitableEstimate(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    Integer queryNeedProfitableEstimateData(DmIcgProcVo dmIcgProcVo);

    void updateProfitableEstimateInit(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateProfitableEstimateAdapterConfig(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 合同确认
     */
    void updateIcgBorderSeal(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD1(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD2(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD3(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD4(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD5(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD6(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 合同组划分
     */
    void updateIcgNo(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 投成拆分
     */
    void updateIcgInvestment(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同分组完成
     */
    void updateIcgYearMonth(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 获取关联配置的计量单元数据
     * @param entityId
     * @return
     */
    List<DmBussCmunitFacOutwards> findBussCmunitConf(Long entityId);


    String getCmunitInfoById(DmCmunitMajorTestVo dmCmunitMajorTestVo);

    DmCmunitMajorTestVo getDmCmunitMajorTestVo(Long cmunitId);

    Long getCmunitMajorTestCount(DmIcgProcVo dmIcgProcVo);

    List<DmCmunitDirectMajorTestVo> listCmunitFacMajorTestInfo(DmIcgProcVo dmIcgProcVo);

    void updateMajorResultByCmunitId(@Param("item") DmCmunitDirectMajorTestVo item);

    void updateMajorResultNull(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateMajorRiskAdapterConfig(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);
}