package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AtrBussReserveIbnrService {

    Page<AtrBussReserveIbnrVo> searchPage(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Pageable pageParam);

    AtrBussReserveIbnrVo findById(Long reserveIbnrId);

    void save(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Long userId);

    List<AtrBussReserveIbnrVo> findList(AtrBussReserveIbnrVo atrBussReserveIbnrVo);

    void delete(AtrBussReserveIbnrVo atrBussReserveIbnrVo);

    void confirm(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Long userId);

    void excelImport(MultipartFile file, AtrBussReserveIbnrVo ibnrVo, Long userId) throws Exception ;

    Long saveByImport(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Long userId);

    void saveDetail(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo);

    /**
     * @Method generateExcel
     * <AUTHOR>
     * @Date 2022/8/23
     * @Description 生成Excel
     * @Return
     */
    void excelExport(HttpServletResponse response, AtrBussReserveIbnrVo atrBussReserveIbnrVo) throws Exception;

    void export(HttpServletRequest request, HttpServletResponse response, AtrBussReserveIbnrVo atrBussReserveIbnrVo) throws Exception;

    Page<AtrBussReserveIbnrDetailVo> findPortfolioData(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Pageable pageParam);
}
