/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-09-28 11:37:38
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.datamgr.dao.icg;

import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreaty;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectSelectVo;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitTreatyVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *  MyBatis Generator(v1.2.12).
 * Create Date: 2021-09-28 11:37:38<br/>
 * Description: 再保合约计量单元 Dao类<br/>
 * Related Table Name: dm_buss_cmunit_treaty<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmBussCmunitTreatyDao extends IDao<DmBussCmunitTreaty, Long> {

    Page<DmBussCmunitDirectSelectVo> findDataRecord(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo, Pageable pageParam);

    DmBussCmunitTreaty findCmunitVoByqueryMethod(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo);

    String findDmOwActionLog(DmBussCmunitTreatyVo dmBussCmunitTreatyVo);

    List<String> findPolicyList(DmDataSearchVo dmDataSearchVo);

    List<DmBussCmunitTreatyVo> findDmBussCmunit(DmDataSearchVo searchVo);

    Page<DmBussCmunitTreatyVo> findDmBussCmunit(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmBussCmunitTreatyVo> findDmBussCmunitProt(DmDataSearchVo searchVo, Pageable pageParam);
}