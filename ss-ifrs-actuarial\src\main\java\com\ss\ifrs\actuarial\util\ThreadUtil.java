package com.ss.ifrs.actuarial.util;

import com.ss.library.utils.SpringContextUtil;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 多线程工具类
 * <AUTHOR>
 */
public class ThreadUtil {

    /**
     * 并发执行 job
     * @param job 自定义的 job 。  将传进总线程数、 线程序号 (base 0)
     * @param threadsSupplier 获取总线程数的函数。 可为空。 如果无法获取到线程总数， 将以 8 个线程执行。
     * @return 线程执行发生的异常
     */
    public static List<Exception> runThreads(BiConsumer<Integer, Integer> job, Supplier<Integer> threadsSupplier) {
        int threads = getThreads(threadsSupplier);
        return doFuture(threads, (threadNo) -> job.accept(threads, threadNo));
    }

    /**
     * 并发执行 job
     * @param data 要处理的数据集合
     * @param job 自定义的 job 。  将逐条传进要处理的数据
     * @param threadsSupplier 获取总线程数的函数。 可为空。 如果无法获取到线程总数， 将以 8 个线程执行。
     * @return 线程执行发生的异常
     */
    public static <T> List<Exception> runThreads(List<T> data, Consumer<T> job, Supplier<Integer> threadsSupplier) {
        return runThreads(data, (t, i) -> job.accept(t), threadsSupplier);
    }

    public static <T> List<Exception> runThreads(List<T> data, BiConsumer<T, Integer> job, Supplier<Integer> threadsSupplier) {
        if (data == null || data.isEmpty()) {
            return Collections.emptyList();
        }
        int threads = getThreads(threadsSupplier);
        AtomicInteger index = new AtomicInteger(0);
        return doFuture(threads, (threadNo) -> {
            while (true) {
                int k = index.getAndIncrement();
                if (k >= data.size()) {
                    break;
                }
                job.accept(data.get(k), threadNo);
            }
        });
    }

    public static <T> void runThreadsThrow(List<T> data, Consumer<T> job, Supplier<Integer> threadsSupplier) {
        List<Exception> exs = runThreads(data, job, threadsSupplier);
        if (!exs.isEmpty()) {
            throw new RuntimeException(exs.get(0));
        }
    }

    public static <T> void runThreadsThrow(List<T> data, BiConsumer<T, Integer> job, Supplier<Integer> threadsSupplier) {
        List<Exception> exs = runThreads(data, job, threadsSupplier);
        if (!exs.isEmpty()) {
            throw new RuntimeException(exs.get(0));
        }
    }

    private static List<Exception> doFuture(int threads, Consumer<Integer> r) {
        List<Exception> exs = new ArrayList<>();
        ExecutorService es = Executors.newFixedThreadPool(threads);
        List<Future<?>> futures = new ArrayList<>();
        for (int i = 0; i < threads; i++) {
            int threadNo = i;
            futures.add(es.submit(() -> r.accept(threadNo)));
        }
        es.shutdown();
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                exs.add(e);
            }
        }
        return exs;
    }

    private static int getThreads(Supplier<Integer> threadsSupplier) {
        int threads = 0;
        if (threadsSupplier == null) {
            threadsSupplier = () ->
                    SpringContextUtil.getBean(JdbcTemplate.class)
                            .queryForObject("select * from t_threads", Integer.class);
        }
        try {
            threads = threadsSupplier.get();
        } catch (Exception ignored) {
        }
        return threads <= 0 ? 8 : threads;
    }

}
