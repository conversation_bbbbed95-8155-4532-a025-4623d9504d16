/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 10:06:14
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;

import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfQuota;
import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfQuotaDetail;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaDetailVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 10:06:14<br/>
 * Description: null Dao类<br/>
 * Related Table Name: bbs_conf_quota_detail<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 * <AUTHOR>
 */
@Mapper
public interface DmConfQuotaDetailDao extends IDao<DmConfQuotaDetail, Long> {
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 根据业务单位ID和风险代码查找指标明细配置对象信息
     * @Return
     */
    DmConfQuotaDetail findByCenterAndRisk(DmConfQuotaDetailVo detailVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 修改指标明细配置信息
     * @Return
     */
    void updateByPo(DmConfQuotaDetail detail);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 根据指标Id查找指标明细配置信息列表
     * @Return
     */
    List<DmConfQuotaDetailVo> findListByQuotaId(Long quotaId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 根据指标Id删除指标明细配置信息
     * @Return
     */
    void deleteByQuotaId(Long quotaId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/24
     * @Description 根据指标Id删除指标明细配置信息
     * @Return
     */
    DmConfQuotaDetailVo findQuotaIdAndPeriod(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Description 根据指标Id查找指标明细配置信息列表
     * @Return List
     */
    List<DmConfQuotaDetail> findListByQuotaVo(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByDimensionVo(DmConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByLoaDimension(DmConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByOtherModel(DmConfQuota bbsConfQuota);

    /**
     * @Date 2023/3/23
     * @Description 查找发展期数据
     */
    List<DmConfQuotaDetail> findConfQuotaPeriodValue(DmConfQuotaVo bbsConfQuotaVo);

    void loadPrePeriodQuotaDetail(DmConfQuotaVo bbsConfQuota);

    void saveQuotaDetailList(@Param("mainId") Long ibnrMainId, @Param("list") List<DmConfQuotaDetail> dmConfQuotaDetailList);


    void deleteLoadQuotaDetail(DmConfQuotaVo dmConfQuotaVo);
}