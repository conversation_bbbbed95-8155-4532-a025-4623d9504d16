package com.ss.ifrs.datamgr.domain.service.impl.icg;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfContractRiskDefDao;
import com.ss.ifrs.datamgr.dao.dap.rein.DmReinsTreatyDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainIcg;
import com.ss.ifrs.datamgr.domain.exception.DmCmunitException;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgService;
import com.ss.ifrs.datamgr.domain.service.icg.cmunitno.DmDomainCmUnitNoService;
import com.ss.ifrs.datamgr.domain.service.icg.majorrisk.DmDomainMajorRiskService;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreatyInward;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreatyOutward;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRiskDefVo;
import com.ss.ifrs.datamgr.pojo.dap.po.rein.DmReinsTreaty;
import com.ss.ifrs.datamgr.pojo.dap.vo.rein.DmReinsTreatyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmUnitNoAdapterVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCreateCmUnitNoVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitTreatyMajorTestVo;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitTreatyOutwardService;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.pojo.com.po.SliceAttributes;
import com.ss.platform.util.DataSliceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service(DmConstant.BusinessModel.MODEL_TREATY_OUT + "ICG")
@Slf4j
public class DmDomainIcgTreatyOutwardServiceImpl extends DmDomainIcg implements DmDomainIcgService {

    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_OUT)
    @Autowired
    private DmDomainCmUnitNoService domainCmUnitNoService;

    @Autowired
    private AppConfig appConfig;
    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_OUT + "MajorTest")
    @Autowired
    private DmDomainMajorRiskService dmDomainMajorRiskService;
    @Autowired
    private DmDomainBussPeriodService dmDomainBussPeriodService;

    @Autowired
    private DmBussCmunitTreatyOutwardService dmBussCmunitTreatyOutwardService;

    @Qualifier(CommonConstant.BussPeriod.PeriodStatus.COMPLETED)
    @Autowired
    private DmDomainPeriodService dmDomainPeriodService;
    @Autowired
    private DmReinsTreatyDao dmReinsTreatyDao;

    @Autowired
    private DmConfContractRiskDefDao dmConfContractRiskDefDao;

    @Autowired
    private ExecutorService ruleExecutorService; // 替换为 Spring 管理的线程池

    @Override
    public void cmunitIdentify(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //获取长短险标识生成是否开启 0-不开启
        this.getShortRiskFlag(dmIcgProcVo);
        dmIcgProcVo.setEndDate(addMonth(dmIcgProcVo.getYearMonth()));
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE));
        //生成计量单元
        dmBussCmunitTreatyOutwardService.cmunitIdentify(dmIcgProcVo);
        //修改已生成计量单元标识
        dmBussCmunitTreatyOutwardService.updateOldCmunitidentify(dmIcgProcVo);
//        //获取计量单元No
//        DmCreateCmUnitNoVo cmUnitNoVo = new DmCreateCmUnitNoVo();
//        cmUnitNoVo.setEntityId(dmIcgProcVo.getEntityId());
//        cmUnitNoVo.setYearMonth(dmIcgProcVo.getYearMonth());
//        cmUnitNoVo.setEndDate(addMonth(dmIcgProcVo.getYearMonth()));
//        List<DmReinsTreatyVo> reinsTreatyList = dmReinsTreatyDao.findReinsTreatyOutward(cmUnitNoVo);
//        Set<String> validCodes = new HashSet<>();
//        validCodes.add("11");
//        validCodes.add("12");
//        validCodes.add("21");
//        validCodes.add("31");
//        if(reinsTreatyList!=null && reinsTreatyList.size() > 0){
//            reinsTreatyList.forEach(reinsTreaty -> {
//                String businessSubType = "";
//                if (validCodes.contains(reinsTreaty.getTreatyTypeCode())) {
//                    businessSubType="4";
//                } else if ("71".equals(reinsTreaty.getTreatyTypeCode())){
//                    businessSubType = "5";
//                }else if ("93".equals(reinsTreaty.getTreatyTypeCode())){
//                    businessSubType = "6";
//                }
//                String cmUnitNo = businessSubType + reinsTreaty.getTreatyNo();
//                dmIcgProcVo.setCmUnitNo(cmUnitNo);
//                dmIcgProcVo.setBusinessSubdivisionType(businessSubType);
//                dmIcgProcVo.setCmunitAdapterNo(reinsTreaty.getRelatedTreatyNo());
//                //生成计量单元
//                dmBussCmunitTreatyOutwardService.cmunitIdentify(dmIcgProcVo);
//                //修改已生成计量单元标识
//                dmBussCmunitTreatyOutwardService.updateOldCmunitidentify(dmIcgProcVo);
//            });
//        }
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_UNIT_RECOGNIZER)
    @Override
    public void cmunitDetermine(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE));
        dmBussCmunitTreatyOutwardService.updateCmunitIdentifyRest(dmIcgProcVo);
        //不符合计量单元生成规则的数据,不再处理
        dmBussCmunitTreatyOutwardService.updateNonConformanceDate(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_RISK_TEST)
    @Override
    public void majorRisk(DmIcgProcVo dmIcgProcVo) {
        // 校验参数是否有效
        this.validParam(dmIcgProcVo);
        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.MAJOR_RISK_NODE));
        dmIcgProcVo.setBusinessModel(DmConstant.BusinessModel.MODEL_TREATY_OUT);
        //查找重大风险方案的配置
        String majorRiskPlan = this.getMajorRiskPlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_DIRECT);
        //清除重风测试数据
        dmBussCmunitTreatyOutwardService.updateMajorResultNull(dmIcgProcVo);
        switch (majorRiskPlan) {
            //全部通过
            case DmConstant.MajorRiskPlan.ALL_PASS:
                dmBussCmunitTreatyOutwardService.updateMajorRiskAllPass(dmIcgProcVo);
                break;
            //配置+规则
            case DmConstant.MajorRiskPlan.ADAPTER_CONFIG_RULE:
                //配置
                dmBussCmunitTreatyOutwardService.updateMajorRiskAdapterConfig(dmIcgProcVo);
                //规则 查询重风结果为'C'线上测试的计量单元
                List<DmCmunitTreatyMajorTestVo> dmCmunitDirectMajorTestVos = dmBussCmunitTreatyOutwardService.listCmunitTreatyMajorTestInfo(dmIcgProcVo);
                // 在方法内部定义开始时间
                long startTime = System.currentTimeMillis();

                // 提交任务到线程池
                for (DmCmunitTreatyMajorTestVo item : dmCmunitDirectMajorTestVos) {
                    if (ruleExecutorService.isTerminated()) {
                        ruleExecutorService = Executors.newFixedThreadPool(10); // 重新初始化
                    }
                    ruleExecutorService.submit(() -> {
                        String majorResult = dmDomainMajorRiskService.getFactorValueCalc(item.getCmunitId(), dmIcgProcVo.getEntityId(), item.getTreatyNo(), null, "1", DmConstant.BusinessModel.MODEL_TREATY_OUT, dmIcgProcVo.getYearMonth(),null);
                        item.setMajorResult(majorResult);
                        item.setProcId(dmIcgProcVo.getProcId());
                        dmBussCmunitTreatyOutwardService.updateMajorResultByCmunitId(item);
                    });
                }

                // 等待所有任务完成（可选）
                ruleExecutorService.shutdown();
                while (!ruleExecutorService.isTerminated()) {
                    // 等待所有线程执行完毕
                }

                // 记录结束时间并打印执行时间
                long endTime = System.currentTimeMillis();
                System.out.println("Start Time: " + new java.util.Date(startTime));
                System.out.println("End Time: " + new java.util.Date(endTime));
                System.out.println("Total Execution Time: " + (endTime - startTime) + " ms");
                break;
            default:
                break;
        }
        //如果有不通过的，设置为业务处理不通过
        dmBussCmunitTreatyOutwardService.updateMajorRiskBussNoPass(dmIcgProcVo);
        //调用重测不通过
        this.majorRiskNoPass(dmIcgProcVo);
    }

    @Override
    public void majorRiskNoPass(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //重测业务不通过，生成合同组合
        dmIcgProcVo.setPortfolioColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_TREATY_OUT));
        if (StringUtils.isNotBlank(dmIcgProcVo.getPortfolioColumn())) {
            dmIcgProcVo.setPortfolioProcId(this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE));
            dmIcgProcVo.setIcgColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_TREATY_OUT));
            //根据PROC_CODE查询PROC_ID
            if (StringUtils.isNotBlank(dmIcgProcVo.getIcgColumn())) {
                dmIcgProcVo.setIcgProcId(this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE));
            }
        }
        dmBussCmunitTreatyOutwardService.majorRiskNoPass(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_EVALDEF_CONFIG)
    @Override
    public void evaluateApproach(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.EVALUATE_APPROACH_NODE));

        //评估方法
        dmBussCmunitTreatyOutwardService.evaluateApproach(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_PORTFOLIO)
    @Override
    public void portfolioDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE));

        dmIcgProcVo.setPortfolioColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_TREATY_OUT));

        //合同组合划分
        dmBussCmunitTreatyOutwardService.portfolioDiscern(dmIcgProcVo);

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_DETERMINATION)
    @Override
    public void profitableEstimate(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //this.syncProfitableRuleParam();
        Integer needProfitableEstimateDataNum = dmBussCmunitTreatyOutwardService.queryNeedProfitableEstimateData(dmIcgProcVo);
        if (needProfitableEstimateDataNum <= 0) {
            return;
        }
        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.PROFITABLE_ESTIMATE_NODE));
        dmIcgProcVo.setDataKey(dmIcgProcVo.getEntityId() + dmIcgProcVo.getYearMonth());
        //盈亏取业务年月最一天作为盈亏判定时间
        dmIcgProcVo.setCurrentDay(getMonthEndTime(dmIcgProcVo.getYearMonth()));
        //初始化盈亏判断
        dmBussCmunitTreatyOutwardService.updateProfitableEstimateInit(dmIcgProcVo);
        //查询盈亏判断配置方案
        String profitableEstimatePlan = this.getProfitableEstimatePlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_TREATY_OUT);
        switch (profitableEstimatePlan) {
            case DmConstant.LossRiskPlan.ADAPTER_RULE:
                //规则
                break;
            default:
                //配置
                dmBussCmunitTreatyOutwardService.updateProfitableEstimateAdapterConfig(dmIcgProcVo);
        }
    }

    @Override
    public void icgBorderSeal(DmIcgProcVo dmIcgProcVo) {
        dmIcgProcVo.setEndDate(this.addMonth(dmIcgProcVo.getYearMonth()));
        dmBussCmunitTreatyOutwardService.icgBorderSeal(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACTGROUP)
    @Override
    public void icgDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //合同确认日期
        this.icgBorderSeal(dmIcgProcVo);
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE));
        dmIcgProcVo.setIcgColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_TREATY_OUT));

        dmBussCmunitTreatyOutwardService.icgDiscern(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_INVESTMENT_COST)
    @Override
    public void icgInvestment(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //查询处理中业务期间
        String currentYearMonth = this.getProcessingYearMonth(dmIcgProcVo.getEntityId(), dmIcgProcVo.getYearMonth(), DmConstant.BizCodeOutModel.OUT_MODEL_CMUNIT_TREATY_IN);
        if (StringUtils.isBlank(currentYearMonth) || !currentYearMonth.equals(dmIcgProcVo.getYearMonth())) {
            //只有处理中的才能重新执行
            return;
        }
        //当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
        Integer currentYearMonthIcgGroupCount = dmBussCmunitTreatyOutwardService.getCurrentYearMonthIcgGroupCount(dmIcgProcVo);
        if(currentYearMonthIcgGroupCount != 1){
            return;
        }
        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.ICG_INVESTMENT_NODE));

        //不需要做
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACT_CONFIRMATION)
    @Override
    public void icgConfirm(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.DM_CONTRACT_CONFIRMATION);
        dmIcgProcVo.setProcId(procId);

        dmBussCmunitTreatyOutwardService.icgConfirm(dmIcgProcVo);

        // 执行完成后更新详情状态
        dmDomainPeriodService.executionDetail(dmIcgProcVo.getEntityId()
                , dmIcgProcVo.getYearMonth(), DmConstant.BussCmunit.CMUNIT_TREATY_OUT);
    }

    /**
     * 获取处理中的业务期间
     *
     * @param entityId
     * @param yearMonth
     * @param bizCode
     * @return
     */
    private String getProcessingYearMonth(Long entityId, String yearMonth, String bizCode) {
        return dmDomainBussPeriodService.getCurrentYearMonth(entityId, yearMonth, bizCode, CommonConstant.BussPeriod.PeriodStatus.PROCESSING, CommonConstant.BussPeriod.DetailBizStatus.PREPARING, CommonConstant.BussPeriod.DetailBizDirection.OUTPUT);
    }

    /**
     * 字符串时间转下月第一天时间
     *
     * @param yearMonthStr
     * @return
     */
    private Date addMonth(String yearMonthStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(yearMonthStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取时间字符串当月最后一天的时间
     * @param yearMonthStr
     * @return
     */
    private Date getMonthEndTime(String yearMonthStr){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(yearMonthStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.add(calendar.SECOND,-1);
        return calendar.getTime();
    }
}
