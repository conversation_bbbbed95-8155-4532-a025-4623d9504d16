package com.ss.ifrs.datamgr.dao.push;

import com.ss.ifrs.datamgr.pojo.push.DataPushVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DmBussDataPushAtrDao {

    void insertAtrDapDDUnit(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void mergeAtrDapDDUnit(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapDDUnitExt(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapDDPaymentPlan(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);


    void insertAtrDapDDPaid(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);


    void insertAtrDapFOPaid(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);


    void insertAtrDapFOPremiumPaid(@Param("vo") DataPushVo vo);


    void insertAtrDapTIUnit(@Param("vo") DataPushVo vo);


    void insertAtrDapTIPaid(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);


    void insertAtrDapTOPaidX(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);


    void insertAtrDapTOPaidT(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);


    void insertAtrDapTOPremiumPaid(@Param("vo") DataPushVo vo);


    void insertAtrDapLicRsvAmount(@Param("vo") DataPushVo vo);

    void insertAtrDapDDLicOs(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapDDLoss(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapTILicOs(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapFOLicOs(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapTOEpi(@Param("vo") DataPushVo vo);

    void insertAtrDapTOLicOsT(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapTreaty(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);

    void insertAtrDapXOLicOs(@Param("vo") DataPushVo vo, SliceAttributes sliceParam);
}
