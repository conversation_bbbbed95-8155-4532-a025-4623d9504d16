package com.ss.ifrs.datamgr.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.ss.platform.pojo.bbs.vo.BbsConfAccountPeriodVo;
import com.ss.platform.core.conf.FeignAuthConfig;

/**
 * 调用业务期间的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BbsConfPeriodFeignClient {

	/**
	 * 查询审核通过且有效的会计期间对象列表
	 */
	@RequestMapping(value = "/period/find_valid_period_list", method = RequestMethod.POST)
	public List<BbsConfAccountPeriodVo> findPeriodList();

}
