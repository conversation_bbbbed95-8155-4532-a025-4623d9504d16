package com.ss.ifrs.datamgr.domain.service.common;

import com.ss.ifrs.datamgr.pojo.log.po.DmLogBussCmUnit;
import com.ss.platform.pojo.com.vo.ActOverviewVo;

public interface DmDomainCommonService {

    /**
     *  综合获取流程节点信息
     * @param actOverviewVo
     * @return
     */
    ActOverviewVo getActOverviewVoByObject(ActOverviewVo actOverviewVo);

    /**
     * 根据procCode来获取上一个执行节点
     * @param procCode 流程节点Code
     * @return
     */
    ActOverviewVo getPrevActOverviewByProcCode(String procCode);

}
