package com.ss.ifrs.datamgr.feign;

import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.BussActionStateVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@FeignClient(value = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsTrackBussActionFeignClient {

    /**
     * 初始化流程节点与状态 不存在就新增 存在则修改
     *
     * @return
     */
    @RequestMapping(value = "/track_action/refreshBussActionState", method = RequestMethod.POST)
    BaseResponse<Object> refreshBussActionState(@RequestBody BussActionStateVo bmsBussActionStateVo);

    /**
     * 根据条件查询流程节点与状态列表
     *
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/track_action/findBussActionStateList")
    BaseResponse<Object> findBussActionStateList(@RequestBody BussActionStateVo bmsBussActionStateVo);

    /**
     *  临时处理同步平台流程节点旧新表状态
     * @param bmsBussActionStateVo
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/track_action/synchronizeProcessNodeStatus")
    BaseResponse<Object> synchronizeProcessNodeStatus(@RequestBody BussActionStateVo bmsBussActionStateVo);

    
}
