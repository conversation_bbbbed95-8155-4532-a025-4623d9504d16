package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;

public interface DmDomainProfitableTiCommonDao {

    /*
     *QR002-非跟单IACF%,QR003-预期维持费用率%,BE013-预期赔付率%,QR008-非金融风险调整%
     */
    void prepareQuota(DmIcgProcVo dmIcgProcVo);
    void deleteDuctProfitableQuota(DmIcgProcVo dmIcgProcVo);

    /*
     *QP001:(预期赔付模式%)
     */
    void prepareDevelopQuota(DmIcgProcVo dmIcgProcVo);
    void deleteDuctProfitableDevelopQuota(DmIcgProcVo dmIcgProcVo);

    /*
     *loss_rate压力情景损失率
     */
    void prepareStressLossRate(DmIcgProcVo dmIcgProcVo);

    /*
     *D005 无风险曲线率
     */
    void prepareInterestRate(DmIcgProcVo dmIcgProcVo);


    void checkBbaAssumingConfiguration(DmIcgProcVo dmIcgProcVo);

    void checkPaaAssumingConfiguration(DmIcgProcVo dmIcgProcVo);

    void checkStressScenarioConfiguration(DmIcgProcVo dmIcgProcVo);

    void checkBbaInterestRateConfiguration(DmIcgProcVo dmIcgProcVo);

    void checkPaaInterestRateConfiguration(DmIcgProcVo dmIcgProcVo);
}
