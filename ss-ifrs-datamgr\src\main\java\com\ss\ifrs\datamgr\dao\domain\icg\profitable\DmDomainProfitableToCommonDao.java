package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

public interface DmDomainProfitableToCommonDao {

    /*
     *QR002-非跟单IACF%,QR003-预期维持费用率%,BE013-预期赔付率%,QR008-非金融风险调整%
     */
    void prepareQuota();

    /*
     *QP001:(预期赔付模式%)
     */
    void prepareDevelopQuota();

    /*
     *loss_rate压力情景损失率
     */
    void prepareStressLossRate();

    /*
     *D005 无风险曲线率
     */
    void prepareInterestRate();
}
