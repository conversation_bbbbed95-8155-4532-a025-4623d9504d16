package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.bms.log.vo.BmsLogPubTaskVo;
import com.ss.platform.pojo.bms.job.po.BmsScheduleDetail;
import com.ss.platform.pojo.bms.log.po.BmsLogScheduleJob;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;
import com.ss.platform.pojo.bms.log.vo.BmsLogScheduleJobVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfMethodParamVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskDetailVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 调用定时任务的接口
 * 
 * <AUTHOR>
 */
@FeignClient(value = "SS-PLATFORM-SCHEDULE", configuration = { FeignAuthConfig.class })
public interface BmsScheduleFeignClient {

	/**
	 * 查看定时任务信息
	 *
	 * @param scheduleJobVo
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/schedule/view", method = RequestMethod.POST)
	public BaseResponse<BmsScheduleJobVo> view(@RequestBody BmsScheduleJobVo scheduleJobVo) throws Exception;

	/**
	 * 立即执行自动任务
	 *
	 * @param scheduleJobVo JobName和JobGroup必要
	 * @return com.ss.platform.pojo.base.po.BaseResponse<java.lang.Object>
	 */
	@PostMapping("/schedule/immediateExecutionTask")
	public BaseResponse<Object> immediateExecutionTask(@RequestBody BmsScheduleJobVo scheduleJobVo) throws Exception;


	/**
	 * 根据业务期间详情查找定时任务信息
	 *
	 * @param entityId 核算单位id
	 * @param bizCode  业务编码标识
	 */
	@RequestMapping(value = "/schedule/find_jobdetail_by_period/{entityId}/{bizCode}", method = RequestMethod.GET)
	BmsScheduleDetail findJobDetailByPeriod(@PathVariable("entityId") Long entityId,
											@PathVariable("bizCode") String bizCode);

	@RequestMapping(value = "/schedule/find_jobdetail_by_entity_id_and_conf_task_id/{entityId}/{confTaskId}", method = RequestMethod.GET)
	BmsScheduleDetail findJobDetailsByEntityIdAndConfTaskId(@PathVariable("entityId") Long entityId, @PathVariable("confTaskId") Long confTaskId);

	/**
	 * @description 根据业务单位和功能组ID查找功能组对象信息
	 * @param  entityId, confTaskId
	 * @return com.ss.platform.admin.pojo.schedule.vo.BmsQrtzConfTaskVo
	 * <AUTHOR>
	 * @date 2023/4/20 17:03
	 */
	@RequestMapping(value = "/schedule/find_conf_task_vo_by_entity_id_and_conf_task_id/{entityId}/{confTaskId}", method = RequestMethod.GET)
    BmsQrtzConfTaskVo findConfTaskVoByEntityIdAndConfTaskId(@PathVariable("entityId") Long entityId, @PathVariable("confTaskId") Long confTaskId);

	/**
	 * @description 根据业务单位和依赖功能组ID查找功能组对象信息
	 * @param  entityId, relationTaskId
	 * @return com.ss.platform.admin.pojo.schedule.vo.BmsQrtzConfTaskVo
	 * <AUTHOR>
	 * @date 2023/4/20 17:03
	 */
	@RequestMapping(value = "/schedule/find_conf_task_vo_by_entity_id_and_relation_task_id/{entityId}/{relationTaskId}", method = RequestMethod.GET)
	List<BmsQrtzConfTaskVo> findConfTaskVoByEntityIdAndRelationTaskId(@PathVariable("entityId") Long entityId, @PathVariable("relationTaskId") Long relationTaskId);

	/**
	 * @description 根据功能id、业务单位、业务期间、账套、任务模式、查询自动或手动模式查询在当前频率下执行状态为1-执行中的功能任务日志是否存在

	 * @throws
	 * <AUTHOR>
	 * @date 2022/9/30 11:45
	 */
	@RequestMapping(value = "/log_job/find_task_dtl/processing_or_success_log_by_vo", method = RequestMethod.POST)
	int findTaskDtlProcessingOrSuccessLogByVo(@RequestBody BmsLogPubTaskVo bmsLogPubTaskVo);

	/**
	 * @description 查询当前频率下，任务状态为1-执行中的功能组日志
	 * @throws
	 * <AUTHOR>
	 * @date 2022/9/30 11:42
	 */
	@RequestMapping(value = "/log_job/find_schedule_job_log_in_progress_by_id/{confTaskId}/{entityId}", method = RequestMethod.GET)
	public BaseResponse<BmsLogScheduleJobVo> findScheduleJobLogInProgressByConfTaskId(@PathVariable("confTaskId") Long confTaskId, @PathVariable("entityId") Long entityId);

	/**
	 * @description 查询当前频率下，任务状态为0-待执行的功能组日志
	 * @throws
	 * <AUTHOR>
	 * @date 2022/9/30 11:42
	 */
	@RequestMapping(value = "/log_job/find_schedule_job_log_to_exec_by_id/{confTaskId}/{entityId}", method = RequestMethod.GET)
	public BaseResponse<BmsLogScheduleJobVo> findScheduleJobLogToExecByConfTaskId(@PathVariable("confTaskId") Long confTaskId, @PathVariable("entityId") Long entityId);

	@RequestMapping(value = "/log_job/save_schedule_job_log", method = RequestMethod.POST)
	BaseResponse<BmsLogScheduleJobVo> saveScheduleJobLog(@RequestBody BmsLogScheduleJob scheduleJobLog);

	@RequestMapping(value = "/log_job/update_schedule_job_log", method = RequestMethod.POST)
	BaseResponse<Object> updateScheduleJobLog(BmsLogScheduleJob scheduleJobLog);

	/**
	 * 根据业务单位和功能组ID查询功能清单
	 *
	 * @param entityId
	 * @param confTaskId
	 * @return
	 */
	@RequestMapping(value = "/functionConf/find_task_dtl_list/{entityId}/{confTaskId}", method = RequestMethod.GET)
	List<BmsQrtzConfTaskDetailVo> findConfTaskDtlListByConfTaskId(@PathVariable("entityId") Long entityId,
																  @PathVariable("confTaskId") Long confTaskId);

	/**
	 * 根据业务单位和功能编码查询某个功能清单
	 *
	 * @param entityId
	 * @param funcCode
	 * @return
	 */
	@RequestMapping(value = "/functionConf/find_task_dtl/{entityId}/{funcCode}", method = RequestMethod.GET)
	BmsQrtzConfTaskDetailVo findConfTaskDtlByFuncCode(@PathVariable("entityId") Long entityId,
													  @PathVariable("funcCode") String funcCode);

	/**
	 * @description 根据功能id查看下功能对象信息
	 * @param  taskDetailId
	 * @return com.ss.platform.pojo.base.po.BaseResponse<com.ss.platform.admin.pojo.schedule.vo.BmsQrtzConfTaskDetailVo>
	 * @throws
	 * <AUTHOR>
	 * @date 2023/4/3 21:06
	 */
	@RequestMapping(value = "/functionConf/config_item/index/find_by_pk/{taskDetailId}", method = RequestMethod.GET)
	BaseResponse<BmsQrtzConfTaskDetailVo> findTaskDetailById(@PathVariable Long taskDetailId);

	/**
	 * @description 通过业务单位和功能组ID查询功能组优先级靠后的一个任务对象【only serve for feign】
	 * @param  [entityId, funcCode]
	 * @return com.ss.platform.admin.pojo.schedule.vo.BmsQrtzConfTaskDetailVo
	 * @throws
	 * <AUTHOR>
	 * @date 2022/9/8 18:23
	 */
	@RequestMapping(value = "/functionConf/find_last_task_dtl_by_conf_task_id/{entityId}/{confTaskId}", method = RequestMethod.GET)
	BmsQrtzConfTaskDetailVo findLastTaskDtlByConfTaskId(@PathVariable("entityId") Long entityId,
														@PathVariable("confTaskId") Long confTaskId);

	@RequestMapping(value = "/functionConf/find_task_dtl/method_param", method = RequestMethod.POST)
	BmsQrtzConfMethodParamVo findTaskDetailMethodParam(@RequestBody BmsQrtzConfMethodParamVo bmsQrtzConfMethodParamVo);

	@RequestMapping(value = "/functionConf/method_param/add", method = RequestMethod.POST)
	BaseResponse<BmsQrtzConfMethodParamVo> addMethodParam(@RequestBody BmsQrtzConfMethodParamVo bmsQrtzConfMethodParamVo);

	/**
	 * 保存日志接口
	 */
	@RequestMapping(value = "/log_job_details/add", method = RequestMethod.POST)
	BaseResponse<String> addTaskLog(@RequestBody BmsLogPubTaskVo bmsLogPubTaskVo);

	/**
	 * 更新日志接口
	 */
	@RequestMapping(value = "/log_job_details/update_task_log", method = RequestMethod.POST)
	BaseResponse<String> updateTaskLog(@RequestBody BmsLogPubTaskVo bmsLogPubTaskVo);

	/**
	 * 查询日志接口
	 */
	@RequestMapping(value = "/log_job_details/find_task_log", method = RequestMethod.POST)
	BaseResponse<BmsLogPubTaskVo> findTaskLog(@RequestBody BmsLogPubTaskVo bmsLogPubTaskVo);

	/**
	 * 更新任务追踪功能日志状态接口
	 * 参数集合：
	 *      entityId  业务单位
	 *      yearMonth  业务年月，可为空
	 *      bookCode  账套【会计引擎、报表管理的业务期间含账套字段】，可为空
	 *      taskCode  任务编码【仅前面判定使用，后面都查询获取】
	 *      taskMode  任务模式
	 *      funcCode  功能编码，可为空
	 *      taskStatus  任务状态: 0-待执行、1-执行中、2-成功、3-失败、4-不执行
	 */
	@RequestMapping(value = "/log_job_details/updateStatus", method = RequestMethod.POST)
	BaseResponse<String> updateTaskLogStatus(@RequestBody Map<String, Object> paramMap);

	/**
	 * 查询最近5次任务运行日志
	 */
	@RequestMapping(value = "/log_job_details/find_task_log_recently", method = RequestMethod.POST)
	List<BmsLogPubTaskVo> findTaskLogRecently(@RequestBody Map<String, Object> paramMap);
}
