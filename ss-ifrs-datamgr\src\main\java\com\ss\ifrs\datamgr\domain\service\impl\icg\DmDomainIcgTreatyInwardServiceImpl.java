package com.ss.ifrs.datamgr.domain.service.impl.icg;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfContractRiskDefDao;
import com.ss.ifrs.datamgr.dao.dap.rein.DmReinsTreatyDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainIcg;
import com.ss.ifrs.datamgr.domain.exception.DmCmunitException;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgService;
import com.ss.ifrs.datamgr.domain.service.icg.cmunitno.DmDomainCmUnitNoService;
import com.ss.ifrs.datamgr.domain.service.icg.majorrisk.DmDomainMajorRiskService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableBbaService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitablePaaService;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitFacOutwards;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreatyInward;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRiskDefVo;
import com.ss.ifrs.datamgr.pojo.dap.po.rein.DmReinsTreaty;
import com.ss.ifrs.datamgr.pojo.dap.vo.rein.DmReinsTreatyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmUnitNoAdapterVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitDirectMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitTreatyMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCreateCmUnitNoVo;
import com.ss.ifrs.datamgr.pojo.log.po.DmLogBussCmUnit;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitDirectService;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitTreatyInwardService;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.pojo.com.po.SliceAttributes;
import com.ss.platform.util.DataSliceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service(DmConstant.BusinessModel.MODEL_TREATY_IN + "ICG")
@Slf4j
public class DmDomainIcgTreatyInwardServiceImpl extends DmDomainIcg implements DmDomainIcgService {

    @Autowired
    private AppConfig appConfig;
    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_IN + "MajorTest")
    @Autowired
    private DmDomainMajorRiskService dmDomainMajorRiskService;

    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_IN)
    @Autowired
    private DmDomainCmUnitNoService domainCmUnitNoService;

    @Autowired
    private DmDomainBussPeriodService dmDomainBussPeriodService;

    @Autowired
    private DmConfContractRiskDefDao dmConfContractRiskDefDao;

    @Autowired
    private DmBussCmunitTreatyInwardService dmBussCmunitTreatyInwardService;

    @Qualifier(CommonConstant.BussPeriod.PeriodStatus.COMPLETED)
    @Autowired
    private DmDomainPeriodService dmDomainPeriodService;

    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_PAA")
    @Autowired
    private DmDomainProfitablePaaService dmDomainProfitablePaaService;

    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_BBA")
    @Autowired
    private DmDomainProfitableBbaService dmDomainProfitableBbaService;
    @Autowired
    private DmReinsTreatyDao dmReinsTreatyDao;
    @Autowired
    private ExecutorService ruleExecutorService; // 替换为 Spring 管理的线程池

    @Override
    public void cmunitIdentify(DmIcgProcVo dmIcgProcVo) {
        if (dmIcgProcVo == null) {
            throw new DmCmunitException("Parameter dmIcgProcVo is null");
        }
        if (dmIcgProcVo.getEntityId() == null || StringUtil.isEmpty(dmIcgProcVo.getYearMonth())){
            throw new DmCmunitException("entityId or yearMonth is empty");
        }
        //获取长短险标识生成是否开启 0-不开启
        this.getShortRiskFlag(dmIcgProcVo);
        dmIcgProcVo.setEndDate(addMonth(dmIcgProcVo.getYearMonth()));
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE));
        //生成计量单元
        dmBussCmunitTreatyInwardService.cmunitIdentify(dmIcgProcVo);
        //修改已生成计量单元标识
        dmBussCmunitTreatyInwardService.updateOldCmunitidentify(dmIcgProcVo);
//        //获取计量单元No
//        DmCreateCmUnitNoVo cmUnitNoVo = new DmCreateCmUnitNoVo();
//        cmUnitNoVo.setEntityId(dmIcgProcVo.getEntityId());
//        cmUnitNoVo.setYearMonth(dmIcgProcVo.getYearMonth());
//        List<DmReinsTreatyVo> reinsTreatyList = dmReinsTreatyDao.findReinsTreatyInward(cmUnitNoVo);
//        Set<String> validCodes = new HashSet<>();
//        validCodes.add("11");
//        validCodes.add("12");
//        validCodes.add("21");
//        validCodes.add("31");
//        validCodes.add("71");
//        if(reinsTreatyList!=null && reinsTreatyList.size() > 0) {
//            reinsTreatyList.forEach(reinsTreaty -> {
//                String businessSubType = "";
//                if (validCodes.contains(reinsTreaty.getTreatyTypeCode())) {
//                    businessSubType = "2";
//                } else if ("93".equals(reinsTreaty.getTreatyTypeCode())) {
//                    businessSubType = "3";
//                }
//                String cmUnitNo = businessSubType + reinsTreaty.getRelatedTreatyNo();
//                dmIcgProcVo.setCmUnitNo(cmUnitNo);
//                dmIcgProcVo.setBusinessSubdivisionType(businessSubType);
//                dmIcgProcVo.setCmunitAdapterNo(reinsTreaty.getRelatedTreatyNo());
//                //生成计量单元
//                dmBussCmunitTreatyInwardService.cmunitIdentify(dmIcgProcVo);
//                //修改已生成计量单元标识
//                dmBussCmunitTreatyInwardService.updateOldCmunitidentify(dmIcgProcVo);
//            });
//        }
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_UNIT_RECOGNIZER)
    @Override
    public void cmunitDetermine(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE));

        //只有处理中的才能重新执行
        dmBussCmunitTreatyInwardService.updateCmunitIdentifyRest(dmIcgProcVo);

        //不符合计量单元生成规则的数据,不再处理
        dmBussCmunitTreatyInwardService.updateNonConformanceDate(dmIcgProcVo);

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_RISK_TEST)
    @Override
    public void majorRisk(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        dmIcgProcVo.setBusinessModel(DmConstant.BusinessModel.MODEL_TREATY_IN);
        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.MAJOR_RISK_NODE));
        dmIcgProcVo.setBusinessModel(DmConstant.BusinessModel.MODEL_TREATY_IN);
        //查询重大风险测试配置方案
        String majorRiskPlan = this.getMajorRiskPlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_TREATY_IN);
        //清除重风测试数据
        dmBussCmunitTreatyInwardService.updateMajorResultNull(dmIcgProcVo);
        switch (majorRiskPlan) {
            //全部通过
            case DmConstant.MajorRiskPlan.ALL_PASS:
                dmBussCmunitTreatyInwardService.updateMajorRiskAllPass(dmIcgProcVo);
                break;
            //配置+规则
            case DmConstant.MajorRiskPlan.ADAPTER_CONFIG_RULE:
                //配置
                dmBussCmunitTreatyInwardService.updateMajorRiskAdapterConfig(dmIcgProcVo);
                //规则 查询重风结果为'C'线上测试的计量单元
                List<DmCmunitTreatyMajorTestVo> dmCmunitDirectMajorTestVos = dmBussCmunitTreatyInwardService.listCmunitTreatyMajorTestInfo(dmIcgProcVo);
                // 在方法内部定义开始时间
                long startTime = System.currentTimeMillis();

                // 提交任务到线程池
                for (DmCmunitTreatyMajorTestVo item : dmCmunitDirectMajorTestVos) {
                    if (ruleExecutorService.isTerminated()) {
                        ruleExecutorService = Executors.newFixedThreadPool(10); // 重新初始化
                    }
                    ruleExecutorService.submit(() -> {
                        String majorResult = dmDomainMajorRiskService.getFactorValueCalc(item.getCmunitId(), dmIcgProcVo.getEntityId(), item.getTreatyNo(), null, "1", DmConstant.BusinessModel.MODEL_TREATY_IN, dmIcgProcVo.getYearMonth(),null);
                        item.setMajorResult(majorResult);
                        item.setProcId(dmIcgProcVo.getProcId());
                        dmBussCmunitTreatyInwardService.updateMajorResultByCmunitId(item);
                    });
                }

                // 等待所有任务完成（可选）
                ruleExecutorService.shutdown();
                while (!ruleExecutorService.isTerminated()) {
                    // 等待所有线程执行完毕
                }

                // 记录结束时间并打印执行时间
                long endTime = System.currentTimeMillis();
                System.out.println("Start Time: " + new java.util.Date(startTime));
                System.out.println("End Time: " + new java.util.Date(endTime));
                System.out.println("Total Execution Time: " + (endTime - startTime) + " ms");
                break;
            default:
                break;
        }
    }

    @Override
    public void majorRiskNoPass(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //是否存在重测业务不通过及未生成合同组数据
        Integer count = dmBussCmunitTreatyInwardService.queryMajorRiskNoPassAndNoIcgGroup(dmIcgProcVo);
        if (count == 0) {
            return;
        }

        //重测业务不通过，生成合同组合
        dmIcgProcVo.setPortfolioColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_TREATY_IN));
        if (StringUtils.isNotBlank(dmIcgProcVo.getPortfolioColumn())) {
            dmIcgProcVo.setPortfolioProcId(this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE));
            dmIcgProcVo.setIcgColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_TREATY_IN));
            //根据PROC_CODE查询PROC_ID
            if (StringUtils.isNotBlank(dmIcgProcVo.getIcgColumn())) {
                dmIcgProcVo.setIcgProcId(this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE));
            }
        }
        dmBussCmunitTreatyInwardService.majorRiskNoPass(dmIcgProcVo);


    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_EVALDEF_CONFIG)
    @Override
    public void evaluateApproach(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.EVALUATE_APPROACH_NODE));

        //评估方法
        dmBussCmunitTreatyInwardService.evaluateApproach(dmIcgProcVo);

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_PORTFOLIO)
    @Override
    public void portfolioDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //当前业务年月存在未进行评估方法的数据
        Integer count = dmBussCmunitTreatyInwardService.currentYearMonthNoEvaluateApproachDate(dmIcgProcVo);
        if (count > 0) {
            //当前业务年月存在未进行评估方法的数据
            return;
        }

        dmIcgProcVo.setPortfolioColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_TREATY_IN));

        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE));

        //合同组合划分
        dmBussCmunitTreatyInwardService.portfolioDiscern(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_DETERMINATION)
    @Override
    public void profitableEstimate(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

//        this.syncProfitableRuleParam();
        Integer needProfitableEstimateDataNum = dmBussCmunitTreatyInwardService.queryNeedProfitableEstimateData(dmIcgProcVo);
        if (needProfitableEstimateDataNum <= 0) {
            return;
        }

        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.PROFITABLE_ESTIMATE_NODE));
        dmIcgProcVo.setDataKey(dmIcgProcVo.getEntityId() + dmIcgProcVo.getYearMonth());

        //初始化盈亏判断
        dmBussCmunitTreatyInwardService.updateProfitableEstimateInit(dmIcgProcVo);

        //查询盈亏判断配置方案
        String profitableEstimatePlan = this.getProfitableEstimatePlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_TREATY_IN);
        switch (profitableEstimatePlan) {
            case DmConstant.LossRiskPlan.ADAPTER_RULE:
                // 同步盈亏规则参数
                this.syncProfitableRuleParam();
                //规则
                this.profitableEstimateAdapterRule(dmIcgProcVo);
                break;
            default:
                //配置
                dmBussCmunitTreatyInwardService.updateProfitableEstimateAdapterConfig(dmIcgProcVo);
        }
//        DmLogBussCmUnit dmLogBussCmUnit = new DmLogBussCmUnit();
//        ClassUtil.copyProperties(dmIcgProcVo, dmLogBussCmUnit);
//        //dmLogBussCmUnit.setTraceNo(dmIcgProcVo.getEntityId() + "-" + profitableEstimatePlan);
//        dmLogBussCmUnit.setTraceCode("proc_profit_loss_discern");
//        dmLogBussCmUnit.setTraceStatus("1");
//        dmLogBussCmUnit.setTraceMsg(null);
//        this.saveCmunitLog(dmLogBussCmUnit);

    }

    @Override
    public void icgBorderSeal(DmIcgProcVo dmIcgProcVo) {
        dmIcgProcVo.setEndDate(this.addMonth(dmIcgProcVo.getYearMonth()));
        dmBussCmunitTreatyInwardService.icgBorderSeal(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACTGROUP)
    @Override
    public void icgDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //合同确认日期
        this.icgBorderSeal(dmIcgProcVo);
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE));
        dmIcgProcVo.setIcgColumn(this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_TREATY_IN));
        dmBussCmunitTreatyInwardService.icgDiscern(dmIcgProcVo);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_INVESTMENT_COST)
    @Override
    public void icgInvestment(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //查询处理中业务期间
        String currentYearMonth = this.getProcessingYearMonth(dmIcgProcVo.getEntityId(), dmIcgProcVo.getYearMonth(), DmConstant.BizCodeOutModel.OUT_MODEL_CMUNIT_TREATY_IN);
        if (StringUtils.isBlank(currentYearMonth) || !currentYearMonth.equals(dmIcgProcVo.getYearMonth())) {
            //只有处理中的才能重新执行
            return;
        }
        //当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
        Integer currentYearMonthIcgGroupCount = dmBussCmunitTreatyInwardService.getCurrentYearMonthIcgGroupCount(dmIcgProcVo);
        if(currentYearMonthIcgGroupCount != 1){
            return;
        }
        //获取流程节点
        dmIcgProcVo.setProcId(this.getProdNode(DmConstant.IcgProcidNode.ICG_INVESTMENT_NODE));

        //不需要做

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACT_CONFIRMATION)
    @Override
    public void icgConfirm(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.DM_CONTRACT_CONFIRMATION);
        dmIcgProcVo.setProcId(procId);

        //合同确认
        dmBussCmunitTreatyInwardService.icgConfirm(dmIcgProcVo);

        // 执行完成后更新详情状态
        dmDomainPeriodService.executionDetail(dmIcgProcVo.getEntityId()
                , dmIcgProcVo.getYearMonth(), DmConstant.BussCmunit.CMUNIT_TREATY_IN);

        //备份
        this.backProfitResult(dmIcgProcVo, DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE);

    }


    /**
     * 获取处理中的业务期间
     *
     * @param entityId
     * @param yearMonth
     * @param bizCode
     * @return
     */
    private String getProcessingYearMonth(Long entityId, String yearMonth, String bizCode) {
        return dmDomainBussPeriodService.getCurrentYearMonth(entityId, yearMonth, bizCode, CommonConstant.BussPeriod.PeriodStatus.PROCESSING, CommonConstant.BussPeriod.DetailBizStatus.PREPARING, CommonConstant.BussPeriod.DetailBizDirection.OUTPUT);
    }

    /**
     * 盈亏判断-规则
     *
     * @param dmIcgProcVo
     */
    private void profitableEstimateAdapterRule(DmIcgProcVo dmIcgProcVo) {
        //todo 计算PAA
        dmDomainProfitablePaaService.Profitable(dmIcgProcVo);
        //todo 计算BBA
        dmDomainProfitableBbaService.Profitable(dmIcgProcVo);
    }

    /**
     * 字符串时间转下月第一天时间
     *
     * @param yearMonthStr
     * @return
     */
    private Date addMonth(String yearMonthStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(yearMonthStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTime();
    }
}
