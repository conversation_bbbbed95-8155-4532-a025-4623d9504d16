package com.ss.ifrs.datamgr.job;

import com.ss.ifrs.datamgr.service.DmRestoreDataService;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.library.utils.CacheUtil;
import com.ss.library.utils.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/restore")
@Api(value = "配置数据还原自动任务API")
public class DmJobRestoreDataApi extends BaseApi {

    @Autowired
    DmRestoreDataService dmRestoreDataService;

    @ApiOperation(value = "配置类相关备份表数据定期还原")
    @RequestMapping(value = "/conf/table_bak", method = {RequestMethod.GET, RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> confTableBakRestore(HttpServletRequest request) {
        String result = "0";
        String exceptionMsg = null;
        try {
            dmRestoreDataService.executeRestoreSql();
            result = "1";
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "清理Hazelcast指定缓存")
    @RequestMapping(value = "/clearCache", method = {RequestMethod.GET, RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> clearCache(HttpServletRequest request) {
        String result = "0";
        String exceptionMsg = null;
        try {
            CacheUtil.remove(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.DmIdentity.APP_CODE);
            result = "OK";
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }
}
