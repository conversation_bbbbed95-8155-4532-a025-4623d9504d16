package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.bms.log.vo.BmsLogExportTrackVo;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 调用BPL日志管理的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsTrackExportFeignClient {

    /**
     * 保存操作的日志接口
     *
     */
    @RequestMapping(value = "/export_track_log/save", method = RequestMethod.POST)
    BaseResponse<Object> save(@RequestBody BmsLogExportTrackVo bmsLogExportTrackVo);

    /**
     * 更新操作的日志接口
     *
     */
    @RequestMapping(value = "/export_track_log/update", method = RequestMethod.POST)
    BaseResponse<Object> update(@RequestBody BmsLogExportTrackVo bmsLogExportTrackVo);

}
