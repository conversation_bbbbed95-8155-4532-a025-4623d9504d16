/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-06-25 10:53:38
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.rpt;

import com.ss.ifrs.datamgr.pojo.rpt.po.PocCheckLogReport;
import com.ss.ifrs.datamgr.pojo.rpt.po.PocRuleSuggestReport;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-06-25 10:53:38<br/>
 * Description: 异常规则建议表 Dao类<br/>
 * Related Table Name: DM_REPORT_RULE_SUGGEST<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface PocRuleSuggestReportDao extends IDao<PocRuleSuggestReport, Long> {

    List<PocRuleSuggestReport> getRuleSuggestList(@Param("list") List<String> ruleCodeList);

    List<PocCheckLogReport> getCheckLogList(String tableName, String taskStatus, String language, String yearMouth);

    List<PocCheckLogReport> getCheckLogListByEntityId(String tableName, String entityId, String taskStatus, String language, String yearMouth);

}