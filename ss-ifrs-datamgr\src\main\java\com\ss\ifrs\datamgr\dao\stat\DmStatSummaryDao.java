package com.ss.ifrs.datamgr.dao.stat;


import com.ss.ifrs.datamgr.pojo.stat.po.DmStatSummary;
import com.ss.ifrs.datamgr.pojo.stat.vo.DmDuctStatVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * Create Date: 2022-10-19 10:27:06<br/>
 * Description: 解析统计结果表 Dao类<br/>
 * Related Table Name: DM_DUCT_STAT_AMOUNT<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmStatSummaryDao extends IDao<DmStatSummary, Long> {
    List<Map<String,Object>> executeSql(String resultSql);

    List<DmDuctStatVo> findStatAmountVos(DmDuctStatVo dmDuctStatAmountVo);

    List<DmDuctStatVo> findStatAmountColumns(long bizTypeId);

    List<Map<String, Object>> getStatAmountData(Map<String, Object> paramMap);

    Long findStatCountData(Map<String, Object> paramMap);
}