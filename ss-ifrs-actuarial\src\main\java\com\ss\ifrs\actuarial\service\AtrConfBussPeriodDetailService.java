package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;

import java.util.List;

public interface AtrConfBussPeriodDetailService {

	List<AtrConfBussPeriodDetailVo> findPeriodDtlByEntityId(AtrConfBussPeriodVo atrConfBussPeriodVo);

	BmsScheduleJobVo findJobByBizCode(BmsScheduleJobVo scheduleJobVo);

	void immediateExecutionTask(BmsScheduleJobVo scheduleJobVo) throws Exception;

	public String checkInputReadyState(AtrConfBussPeriodDetailVo confBussPeriodDetailVo);
	
	public String checkOutputReadyState(AtrConfBussPeriodDetailVo confBussPeriodDetailVo);
}
