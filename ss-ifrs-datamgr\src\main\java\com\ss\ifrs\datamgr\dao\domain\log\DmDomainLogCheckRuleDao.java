package com.ss.ifrs.datamgr.dao.domain.log;


import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfCheckRuleVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.log.vo.DmLogCheckRuleVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DmDomainLogCheckRuleDao {

    Integer deleteLogCheckRule(DmLogCheckRuleVo dmLogCheckRuleVo);

    Integer insertLogCheckRule(@Param("dmConfCheckRuleVo") DmConfCheckRuleVo dmConfCheckRuleVo, @Param("dmBussDataVerifyVo") DmBussDataVerifyVo dmBussDataVerifyVo, SliceAttributes sliceParam);

    Integer getLogCheckRuleCount(DmLogCheckRuleVo dmLogCheckRuleVo,String traceNo);

}
