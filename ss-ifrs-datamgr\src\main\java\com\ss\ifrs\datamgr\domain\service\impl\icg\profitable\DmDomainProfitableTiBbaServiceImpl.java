package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.profitable.DmDomainProfitableTiBbaDao;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableBbaService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.library.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_BBA")
public class DmDomainProfitableTiBbaServiceImpl implements DmDomainProfitableBbaService {

    @Autowired
    private DmDomainProfitableTiBbaDao dmDomainProfitableTiBbaDao;

    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_COMMON")
    @Autowired
    private DmDomainProfitableCommonService dmDomainProfitableCommonService;

    @Override
    public void calCmUnitDevelopAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiBbaDao.deleteDuctProfitableCmUnitAmount(dmIcgProcVo);
        Date beginDate = null;
        try {
            beginDate = DateUtil.strToDate(dmIcgProcVo.getYearMonth() + "01", "yyyyMMdd");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        dmIcgProcVo.setBeginDate(beginDate);

        //未来发展期个数：评估年月 到 终保时间 相隔月份数
        int devMonthCount = dmDomainProfitableTiBbaDao.getFutureDevelopmentPeriodCount(dmIcgProcVo);

        //循环各个发展期保费占比
        for (int i = 0; i <= devMonthCount; i++) {
            // 获取当期发展期的起始时间
            Date newBegindate = DateUtil.addMonths(beginDate, i);
            // 获取当期发展期的结束时间
            Date endDate = DateUtil.getLastDateByYearMonth(DateUtil.dateToStr(newBegindate, "yyyyMM"));
            dmIcgProcVo.setBeginDate(newBegindate);
            dmIcgProcVo.setEndDate(endDate);
            //月度保费：总保费 * 各月份天数/总承保天数
            dmDomainProfitableTiBbaDao.calCmUnitDevelopAmount(dmIcgProcVo, i);
        }

    }

    @Override
    public void calIacfRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiBbaDao.calIacfRate(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiBbaDao.calCmUnitExpectFutureAmount(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiBbaDao.calCmUnitExpectLossAmount(dmIcgProcVo);
    }

    @Override
    public void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiBbaDao.calCmUnitCostRate(dmIcgProcVo);
    }

    @Override
    public void Profitable(DmIcgProcVo dmIcgProcVo) {
        //初始化盈亏判断计量单元
        dmDomainProfitableTiBbaDao.deleteDuctCmUnitProfitable(dmIcgProcVo);
        dmDomainProfitableTiBbaDao.insertDuctCmUnitProfitable(dmIcgProcVo);

        //满足条件的数据为空, 结束判定
        Integer count = dmDomainProfitableTiBbaDao.getDuctCmUnitProfitableCount(dmIcgProcVo);
        if (count <= 0) {
            return;
        }

        //初始化计算参数数据
        dmDomainProfitableCommonService.prepareQuota(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareStressLossRate(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareInterestRate(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareDevelopQuota(dmIcgProcVo);
        //计量单元 - 现金流
        calCmUnitDevelopAmount(dmIcgProcVo);

        //校验数据:数据完整性 及 配置表数据是否缺失,  NODE_STATE - 1 校验通过;  NODE_STATE -2 校验不通过
        dmDomainProfitableCommonService.checkFactor(dmIcgProcVo);

        //存在数据不通过校验，更新数据状态，结束处理
        Integer failCount = dmDomainProfitableTiBbaDao.getDuctCmUnitProfitableFailCount(dmIcgProcVo);
        if (failCount > 0) {
            dmDomainProfitableTiBbaDao.updateProfitableEstimateCheckFail(dmIcgProcVo);
            return;
        }

        //跟單IACF率
        calIacfRate(dmIcgProcVo);
        //预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
        calCmUnitExpectFutureAmount(dmIcgProcVo);
        //预期赔付现金流现值
        calCmUnitExpectLossAmount(dmIcgProcVo);
        //成本率
        calCmUnitCostRate(dmIcgProcVo);
        //更新盈亏标识
        dmDomainProfitableTiBbaDao.updateProfitableEstimateDuctData(dmIcgProcVo);

        //更新判定结果及流程状态
        dmDomainProfitableTiBbaDao.updateProfitableEstimateResult(dmIcgProcVo);

    }

}
