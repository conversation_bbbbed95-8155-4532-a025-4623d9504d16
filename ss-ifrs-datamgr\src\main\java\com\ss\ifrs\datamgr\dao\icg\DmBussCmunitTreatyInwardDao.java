package com.ss.ifrs.datamgr.dao.icg;

import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreaty;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreatyInward;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectSelectVo;
//import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitTreatyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmunitMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitDirectMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitTreatyMajorTestVo;
import org.apache.ibatis.annotations.Mapper;
//import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DmBussCmunitTreatyInwardDao {

    /**
     * 计量单元划分
     */
    void cmunitIdentify(DmIcgProcVo dmIcgProcVo);
    void updateCmunitIdentifyRest(DmIcgProcVo dmIcgProcVo);
    void updateOldCmunitidentify(DmIcgProcVo dmIcgProcVo);
    void updateNonConformanceDate(DmIcgProcVo dmIcgProcVo);

    /**
     * 重大风险测试
     */
    void updateMajorRiskAllPass(DmIcgProcVo dmIcgProcVo);
    void updateMajorRiskAdapterConfig(DmIcgProcVo dmIcgProcVo);
//    void updateMajorRiskAdapterRule(DmIcgProcVo dmIcgProcVo);
    Integer getConfContractRuleDef(DmIcgProcVo dmIcgProcVo);
    void updateMajorRiskAdapterNoRuleDate(DmIcgProcVo dmIcgProcVo);
    void updateMajorRiskBussNoPass(DmIcgProcVo dmIcgProcVo);
    /**
     * 重大风险测试配置+规则方案,查不到配置或配置为否默认全部通过
     */
    void updateMajorRiskNoConfPass(DmIcgProcVo dmIcgProcVo);

    /**
     * 重大风险测试不通过的处理方案
     */
    void majorRiskNoPass(DmIcgProcVo dmIcgProcVo);
    Integer queryMajorRiskNoPassAndNoIcgGroup(DmIcgProcVo dmIcgProcVo);

    /**
     * 评估方法适配
     */
    void evaluateApproach(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同组合划分
     */
    void portfolioDiscern(DmIcgProcVo dmIcgProcVo);
    Integer currentYearMonthNoEvaluateApproachDate(DmIcgProcVo dmIcgProcVo);
    /**
     * 盈亏判定
     */
    void profitableEstimate(DmIcgProcVo dmIcgProcVo);
    Integer queryNeedProfitableEstimateData(DmIcgProcVo dmIcgProcVo);
    void updateProfitableEstimateInit(DmIcgProcVo dmIcgProcVo);
    void updateProfitableEstimateAdapterConfig(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同确认
     */
    void updateIcgBorderSeal(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同组划分
     */
    void icgDiscern(DmIcgProcVo dmIcgProcVo);

    /**
     * 投成拆分
     */
    void icgInvestment(DmIcgProcVo dmIcgProcVo);
    Integer getCurrentYearMonthIcgGroupCount(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同分组完成
     */
    void icgConfirm(DmIcgProcVo dmIcgProcVo);


    DmBussCmunitTreaty findById(Long cmUnitId);

    Page<DmBussCmunitDirectSelectVo> findDataRecord(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo, Pageable pageParam);

    DmBussCmunitTreaty findCmunitVoByqueryMethod(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo);

    String getCmunitInfoById(DmCmunitMajorTestVo dmCmunitMajorTestVo);

    DmCmunitMajorTestVo getDmCmunitMajorTestVo(Long cmunitId);

    Long getCmunitMajorTestCount(DmIcgProcVo dmIcgProcVo);

    List<DmCmunitTreatyMajorTestVo> listCmunitTreatyMajorTestInfo(DmIcgProcVo dmIcgProcVo);

    void updateMajorResultByCmunitId(@Param("item") DmCmunitTreatyMajorTestVo dmCmunitTreatyMajorTestVos);

    List<DmBussCmunitTreatyInward> findBussCmunitConf(Long entityId);

    void updateMajorResultNull(DmIcgProcVo dmIcgProcVo);
}
