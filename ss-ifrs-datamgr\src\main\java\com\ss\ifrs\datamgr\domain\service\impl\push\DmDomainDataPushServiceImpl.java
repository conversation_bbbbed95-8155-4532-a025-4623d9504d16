package com.ss.ifrs.datamgr.domain.service.impl.push;

import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.domain.exception.DmVerifyException;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.domain.service.push.DmDomainDataPushService;
import com.ss.ifrs.datamgr.feign.AtrDapDataClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodDetailVo;
import com.ss.ifrs.datamgr.pojo.push.DataPushAccVo;
import com.ss.ifrs.datamgr.pojo.push.DataPushVo;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodDetailService;
import com.ss.ifrs.datamgr.service.push.DmBussDataPushAccService;
import com.ss.ifrs.datamgr.service.push.DmBussDataPushAtrService;
import com.ss.ifrs.datamgr.service.push.DmBussDataPushExpService;
import com.ss.library.thread.ThreadUtils;
import com.ss.library.utils.DateUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ThreadConstant;
import com.ss.platform.pojo.bbs.vo.BbsConfModelMappingVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import com.ss.platform.util.DataSliceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DmDomainDataPushServiceImpl implements DmDomainDataPushService {

    @Autowired
    private DmBussDataPushExpService dmBussDataPushExpService;

    @Autowired
    private DmBussDataPushAtrService dmBussDataPushAtrService;

    @Autowired
    private DmBussDataPushAccService dmBussDataPushAccService;

    @Autowired
    private DmConfBussPeriodDetailService dmConfBussPeriodDetailService;

    @Qualifier(CommonConstant.BussPeriod.PeriodStatus.COMPLETED)
    @Autowired
    private DmDomainPeriodService dmDomainPeriodService;

    @Autowired
    private AtrDapDataClient atrDapDataClient;

    @Autowired
    private AppConfig appConfig;

    @Override
    public void expDataPush(DataPushVo vo) {
        //业务期间详情判断,已准备直接返回,计量单元未完成返回
        DmConfBussPeriodDetailVo dmConfBussPeriodDetailVo = new DmConfBussPeriodDetailVo();
        dmConfBussPeriodDetailVo.setDirection(DmConstant.DirectionType.OUT_PUT);
        dmConfBussPeriodDetailVo.setBizCode(DmConstant.BizCodeOutModel.OUT_MODEL_PUSH_DATA_TO_EXP);
        dmConfBussPeriodDetailVo.setYearMonth(vo.getYearMonth());
        DmConfBussPeriodDetailVo periodDetailVo = dmConfBussPeriodDetailService.findByBizCodeAndYearMonthAndDirection(dmConfBussPeriodDetailVo);
        if (null == periodDetailVo || !periodDetailVo.getReadyState().equals(CommonConstant.BussPeriod.PeriodStatus.PREPARING)) {
            return;
        }

        //查询费用分摊数据推送计划类型
        String expSyncPlanType = dmBussDataPushExpService.getExpSyncPlanType(vo);
        if (StringUtils.isBlank(expSyncPlanType)) {
            expSyncPlanType = DmConstant.ExpSyncType.PLAN_TYPE_L;
        }

        switch (expSyncPlanType) {
            case DmConstant.ExpSyncType.PLAN_TYPE_L:
                //待摊数据同步来源与科目余额
                dmBussDataPushExpService.pushExpFinancialDataByLedger(vo);
                break;
            case DmConstant.ExpSyncType.PLAN_TYPE_A:
                //待摊数据同步来源与科目余额和专项余额
                dmBussDataPushExpService.pushExpFinancialDataByArticle(vo);
                break;
        }

        //推送计量单元数据
        dmBussDataPushExpService.pushExpCmUnitData(vo);

        //同步完成后修改业务期间详情状态
        dmDomainPeriodService.executionDetail(vo.getEntityId(), vo.getYearMonth(), DmConstant.BizCodeOutModel.OUT_MODEL_PUSH_DATA_TO_EXP);

    }

    @Override
    public void atrDataPush(DataPushVo vo) {
        List<Runnable> runnableList = new ArrayList<>();
        atrDapDataClient.cleanDapData("atr_dap_dd_unit",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_dd_unit_ext",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_dd_payment_plan",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_dd_paid",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_ti_paid",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_fo_paid",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_to_paid_t",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_to_paid_x",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_treaty",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_dd_os",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_dd_loss",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_ti_os",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_fo_os",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_to_os_t",vo.getYearMonth());
        atrDapDataClient.cleanDapData("atr_dap_xo_os",vo.getYearMonth());

        List<Exception> exceptions = ThreadUtils.execute(runnableList, ThreadConstant.RULE_THREAD_POOL, true);
        if (!exceptions.isEmpty()) {
            throw new RuntimeException(exceptions.get(0));
        }
        runnableList = new ArrayList<>();

        //单数据（直保&临分分入）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapDDUnit(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //更新(直保&临分分入)
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.mergeAtrDapDDUnit(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //单数据（直保&临分分入） 扩展表
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapDDUnitExt(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //缴费计划
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapDDPaymentPlan(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //实收实付接口表（直保）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapDDPaid(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());


        //实收实付接口表（临分分出）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapFOPaid(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //实收实付接口表（合约分入）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapTIPaid(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //实收实付接口表（比例合约分出）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapTOPaidT(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //实收实付接口表（非比例合约分出）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapTOPaidX(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //合约信息
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapTreaty(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //已发生已报告未决（直保&临分分入）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapDDLicOs(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //已决赔款（直保&临分分入）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapDDLoss(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //已发生已报告未决（合约分入）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapTILicOs(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //已发生已报告未决（临分分出）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapFOLicOs(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //已发生已报告未决（比例合约分出）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapTOLicOsT(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        //已发生已报告未决赔款（非比例合约分出）
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAtrService.insertAtrDapXOLicOs(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));}, appConfig.getDataSliceQuantity());

        exceptions = ThreadUtils.execute(runnableList, ThreadConstant.RULE_THREAD_POOL, true);
        if (!exceptions.isEmpty()) {
            throw new RuntimeException(exceptions.get(0));
        }
        dmDomainPeriodService.executionDetail(vo.getEntityId(), vo.getYearMonth(), DmConstant.BizCodeOutModel.OUT_MODEL_PUSH_DATA_TO_ATR);


    }

    @Override
    public void accDataPush(DataPushVo vo) {
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushAccPayment(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushDDCmunitInfo(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        },  appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushAdvancePremiumDDInfo(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        },  appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushFOCmunitInfo(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushTICmunitInfo(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushTOCmunitInfo(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());
        dmDomainPeriodService.executionDetail(vo.getEntityId(), vo.getYearMonth(), DmConstant.BizCodeOutModel.OUT_MODEL_PUSH_DATA_TO_ACC);
    }

    @Override
    public void accDataPushDiary(DataPushAccVo vo) {

        BbsConfModelMappingVo bplConfModelMappingSoureColumn = dmBussDataPushAccService.getBplConfModelMappingSoureColumn();
        vo.setSourceColumn(bplConfModelMappingSoureColumn.getSourceColumn());
        vo.setMappingColumn(bplConfModelMappingSoureColumn.getMappingColumn());

        // 同步凭证主表
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushFinVoucher(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());

        // 同步凭证明细表
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmBussDataPushAccService.pushFinVoucherDetail(vo, SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());
    }
}
