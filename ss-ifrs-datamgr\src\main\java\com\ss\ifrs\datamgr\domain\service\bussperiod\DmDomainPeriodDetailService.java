package com.ss.ifrs.datamgr.domain.service.bussperiod;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;

import java.util.List;

public interface DmDomainPeriodDetailService {

    void addByConfTable(Long entityId, String yearmonth);

    void update(BussPeriodReqVo bussPeriodReqVo);

    Boolean checkExistsPreparingInput(Long entityId, String yearMonth);

    Boolean checkExistsPreparingOutput(Long entityId, String yearMonth);

    List<String> listPreparingInputModel(Long entityId,String yearMonth);
}