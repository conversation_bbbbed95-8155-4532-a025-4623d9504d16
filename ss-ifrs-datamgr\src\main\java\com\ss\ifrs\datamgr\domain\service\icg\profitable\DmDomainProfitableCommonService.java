package com.ss.ifrs.datamgr.domain.service.icg.profitable;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;

public interface DmDomainProfitableCommonService {

    /*
     *QR002-非跟单IACF%,QR003-预期维持费用率%,BE013-预期赔付率%,QR008-非金融风险调整%
     */
    void prepareQuota(DmIcgProcVo dmIcgProcVo);

    /*
     *QP001-预期赔付模式%; D005-发展期利率
     */
    void prepareDevelopQuota(DmIcgProcVo dmIcgProcVo);

    /*
     *loss_rate压力情景损失率
     */
    void prepareStressLossRate(DmIcgProcVo dmIcgProcVo);

    /*
     *D005 无风险曲线率
     */
    void prepareInterestRate(DmIcgProcVo dmIcgProcVo);

    /*
     *检查计算因子是否完整
     */
    void checkFactor(DmIcgProcVo dmIcgProcVo);
}
