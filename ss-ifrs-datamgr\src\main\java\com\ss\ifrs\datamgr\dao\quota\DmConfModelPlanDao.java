/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-03-08 14:48:09
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;
 
import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfModelPlan;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfModelPlanVo;

import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-03-08 14:48:09<br/>
 * Description: 计量模型方案配置 Dao类<br/>
 * Related Table Name: dm_conf_model_plan<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmConfModelPlanDao extends IDao<DmConfModelPlan, Long> {

    List<DmConfModelPlanVo> findListByVo(Map<String, Object> map);

    List<DmConfModelPlanVo> findModelClassByCenterId(Long id);

    Long findModelDefIdByPlan(DmConfModelPlanVo dmConfModelPlanVo);

    /*
    * 根据模型方案，进行统计未配置假设值的业务线
    * */
    Integer countLoaNotQuotaConfigByModelPlan(DmConfModelPlanVo dmConfModelPlanVo);

}