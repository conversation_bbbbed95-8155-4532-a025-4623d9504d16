-- endorse_type_code特殊处理功能示例SQL

-- 1. 查看包含15或16的保单数据示例
SELECT 
    policy_no,
    endorse_seq_no,
    kind_code,
    endorse_type_code,
    year_month,
    premium,
    effective_date,
    expiry_date
FROM atr_dap_dd_unit 
WHERE entity_id = 1001
  AND endorse_type_code IS NOT NULL
  AND (endorse_type_code LIKE '%15%' OR endorse_type_code LIKE '%16%')
ORDER BY policy_no, year_month;

-- 2. 查看特定保单的所有批改记录
SELECT 
    policy_no,
    endorse_seq_no,
    endorse_type_code,
    year_month,
    premium,
    approval_date
FROM atr_dap_dd_unit 
WHERE policy_no = 'POLICY_EXAMPLE_001'
ORDER BY year_month, endorse_seq_no;

-- 3. 验证特殊处理后的计算结果
-- 查看ICU表中的计算结果
SELECT 
    u.policy_no,
    u.endorse_seq_no,
    u.kind_code,
    u.year_month,
    u.premium,
    u.cur_ed_premium,
    u.pre_cuml_ed_premium,
    u.remaining_months
FROM atr_buss_dd_lrc_u u
WHERE u.policy_no IN (
    SELECT DISTINCT policy_no 
    FROM atr_dap_dd_unit 
    WHERE endorse_type_code LIKE '%15%' OR endorse_type_code LIKE '%16%'
)
ORDER BY u.policy_no, u.year_month;

-- 4. 查看发展期数据
SELECT 
    d.main_id,
    u.policy_no,
    u.endorse_seq_no,
    u.year_month,
    d.dev_no,
    d.ed_premium,
    d.ed_rate,
    d.recv_premium
FROM atr_buss_dd_lrc_u u
JOIN atr_buss_dd_lrc_u_dev d ON u.id = d.main_id
WHERE u.policy_no IN (
    SELECT DISTINCT policy_no 
    FROM atr_dap_dd_unit 
    WHERE endorse_type_code LIKE '%15%' OR endorse_type_code LIKE '%16%'
)
ORDER BY u.policy_no, u.year_month, d.dev_no;

-- 5. 数据验证查询
-- 验证特殊处理的保单是否只有第0期数据
WITH special_policies AS (
    SELECT DISTINCT policy_no 
    FROM atr_dap_dd_unit 
    WHERE endorse_type_code LIKE '%15%' OR endorse_type_code LIKE '%16%'
),
dev_summary AS (
    SELECT 
        u.policy_no,
        u.year_month,
        COUNT(d.dev_no) as dev_count,
        MAX(d.dev_no) as max_dev_no,
        SUM(d.ed_premium) as total_ed_premium
    FROM atr_buss_dd_lrc_u u
    JOIN atr_buss_dd_lrc_u_dev d ON u.id = d.main_id
    WHERE u.policy_no IN (SELECT policy_no FROM special_policies)
    GROUP BY u.policy_no, u.year_month
)
SELECT 
    policy_no,
    year_month,
    dev_count,
    max_dev_no,
    total_ed_premium,
    CASE 
        WHEN max_dev_no = 0 THEN '只有第0期'
        WHEN max_dev_no > 0 THEN '有多个发展期'
        ELSE '无发展期'
    END as processing_type
FROM dev_summary
ORDER BY policy_no, year_month;

-- 6. 业务规则验证
-- 验证第0期已赚金额 = 签单保费 - 历史累计已赚的计算是否正确
SELECT 
    u.policy_no,
    u.year_month,
    u.premium as sign_premium,
    u.pre_cuml_ed_premium as hist_cuml_ed,
    u.cur_ed_premium as cur_ed,
    (u.premium - u.pre_cuml_ed_premium) as expected_cur_ed,
    CASE 
        WHEN ABS(u.cur_ed_premium - (u.premium - u.pre_cuml_ed_premium)) < 0.01 
        THEN '计算正确' 
        ELSE '计算异常' 
    END as validation_result
FROM atr_buss_dd_lrc_u u
WHERE u.policy_no IN (
    SELECT DISTINCT policy_no 
    FROM atr_dap_dd_unit 
    WHERE endorse_type_code LIKE '%15%' OR endorse_type_code LIKE '%16%'
)
  AND u.year_month = '202312' -- 替换为实际的评估期
ORDER BY u.policy_no;
