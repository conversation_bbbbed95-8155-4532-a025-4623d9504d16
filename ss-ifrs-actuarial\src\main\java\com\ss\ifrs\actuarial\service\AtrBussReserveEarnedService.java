package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveEarnedVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveEarnedDetailVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import java.util.List;

public interface AtrBussReserveEarnedService {

    void reserveEarnedExtract(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Long userId);

    /**
     * @description: upr主表查询
     * @param : [atrBussReserveEarnedVo,pageParam]
     * @return: Page<AtrBussReserveEarnedVo>
     * @author: wuyh.
     * @createTime: 2021/3/10 18:38
     */
    Page<AtrBussReserveEarnedVo> searchPage(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Pageable pageParam);


    Page<AtrBussReserveEarnedDetailVo> findForDataTables(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Pageable pageParam);

    //校验是否有确认的计量版本
    Boolean checkIsConfirm(AtrBussReserveEarnedVo atrBussReserveEarnedVo);

    //计量版本确认
    void confirm(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Long userId);

//    void downloadDataFile(HttpServletResponse response, AtrBussReserveEarnedVo atrBussReserveEarnedVo) throws IOException;

    List<AtrBussReserveEarnedDetailVo> findDownloadList(AtrBussReserveEarnedVo atrBussReserveEarnedVo);

    //已赚准备金版本删除
    void delete(Long reserveEarnedId, Long userId);
}
