package com.ss.ifrs.datamgr.domain.service.impl.bussperiod;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.domain.abst.DmDomainPeriod;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodDetailService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfBussPeriod;
import com.ss.library.utils.CacheUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service(CommonConstant.BussPeriod.PeriodStatus.COMPLETED)
public class DmDomainPeriodCompletedServiceImpl extends DmDomainPeriod implements DmDomainPeriodService {
    final Logger LOG = LoggerFactory.getLogger(getClass());

    String state = CommonConstant.BussPeriod.PeriodStatus.COMPLETED;
    Long userId = 1L;

    @Autowired
    private DmDomainBussPeriodService domainBussPeriodService;

    @Autowired
    private DmDomainPeriodDetailService bussPeriodDetailService;

    @Autowired
    private DmDomainPeriodProcessingServiceImpl bussPeriodProcessingService;

    /**
     *
     */
    @Override
    public void execution(Long entityId, String yearMonth) {

        /**
         *执行逻辑：
         * 1、判断是否存在执行中业务期间
         * 1.1、如否，退出并提示
         * 1.2、如是：
         * 2、判断是否所有明细为已准备
         * 2.1、如否，退出并提示
         * 2.2、如是：调整当前业务期间为已完成；
         * */

        // 检验是否存在执行中业务期间
        String checkYearMonth = domainBussPeriodService.getYearMonth(entityId,yearMonth,CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
        if (StringUtil.isEmpty(checkYearMonth)) {
            return;
            //throw new BusinessException(ResponseEnum.ERROR_PERIOD_NOT_PROCESS_TO_COMPLETED);
        }

        // 验证所有输出明细数据状态是否己完成
        if (bussPeriodDetailService.checkExistsPreparingOutput(entityId, checkYearMonth)) {
            return;
            //throw new BusinessException(ResponseEnum.ERROR_PERIOD_DETAIL_OUTPUT_NOT_READY);
        }

        DmConfBussPeriod confBussPeriod = new DmConfBussPeriod();
        confBussPeriod.setEntityId(entityId);
        confBussPeriod.setYearMonth(checkYearMonth);
        confBussPeriod.setPeriodState(this.state);
        confBussPeriod.setUpdatorId(this.userId);
        this.update(confBussPeriod);

        //业务期间完成后，执行新的业务期间
        bussPeriodProcessingService.execution(entityId, null);
    }

    @Override
    public void executionDetail(Long entityId, String yearMonth, String bizCode) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setYearMonth(yearMonth);
        bussPeriodReqVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.READIED);
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.OUTPUT);
        bussPeriodReqVo.setUpdatorId(this.userId);
        bussPeriodReqVo.setUpdateTime(new Date());
        bussPeriodDetailService.update(bussPeriodReqVo);

        CacheUtil.remove(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.DmIdentity.APP_CODE, entityId + bizCode);
        //明细数据已完成，调整业务期间为已完成
        execution(entityId, yearMonth);
    }
}
