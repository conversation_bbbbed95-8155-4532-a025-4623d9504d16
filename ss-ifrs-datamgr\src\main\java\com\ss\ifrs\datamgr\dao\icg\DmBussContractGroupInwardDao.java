package com.ss.ifrs.datamgr.dao.icg;

import com.ss.ifrs.datamgr.pojo.stat.po.DmDataRecord;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmMajorRiskSolveVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmBussContractGroupInwardDao extends IDao<DmDataRecord, Long> {
    // 重大风险测试
    void majorRisk(Long entityId, String bussYearMonth);

    // 合同组合
    void contractPortfolio(Long entityId, String bussYearMonth);

    //盈亏判定
    void contractProfitable(Long entityId, String bussYearMonth);

    // 合同分组
    void contractGroup(Long entityId, String bussYearMonth);

    String checkPortfolioIsFinish(Long entityId, String bussYearMonth);

    String checkIcgIsFinish(Long entityId, String bussYearMonth);

    void updateMajorRiskByVo(DmMajorRiskSolveVo dmMajorRiskSolveVo);

    String checkMeasurementUnitIsHave(Long entityId, String bussYearMonth);

    void evalDefEvalMethod(Long entityId, String bussYearMonth );

    //新计量单元
    void contractMeteringUnit(Long entityId, String bussYearMonth);

    void contractInsurance(Long entityId, String bussYearMonth);

    void confirmYearMonth(Long entityId, String bussYearMonth);

    //重大风险业务不通过
    void majorRiskNoPass(Long entityId, String bussYearMonth);

    String checkMajorRiskIsFinish(Long entityId, String bussYearMonth);

    String checkEvaluateApproachIsFinish(Long entityId, String bussYearMonth);

    String checkProfitIsFinish(Long entityId, String bussYearMonth);

    String checkIcgConfirmIsFinish(Long entityId, String bussYearMonth);

    String checkConfirmIsFinish(Long entityId, String bussYearMonth);

}
