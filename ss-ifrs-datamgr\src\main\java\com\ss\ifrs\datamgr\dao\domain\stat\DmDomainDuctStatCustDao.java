package com.ss.ifrs.datamgr.dao.domain.stat;

import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelStatResultVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmCheckOdsTotalVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmParingStatVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmStatNeedAmountVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmStatVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DmDomainDuctStatCustDao {

    /**
     * 查询模型是否有金额字段统计
     *
     * @param bizTypeId
     * @return
     */
    DmStatNeedAmountVo queryIsNeedStatAmount(@Param("bizTypeId") Long bizTypeId);

    Integer queryTableHaveEntityCode(@Param("tableName") String tableName);

    /**
     *
     * @param vo
     * @return
     */
    Integer checkOdsTableDataTotal(@Param("vo") DmCheckOdsTotalVo vo);

    /**
     * 清除统计old数据
     * @param statTableName
     * @param taskCode
     * @param entityCode
     * @return
     */
    Integer cleanOldStatData(@Param("statTableName") String statTableName, @Param("taskCode") String taskCode, @Param("entityCode") String entityCode);

    /**
     * 获取统计、group by、order by 字段
     * @param bizTypeId
     * @return
     */
    DmStatVo queryStatField(@Param("bizTypeId") Long bizTypeId);

    /**
     * 获取汇总字段
     * @param bizTypeId
     * @return
     */
    DmStatVo querySumField(@Param("bizTypeId") Long bizTypeId);

    /**
     * 统计数量
     * @param vo
     * @return
     */
    Integer statCount(@Param("vo") DmStatVo vo);

    /**
     * 统计金额
     * @param vo
     * @return
     */
    Integer statAmount(@Param("vo") DmStatVo vo);

    /**
     * 查询需要统计的信号
     * @param vo
     * @return
     */
    List<DmParingStatVo> queryStatSignal(@Param("vo") DmParingStatVo vo);

    DapModelStatResultVo getStatStatusCount(String tableName, String taskCode);
}
