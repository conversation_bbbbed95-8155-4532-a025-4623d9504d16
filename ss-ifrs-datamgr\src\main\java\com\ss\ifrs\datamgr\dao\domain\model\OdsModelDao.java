package com.ss.ifrs.datamgr.dao.domain.model;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelConditionVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OdsModelDao {
    Integer updateFailStatus(DmBussDataVerifyVo dmBussDataVerifyVo, SliceAttributes sliceParam);

    Integer updateSuccessStatus(DmBussDataVerifyVo dmBussDataVerifyVo,SliceAttributes sliceParam);

}
