package com.ss.ifrs.datamgr.dao.push;

import com.ss.ifrs.datamgr.pojo.push.DataPushAccVo;
import com.ss.ifrs.datamgr.pojo.push.DataPushVo;
import com.ss.platform.pojo.bbs.vo.BbsConfModelMappingVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface DmBussDataPushAccDao {
    BbsConfModelMappingVo getBplConfModelMappingSoureColumn();

    void pushAccPayment(@Param("dataPushAccVo")DataPushVo dataPushAccVo, SliceAttributes sliceParam);

    void updateRiskEvaluate(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void updateTreatyEvaluate(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void updateAccidentDateByClaimLoss(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void updateAccidentDateByRiStatementNo(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void updateAccDapEntryData(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void updateCurrentPreviousIs(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void pushLedgerBalance(DataPushAccVo dataPushVo, SliceAttributes sliceParam);

    void pushArticleBalance(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void pushFinVoucher(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void pushFinVoucherDetail(DataPushAccVo dataPushAccVo, SliceAttributes sliceParam);

    void pushDDCmunitInfo(@Param("dataPushAccVo") DataPushVo dataPushAccVo, SliceAttributes sliceParam);

    void pushAdvancePremiumDDInfo(@Param("dataPushAccVo") DataPushVo dataPushAccVo, SliceAttributes sliceParam);

    void pushFOCmunitInfo(@Param("dataPushAccVo") DataPushVo dataPushAccVo, SliceAttributes sliceParam);

    void pushTOCmunitInfo(@Param("dataPushAccVo") DataPushVo dataPushAccVo, SliceAttributes sliceParam);

    void pushTICmunitInfo(@Param("dataPushAccVo") DataPushVo dataPushAccVo, SliceAttributes sliceParam);
}
