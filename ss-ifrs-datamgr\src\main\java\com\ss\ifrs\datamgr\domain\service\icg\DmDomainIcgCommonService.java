package com.ss.ifrs.datamgr.domain.service.icg;

public interface DmDomainIcgCommonService {

    String getFactorValueCal(Long cmUnitId, Long entityId, String bussDefCode, Long ruleId, String ruleType,
                             String businessModel);



    void insertBussCmUnitDtlLog(String traceNo, String traceCode, String traceStatus, String traceMsg);

    void backupProfitUnitResultTab(Long entityId, String yearMonth, String tableName);

    // 同步盈亏规则参数
    void syncProfitableRuleParam();
}
