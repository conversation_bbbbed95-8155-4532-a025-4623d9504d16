package com.ss.ifrs.actuarial.service;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveOsDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveOsVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AtrBussReserveOsService {
    void reserveOsExtract(AtrBussReserveOsVo atrBussReserveOsVo, Long userId);

    Page<AtrBussReserveOsVo> searchPage(AtrBussReserveOsVo atrBussReserveOsVo, Pageable pageParam);

    Boolean checkIsConfirm(AtrBussReserveOsVo atrBussReserveOsVo);

    void confirm(AtrBussReserveOsVo atrBussReserveOsVo, Long userId);

    Page<AtrBussReserveOsDetailVo> findForDataTables(AtrBussReserveOsVo atrBussReserveOsVo, Pageable pageParam);

    void downloadDataFile(HttpServletResponse response, AtrBussReserveOsVo atrBussReserveOsVo) throws IOException;

    List<AtrBussReserveOsVo> findOsDownloadList(AtrBussReserveOsVo atrBussReserveOsVo);

    Map<String, Object> getDownloadAllStaffWorkers(JSONObject jsonParam) throws Exception;

    HSSFWorkbook excelDownload(HttpServletResponse response) throws IOException;

    //os准备金版本删除
    void delete(Long reserveOsId, Long userId);
}
