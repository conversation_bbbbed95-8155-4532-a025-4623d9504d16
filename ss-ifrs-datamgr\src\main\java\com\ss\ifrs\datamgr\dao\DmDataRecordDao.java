package com.ss.ifrs.datamgr.dao;


import com.ss.ifrs.datamgr.pojo.stat.po.DmDataRecord;
import com.ss.ifrs.datamgr.pojo.stat.vo.DmDataRecordVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
/**
 * Create Date: 2020-12-19 11:26:15<br/>
 * Description: 数据管理-数据记录 Dao类<br/>
 * Related Table Name: DM_DATA_RECORD<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmDataRecordDao extends IDao<DmDataRecord, Long> {

    Page<DmDataRecordVo> findDataRecord(DmDataRecordVo dmDataRecordVo, Pageable pageParam);

    void saveDataRecordMap(Map<String, Object> map);

    DmDataRecordVo findDataRecordById(Long recordId);

    void saveTargetList(Map<String, Object> map);

    Long findStatisticsData(DmDataRecordVo dmDataRecordVo);

    Long findColumnExist(@Param("tableName") String tableName, @Param("columnName") String columnName);
}