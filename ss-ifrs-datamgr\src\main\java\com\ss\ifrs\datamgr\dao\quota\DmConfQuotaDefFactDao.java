/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;
 

import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfQuotaDefFact;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 假设配置关联业务模型轨迹表 Dao类<br/>
 * Related Table Name: ATR_CONF_QUOTA_DEF_FACT<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmConfQuotaDefFactDao extends IDao<DmConfQuotaDefFact, Long> {

    List<DmConfQuotaDefFact> findByDefId(Long quotaDefId);
    List<Long> findIdByDefId(Long quotaDefId);
}