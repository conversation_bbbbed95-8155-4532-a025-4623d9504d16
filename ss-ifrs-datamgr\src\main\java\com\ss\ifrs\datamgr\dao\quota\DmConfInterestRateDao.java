/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2021-02-24 11:48:16
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;

import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfInterestRate;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfInterestRateVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;


/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2021-02-24 11:48:16<br/>
 * Description: 无风险利率曲线配置 Dao类<br/>
 * Related Table Name: ATR_CONF_INTEREST_RATE<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmConfInterestRateDao extends IDao<DmConfInterestRate, Long> {

    Page<DmConfInterestRateVo> fuzzySearchPage(DmConfInterestRateVo dmConfInterestRateVo, Pageable pageParam);

    DmConfInterestRateVo findByInterestRateId(Long interestRateId);

}