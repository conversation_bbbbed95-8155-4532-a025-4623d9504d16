DELETE from accuser.acc_conf_code where code_code = 'BorderIs';
INSERT INTO accuser.acc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('acc_seq_conf_code'), 513, 'BorderIs', '合同初始确认标识', '合同初始确认标识', '合同初始确认标识', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);

DELETE from acc_conf_model_code where model_code_id = 393;
INSERT INTO accuser.acc_conf_model_code (model_code_id, model_code_code, model_code_c_name, model_code_l_name, model_code_e_name, model_column_type, model_column_code, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, serial_no) VALUES (393, 'BorderIs', '合同初始确认标识', '合同初始确认标识', '合同初始确认标识', '1', 'border_is', '1', '1', 1, '2023-09-12 14:48:21', NULL, 1, '2023-09-12 14:48:21', 1, '2025-07-09 14:23:06.939', 2);



delete from acc_conf_entryrule_detail t where entry_rule_id in (14452,12620,14811);
delete from acc_conf_entryrule t where t.scenario_id in (12620,
14528,
14806);
delete from acc_conf_scenario_modelref t where t.scenario_id in (12620,
14528,
14806);
delete from acc_conf_scenario t where t.scenario_code in ('DA2101001',
'DP1107001',
'DP1107010');

INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (12620, 'DP1107010', '直接业务-保费收款处理-保费-0-初始确认', '直接业务-保费收款处理-保费-0-初始确认', '直接业务-保费收款处理-保费-0-初始确认', 1, 'b.BUSINESS_SOURCE_CODE = ''DB'' and b.EXPENSES_TYPE_CODE = ''R10'' and b.VOUITEM = ''1107'' and  (case when B.EXTEND_COLUMN1 in (''2'',''4'') then ''2'' else B.EXTEND_COLUMN1 end)  = ''2'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN 50 THEN ''03'' ELSE ''01'' END ) = ''01'' and b.POSTING_TYPE_CODE = ''11'' and b.border_is = ''1''', '业务-保费/佣金', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.141', 1, '2025-07-08 14:07:13.901', 46);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14528, 'DP1107001', '直接业务-保费收款处理-保费-0', '直接业务-保费收款处理-保费-0', '直接业务-保费收款处理-保费-0', 5, 'b.BUSINESS_SOURCE_CODE = ''DB'' and b.EXPENSES_TYPE_CODE = ''R10'' and b.VOUITEM = ''1107'' and  (case when B.EXTEND_COLUMN1 in (''2'',''4'') then ''2'' else B.EXTEND_COLUMN1 end)  = ''2'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN 50 THEN ''03'' ELSE ''01'' END ) = ''01'' and b.POSTING_TYPE_CODE = ''11'' and  coalesce(B.BORDER_IS,''0'')  = ''0''', '业务-保费/佣金', '1', '1', 1, '2025-07-08 14:07:14.141', '', 1, '2025-04-30 18:10:55.622', 1, '2025-07-08 14:07:14.141', 46);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14806, 'DA2101001', '直接业务-保费预转实挂帐-保费', '直接业务-保费预转实挂帐-保费', '直接业务-保费预转实挂帐-保费', 5, 'b.BUSINESS_SOURCE_CODE = ''DB'' and b.EXPENSES_TYPE_CODE = ''R10'' and b.VOUITEM = ''2101'' and  coalesce(B.EXTEND_COLUMN1,''0'')  = ''0'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN 50 THEN ''03'' ELSE ''01'' END ) = ''01'' and b.POSTING_TYPE_CODE = ''21''', '业务-保费/佣金', '1', '1', 1, '2025-07-08 14:07:14.141', '', 1, '2025-04-30 18:12:25.913', 1, '2025-07-08 14:07:14.141', 46);


INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180249, 14806, 66, 'DB', NULL, '1', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180250, 14806, 67, 'R10', NULL, '2', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180251, 14806, 311, '2101', NULL, '3', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180252, 14806, 63, '0', NULL, '4', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180253, 14806, 70, '01', NULL, '5', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180254, 14806, 69, '21', NULL, '6', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180255, 14528, 66, 'DB', NULL, '1', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180256, 14528, 67, 'R10', NULL, '2', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180257, 14528, 311, '1107', NULL, '3', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180258, 14528, 63, '2', NULL, '4', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180259, 14528, 70, '01', NULL, '5', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180260, 14528, 69, '11', NULL, '6', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180261, 14528, 393, '0', NULL, '7', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180262, 12620, 66, 'DB', NULL, '1', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180263, 12620, 67, 'R10', NULL, '2', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180264, 12620, 311, '1107', NULL, '3', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180265, 12620, 63, '2', NULL, '4', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180266, 12620, 70, '01', NULL, '5', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180267, 12620, 69, '11', NULL, '6', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');
INSERT INTO accuser.acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (180268, 12620, 393, '1', NULL, '7', '1', '1', 1, '2025-07-08 14:07:13.901', '', 1, '2025-07-08 14:07:14.248', 1, '2025-07-08 14:07:13.901');

INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14452, 1, 'BookI17', 14528, NULL, NULL, NULL, NULL, '1', '2025-07-08 14:07:13.901', 1, '1', NULL, '2025-04-30 18:10:55.689', 1, '2025-07-08 14:07:13.901', 1, 5);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (12620, 1, 'BookI17', 12620, NULL, NULL, NULL, NULL, '1', NULL, NULL, '1', NULL, '2025-07-08 14:07:14.196', 1, '2025-07-08 14:07:13.901', 1, NULL);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14811, 1, 'BookI17', 14806, NULL, NULL, NULL, NULL, '1', '2025-07-08 14:07:13.901', 1, '1', NULL, '2025-04-30 18:12:25.923', 1, '2025-07-08 14:07:13.901', 1, 5);


INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (32300, 14811, 1, 1940077, 1940071, NULL, '2025-07-08 14:07:14.325', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (32301, 14452, 1, 1939116, NULL, NULL, '2025-07-08 14:07:14.325', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (32302, 12620, 1, 1940071, 1939116, NULL, '2025-07-08 14:07:14.325', 1, NULL, NULL);

update acc_conf_entryrule_detail b set account_id_dr = 1940164, account_id_cr = 1940165 where b.entry_rule_id in (
SELECT entry_rule_id FROM acc_conf_entryrule a where a.scenario_id in (
 SELECT scenario_id from acc_conf_scenario t where t.scenario_code  in ('PAA0130','PAA0131')
));