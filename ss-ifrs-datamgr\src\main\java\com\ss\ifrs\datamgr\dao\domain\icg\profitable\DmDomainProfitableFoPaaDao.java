package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

public interface DmDomainProfitableFoPaaDao {

    /*
     *S001,S002集合保费，费用
     */
    void calCmSetAmount();

    /*
     *跟單IACF率
     */
    void calIacfRate();

    /*
     *预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
     */
    void calCmUnitExpectFutureAmount();

    /*
     *预期赔付现金流现值
     */
    void calCmUnitExpectLossAmount();

    /*
     *集合成本率
     */
    void calCmSetCostRate();

    /*
     *集合成本率(非金融风险调整)
     */
    void calCmUnitCostRate();

    /*
     *盈亏结果
     */
    void Profitable();
}
