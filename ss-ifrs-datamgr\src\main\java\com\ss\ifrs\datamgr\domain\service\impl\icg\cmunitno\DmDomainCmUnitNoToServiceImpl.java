package com.ss.ifrs.datamgr.domain.service.impl.icg.cmunitno;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.DmDomainIcgTreatyOutwardDao;
import com.ss.ifrs.datamgr.domain.service.icg.cmunitno.DmDomainCmUnitNoService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmUnitNoAdapterVo;
import com.ss.library.utils.StringUtil;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_TREATY_OUT)
public class DmDomainCmUnitNoToServiceImpl implements DmDomainCmUnitNoService {

    int cnunitNoDigit = 40;

    @Autowired
    private BbsConfEntityFeignClient bbsConfEntityFeignClient;

    @Autowired
    private DmDomainIcgTreatyOutwardDao dmDomainIcgTreatyOutwardDao;

    @Override
    public String getCmUnitNo(DmCmUnitNoAdapterVo cmUnitNoAdapterVo) {
        BbsConfEntityVo entityVo = bbsConfEntityFeignClient.findByEntityId(cmUnitNoAdapterVo.getEntityId());
        StringBuffer sb = new StringBuffer();
        sb.append(entityVo.getEntityCode());
        sb.append(DmConstant.BusinessModel.MODEL_TREATY_OUT);
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment1st())) {
            sb.append(cmUnitNoAdapterVo.getSegment1st());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment2nd())) {
            sb.append(cmUnitNoAdapterVo.getSegment2nd());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment3rd())) {
            sb.append(cmUnitNoAdapterVo.getSegment3rd());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment4th())) {
            sb.append(cmUnitNoAdapterVo.getSegment4th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment5th())) {
            sb.append(cmUnitNoAdapterVo.getSegment5th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment6th())) {
            sb.append(cmUnitNoAdapterVo.getSegment6th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment7th())) {
            sb.append(cmUnitNoAdapterVo.getSegment7th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment8th())) {
            sb.append(cmUnitNoAdapterVo.getSegment8th());
        }
        if (StringUtil.isNotEmpty(cmUnitNoAdapterVo.getSegment9th())) {
            sb.append(cmUnitNoAdapterVo.getSegment9th());
        }
        int lenth = cnunitNoDigit - sb.length();
        Long cmunitNoSeqNo = dmDomainIcgTreatyOutwardDao.getCmunitNoSeqNo();
        sb.append(StringUtil.substring(StringUtil.lpad(cmunitNoSeqNo.toString(), lenth, "0"), 0, lenth));

        return sb.toString();
    }

    /**
     * 计量单元编码： 业务单位 || 合约/临分标志 || 分入/分出标志 || 流水号(000001)
     * @param cmUnitNoAdapterVo
     * @return
     */
    @Override
    public String getCmUnitNoSql(DmCmUnitNoAdapterVo cmUnitNoAdapterVo) {
        BbsConfEntityVo entityVo = bbsConfEntityFeignClient.findByEntityId(cmUnitNoAdapterVo.getEntityId());
        StringBuffer sbSql = new StringBuffer();
        sbSql.append("'");
        sbSql.append(entityVo.getEntityCode());
        sbSql.append(DmConstant.BusinessModel.MODEL_TREATY_OUT);

        String segment1st = dmDomainIcgTreatyOutwardDao.getSegment1st();//Dao.getSegment1st();//由Dao返回脚本形如"to_char(t.effective_date, 'YYYY')";
        if (StringUtil.isNotEmpty(segment1st)) {
            sbSql.append(segment1st);
        }

        String segment2st = dmDomainIcgTreatyOutwardDao.getSegment2nd();//Dao.getSegment2st();//由Dao返回脚本形如“coalesce(substr(t.risk_code, 0, (v_totalnum - coalesce(length(v_unit_no), 0))), '')”
        if (StringUtil.isNotEmpty(segment2st)) {
            sbSql.append(segment2st);
        }

        String segment3rd = dmDomainIcgTreatyOutwardDao.getSegment3rd();
        if (StringUtil.isNotEmpty(segment3rd)) {
            sbSql.append(segment3rd);
        }

        String segment4th = dmDomainIcgTreatyOutwardDao.getSegment4th();
        if (StringUtil.isNotEmpty(segment4th)) {
            sbSql.append(segment4th);
        }

        String segment5th = dmDomainIcgTreatyOutwardDao.getSegment5th();
        if (StringUtil.isNotEmpty(segment5th)) {
            sbSql.append(segment5th);
        }

        String segment6th = dmDomainIcgTreatyOutwardDao.getSegment6th();
        if (StringUtil.isNotEmpty(segment6th)) {
            sbSql.append(segment6th);
        }
        String segment7th = dmDomainIcgTreatyOutwardDao.getSegment7th();
        if (StringUtil.isNotEmpty(segment7th)) {
            sbSql.append(segment7th);
        }

        String segment8th = dmDomainIcgTreatyOutwardDao.getSegment8th();
        if (StringUtil.isNotEmpty(segment8th)) {
            sbSql.append(segment8th);
        }
        String segment9th = dmDomainIcgTreatyOutwardDao.getSegment9th();
        if (StringUtil.isNotEmpty(segment9th)) {
            sbSql.append(segment9th);
        }
        sbSql.append("'");
        String cmunitNoSeqSql = dmDomainIcgTreatyOutwardDao.getCmunitNoSeqSql(); //Dao.getCmunitNoSeqSql()由Dao返回字符串形如:lpad(to_char(dm_seq_direct_cmunitno.nextval, 'FM999999999999999999'), lenth, '0')
        sbSql.append(cmunitNoSeqSql);

        return sbSql.toString();
    }
}
