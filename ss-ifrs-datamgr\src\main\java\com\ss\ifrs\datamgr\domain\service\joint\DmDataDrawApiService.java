package com.ss.ifrs.datamgr.domain.service.joint;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;

/**
 * @className: DmDataDrawApiService
 * @description: 数据采集Api服务接口
 * @author: yinxh.
 * @createTime: 2021/5/7 18:16
 * @version: 1.0
 */
public interface DmDataDrawApiService {
	
	/** 
	 * @description: API方式采集数据
	 * @param : [jsonParams, dmDuctDrawLogVo]
	 * @return: void
	 * @throws:  
	 * @author: yinxh. 
	 * @createTime: 2021/5/7 19:09
	 */ 
	void dataDrawAction(JSONObject jsonParams, DmDataDrawVo dmDataDrawVo) throws Exception;

}
