package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveUprDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveUprVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface AtrBussReserveUprService {

    void reserveUprExtract(AtrBussReserveUprVo atrBussReserveUprVo, Long userId);

    /**
     * @description: upr主表查询
     * @param : [atrBussReserveUprVo,pageParam]
     * @return: Page<AtrBussReserveUprVo>
     * @author: wuyh.
     * @createTime: 2021/3/10 18:38
     */
    Page<AtrBussReserveUprVo> searchPage(AtrBussReserveUprVo atrBussReserveUprVo, Pageable pageParam);


    Page<AtrBussReserveUprDetailVo> findForDataTables(AtrBussReserveUprVo atrBussReserveUprVo, Pageable pageParam);

    //校验是否有确认的upr准备金版本
    Boolean checkIsConfirm(AtrBussReserveUprVo atrBussReserveUprVo);

    //计量版本确认
    void confirm(AtrBussReserveUprVo atrBussReserveUprVo, Long userId);

    void downloadDataFile(HttpServletResponse response, AtrBussReserveUprVo atrBussReserveUprVo) throws IOException;

    List<AtrBussReserveUprDetailVo> findDownloadList(AtrBussReserveUprVo atrBussReserveUprVo);

    //upr准备金版本删除
    void delete(Long reserveUprId, Long userId);
}
