package com.ss.ifrs.datamgr.domain.service.tableDeal;

public interface DmTableDealService {

    /**
     * 创建ODS表
     *
     * @param tableName
     * @param tableComment
     */
    void addOdsTable(String tableName, String tableComment, String schema);

    /**
     * 创建DM表
     *
     * @param tableName
     * @param tableComment
     */
    void addDmTable(String tableName, String tableComment, String schema);

    /**
     * 创建统计表
     *
     * @param tableName
     * @param tableComment
     * @param schema
     */
    void addStatTable(String tableName, String tableComment, String schema);

    /**
     * 修改表字段
     *
     * @param table
     * @param column
     * @param columnType
     * @param columnComment
     * @param schema
     */
    void alterTableAddColumn(String table, String column, String columnType, String columnComment, String schema);

    /**
     * 删除表
     *
     * @param table  表名称
     * @param schema 模式名称
     */
    void dropTable(String schema, String table);

    /**
     * 删除字段
     *
     * @param table
     * @param column
     * @param schema
     */
    void dropColumn(String table, String column, String schema);

    /**
     * 删除序列
     *
     * @param sequence
     * @param schema
     */
    void dropSequence(String sequence, String schema);

    /**
     * 修改表注释
     *
     * @param table
     * @param comment
     * @param schema
     */
    void modifyTableComment(String table, String comment, String schema);


    /**
     * 变更表字段注释
     *
     * @param tableName
     * @param columnName
     * @param comment
     * @param schema
     */
    void modifyTableColumnComment(String tableName, String columnName, String comment, String schema);

    /**
     * 判断字段是否存在
     *
     * @param tableName  表名称
     * @param columnName 列名称
     * @param schema
     * @return
     */
    Boolean isColumnExists(String tableName, String columnName, String schema);

    /**
     * 查询分区表是否存在
     *
     * @param tableName
     * @return
     */
    Boolean checkIfPartitionedTableExists(String tableName);

    /**
     * 创建分区表
     *
     * @param yearMonth
     */
    void createPartitionedTable(String yearMonth);

}
