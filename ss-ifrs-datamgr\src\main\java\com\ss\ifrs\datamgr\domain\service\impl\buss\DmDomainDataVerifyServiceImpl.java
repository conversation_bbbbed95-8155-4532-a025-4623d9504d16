package com.ss.ifrs.datamgr.domain.service.impl.buss;

import com.ss.ifrs.datamgr.annotation.DmTrackDataVerifyProcess;
import com.ss.ifrs.datamgr.annotation.DmTrackProcess;
import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableDao;
import com.ss.ifrs.datamgr.dao.domain.buss.DmDomainDataVerifyDao;
import com.ss.ifrs.datamgr.dao.domain.conf.DmDomainConfCheckRuleDao;
import com.ss.ifrs.datamgr.dao.domain.dap.DmDapModelDao;
import com.ss.ifrs.datamgr.dao.domain.dap.DmDapModelDuctDao;
import com.ss.ifrs.datamgr.dao.domain.joint.DmDomainBussFileExchangeModelDao;
import com.ss.ifrs.datamgr.dao.domain.joint.DmDomainDataPushSignalDao;
import com.ss.ifrs.datamgr.dao.domain.log.DmDomainLogCheckRuleDao;
import com.ss.ifrs.datamgr.dao.domain.log.DmLogDataVerifyDao;
import com.ss.ifrs.datamgr.dao.domain.model.OdsModelDao;
import com.ss.ifrs.datamgr.dao.schema.DmTableStructDao;
import com.ss.ifrs.datamgr.dao.stat.DmStatSummaryDao;
import com.ss.ifrs.datamgr.domain.exception.DmVerifyException;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.domain.service.duct.DmDomainDuctStatService;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgService;
import com.ss.ifrs.datamgr.domain.service.joint.DmDomainDataPushSignalService;
import com.ss.ifrs.datamgr.feign.BbsDataSyncClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfCheckRuleVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfigValidPolicyRuleVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.*;
import com.ss.ifrs.datamgr.pojo.log.vo.DmLogCheckRuleVo;
import com.ss.ifrs.datamgr.pojo.log.vo.DmLogDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.model.vo.DmModelCompensateDataVo;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainDataVerifyService;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.model.DmModelCompensateService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableVo;
import com.ss.ifrs.datamgr.pojo.stat.vo.DmDuctStatVo;
import com.ss.ifrs.datamgr.service.conf.DmConfCodeService;
import com.ss.ifrs.datamgr.service.impl.icg.DmBussContractGroupOutwardsServiceImpl;
import com.ss.ifrs.datamgr.service.impl.icg.DmBussContractGroupServiceImpl;
import com.ss.ifrs.datamgr.service.impl.icg.DmBussContractGroupTreatyInwardServiceImpl;
import com.ss.ifrs.datamgr.service.impl.icg.DmBussContractGroupTreatyOutwardServiceImpl;
import com.ss.library.thread.ThreadUtils;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.StringUtil;

import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ThreadConstant;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import com.ss.platform.pojo.com.vo.DataSyncReqVo;
import com.ss.platform.util.DataSliceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DmDomainDataVerifyServiceImpl implements DmDomainDataVerifyService {

    @Autowired
    AppConfig appConfig;
    @Autowired
    DmModelCompensateService dmModelCompensateService;

    @Autowired
    DmConfTableDao dmConfTableDao;

    @Autowired
    private DmTableStructDao dmTableStructDao;

    @Autowired
    DmDomainBussPeriodService dmDomainBussPeriodService;

    @Autowired
    DmStatSummaryDao dmStatSummaryDao;

    @Autowired
    DmDomainDataVerifyDao dmDomainDataVerifyDao;

    @Autowired
    DmBussContractGroupServiceImpl dmBussContractGroupService;

    @Autowired
    DmBussContractGroupOutwardsServiceImpl dmBussContractGroupOutwardsService;

    @Autowired
    DmBussContractGroupTreatyInwardServiceImpl dmBussContractGroupTreatyInwardService;

    @Autowired
    DmBussContractGroupTreatyOutwardServiceImpl dmBussContractGroupTreatyOutwardService;

    @Autowired
    BbsConfEntityFeignClient bbsConfEntityFeignClient;

    @Autowired
    DmDapModelDao dmDapModelDao;

    @Autowired
    private OdsModelDao odsModelDao;

    @Autowired
    DmDomainConfCheckRuleDao dmDomainConfCheckRuleDao;

    @Autowired
    DmDomainLogCheckRuleDao dmDomainLogCheckRuleDao;

    @Autowired
    DmDomainDataPushSignalService dmDomainDataPushSignalService;

    @Autowired
    DmDomainDataPushSignalDao dmDomainDataPushSignalDao;

    @Autowired
    DmDomainBussFileExchangeModelDao dmDomainBussFileExchangeModelDao;

    @Autowired
    DmLogDataVerifyDao dmLogDataVerifyDao;

    @Autowired
    DmDapModelDuctDao dmDapModelDuctDao;

    @Autowired
    BbsDataSyncClient bbsDataSyncClient;

    @Qualifier(CommonConstant.BussPeriod.PeriodStatus.PREPARED)
    @Autowired
    DmDomainPeriodService dmDomainPeriodService;
    @Autowired
    DmConfCodeService dmConfCodeService;
    @Autowired
    DmDomainDuctStatService dmDomainDuctStatService;
    @Autowired
    private ApplicationContext applicationContext;


    private Map<String, DmDomainIcgService> icgServiceMap = new HashMap<>();

    @Autowired
    public DmDomainDataVerifyServiceImpl(Map<String, DmDomainIcgService> strategyMap) {
        this.icgServiceMap.clear();
        this.icgServiceMap.putAll(strategyMap);
    }

    final static Map<String, List<String>> bizCodeMap = new ConcurrentHashMap() {
        {
            put(DmConstant.BizCodeModel.MODEL_POLICY_MAIN, Collections.singletonList(DmConstant.BusinessModel.MODEL_DIRECT + "ICG"));
            put(DmConstant.BizCodeModel.MODEL_POLICY_PREMIUM, Collections.singletonList(DmConstant.BusinessModel.MODEL_DIRECT + "ICG"));
            put(DmConstant.BizCodeModel.MODEL_REINS_TREATY, Arrays.asList(DmConstant.BusinessModel.MODEL_TREATY_IN + "ICG"
                    , DmConstant.BusinessModel.MODEL_TREATY_OUT + "ICG"));
            put(DmConstant.BizCodeModel.MODEL_REINS_OUTWARD_DETAIL, Collections.singletonList(DmConstant.BusinessModel.MODEL_FAC_OUT + "ICG"));
        }
    };


    @DmTrackDataVerifyProcess(traceProc = "dataVerify")
    @DmTrackProcess
    @Override
    public void dataVerify(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException {

        String yearMonth = dmBussDataVerifyVo.getYearMonth();
        String errorMsg;

        //判断yearMonth有效性
        if (StringUtil.isEmpty(yearMonth) || !yearMonth.matches("\\d{4}(0[1-9]|1[0-2]|JS)")) {
            errorMsg = DmConstant.VerifyCode.TRACE_MSG_INVALID_YEAR_MONTH + "：" + yearMonth;
            //抛异常
            throw new DmVerifyException(errorMsg);
        }

        dmBussDataVerifyVo.setYearMonth(yearMonth);
        Long bizTypeId = dmBussDataVerifyVo.getBizTypeId();


        //校验bizTypeId的有效性
        DmConfTableVo voById = dmConfTableDao.findVoById(bizTypeId);

        if (voById == null) {
            errorMsg = DmConstant.VerifyCode.TRACE_MSG_NO_SUCH_MODEL + ":" + bizTypeId;
            //抛异常
            throw new DmVerifyException(errorMsg);
        }
        String compensationControl = dmConfCodeService.findCodeCodeByCodeCode("CompensationControl");
        String transitionPeriod = dmConfCodeService.findCodeCodeByCodeCode("transitionPeriod");
        DmDomainDataVerifyServiceImpl dmDomainDataVerifyService = applicationContext.getBean(DmDomainDataVerifyServiceImpl.class);
        // 补偿机制校验
        if (DmConstant.SwitchCode.OPEN.equals(compensationControl)
                && StringUtil.isNotEmpty(transitionPeriod) && yearMonth.compareTo(transitionPeriod) >= 0
                && DmConstant.BizCodeModel.COMPENSATE_RELATION_MODEL.contains(voById.getBizCode())) {
            dmDomainDataVerifyService.dataVerifyCompensate(dmBussDataVerifyVo);
            String compensateCondition = dmModelCompensateService.getMappingCondition(dmBussDataVerifyVo.getBizTypeId());
            dmBussDataVerifyVo.setCompensateCondition(compensateCondition);
        }
        //执行校验
        dmDomainDataVerifyService.dataVerifyDeal(dmBussDataVerifyVo);

    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyDeal")
    @DmTrackProcess
    @Override
    public void dataVerifyDeal(DmBussDataVerifyVo dmBussDataVerifyVo) {
        DmDomainDataVerifyServiceImpl dmDomainDataVerifyService = applicationContext.getBean(DmDomainDataVerifyServiceImpl.class);
        try {
            //校验前
            dmDomainDataVerifyService.dataVerifyPrepare(dmBussDataVerifyVo);
            //校验中
            dmDomainDataVerifyService.dataVerifyTesting(dmBussDataVerifyVo);
            //校验后
            dmDomainDataVerifyService.dataVerifyAfter(dmBussDataVerifyVo);
        } catch (DmVerifyException e) {
            log.error(e.getMessage());
            return;
        }
    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyPrepare")
    @DmTrackProcess
    @Override
    public void dataVerifyPrepare(DmBussDataVerifyVo dmBussDataVerifyVo) {
        DmDomainDataVerifyServiceImpl dmDomainDataVerifyService = applicationContext.getBean(DmDomainDataVerifyServiceImpl.class);
        // 参数预处理
        dmDomainDataVerifyService.parameterPreprocessing(dmBussDataVerifyVo);
        // 创建临时表
        String tempVerityIs = this.dapTempVerityIs(dmBussDataVerifyVo.getBizCode());
        if (DmConstant.DapTempVerityIs.VERITY_YES.equals(tempVerityIs)) {
            this.createDapTempTable(dmBussDataVerifyVo);
        }
        Integer odsVerifyDataCount = dmDapModelDao.getOdsVerifyDataCount(dmBussDataVerifyVo);
        //业务期间的有效性校验
        Boolean aBoolean = this.checkValidYearMonthIs(dmBussDataVerifyVo.getEntityId()
                , dmBussDataVerifyVo.getYearMonth(), dmBussDataVerifyVo.getBizCode());
        String errorMsg;
        if (!aBoolean) {
            errorMsg = DmConstant.VerifyCode.TRACE_MSG_INVALID_YEAR_MONTH + "：" + dmBussDataVerifyVo.getYearMonth();
            //抛异常
            throw new DmVerifyException(errorMsg);
        }


        //没有满足的数据直接进行下面校验
        updateDataPushSignalStatus(dmBussDataVerifyVo.getDrawType(), dmBussDataVerifyVo.getBizCode()
                , dmBussDataVerifyVo.getTaskCode(), odsVerifyDataCount);
        if (odsVerifyDataCount < 1) {
            // 没有需要校验的数据，直接修改业务区间详情状态为成功
            dmDomainPeriodService.executionDetail(dmBussDataVerifyVo.getEntityId(), dmBussDataVerifyVo.getYearMonth(), dmBussDataVerifyVo.getBizCode());
            errorMsg = DmConstant.VerifyCode.TRACE_MSG_NO_DATA;

            throw new DmVerifyException(dmBussDataVerifyVo.getBizCode(), errorMsg);
        }


        // 初始化状态
        dmDomainDataVerifyService.prepareData(dmBussDataVerifyVo);
    }

    /**
     * 参数预处理
     *
     * @param dmBussDataVerifyVo
     */
    @DmTrackDataVerifyProcess(traceProc = "dataVerifyPrepare")
    public void parameterPreprocessing(DmBussDataVerifyVo dmBussDataVerifyVo) {
        // 获取bizCode
        DmConfTableVo voById = dmConfTableDao.findVoById(dmBussDataVerifyVo.getBizTypeId());
        String bizCode = voById.getBizCode();
        dmBussDataVerifyVo.setBizCode(bizCode);
        dmBussDataVerifyVo.setCmunitIs(voById.getCmunitIs());


        //查询当前模型是否存在entity_code BASE_ENTITY_MAPPING业务与精算财务机构映射除外
        Boolean entityCodeColumnIs = this.existsColumn(bizCode
                , DmConstant.BizCodeModelColumn.COLUMN_ENTITY_CODE);
        if (entityCodeColumnIs && !"BASE_ENTITY_MAPPING".equals(bizCode)) {
            BbsConfEntityVo byEntityId = bbsConfEntityFeignClient.findByEntityId(dmBussDataVerifyVo.getEntityId());
            String entityCode = byEntityId.getEntityCode();
            dmBussDataVerifyVo.setEntityCode(entityCode);
        }
        Boolean existsEntityId = this.existsColumn(bizCode, DmConstant.BizCodeModelColumn.COLUMN_ENTITY_ID);
        dmBussDataVerifyVo.setExistsEntityId(existsEntityId);

        //获取操作的表
        String tableName = this.dapVerifyTable(bizCode, dmBussDataVerifyVo.getEntityId());
        dmBussDataVerifyVo.setTableName(tableName);

        dmBussDataVerifyVo.setTypeGroup(voById.getTypeGroup());

        // 实际表名
        dmBussDataVerifyVo.setRealTableName(DmConstant.DbStruct.ODS_USER_SCHEMA + "." + DmConstant.DbStruct.ODS_TABLE_PREFIX + bizCode);

    }

    /**
     * 数据准备
     *
     * @param dmBussDataVerifyVo
     */
    public void prepareData(DmBussDataVerifyVo dmBussDataVerifyVo) {
        String transitionPeriod = dmConfCodeService.findCodeCodeByCodeCode("transitionPeriod");
        // 有效单规则方案
        if (StringUtil.isNotEmpty(transitionPeriod) && dmBussDataVerifyVo.getYearMonth().compareTo(transitionPeriod) < 0
                && DmConstant.BizCodeModel.MODEL_POLICY_MAIN.equalsIgnoreCase(dmBussDataVerifyVo.getBizCode())
                && DmConstant.SwitchCode.OPEN.equals(dmConfCodeService.findCodeCodeByCodeCode("ValidPolicyVerify")))
            dataVerifyValidPolicy(dmBussDataVerifyVo);
        else
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmDapModelDao.updateInitStatus(dmBussDataVerifyVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());

        //删除校验日志
        DmLogCheckRuleVo dmLogCheckRuleVo = new DmLogCheckRuleVo();
        ClassUtil.copyProperties(dmBussDataVerifyVo, dmLogCheckRuleVo);
        dmDomainLogCheckRuleDao.deleteLogCheckRule(dmLogCheckRuleVo);

        //删除计量单元数据
        if (DmConstant.DrawType.FILE_EXCHANGE.equals(dmBussDataVerifyVo.getDrawType()) || DmConstant.DrawType.ETL.equals(dmBussDataVerifyVo.getDrawType())) {
            switch (dmBussDataVerifyVo.getBizCode()) {
                case DmConstant.BizCodeModel
                        .MODEL_REINS_TREATY:
                    dmDapModelDao.deleteCmunitTreatyInward(dmBussDataVerifyVo);
                    dmDapModelDao.deleteCmunitTreatyOutward(dmBussDataVerifyVo);
                    break;
                case DmConstant.BizCodeModel
                        .MODEL_POLICY_PREMIUM:
                case DmConstant.BizCodeModel.MODEL_POLICY_MAIN:
                    if ("1".equals(dmBussDataVerifyVo.getCmunitIs())) {
                        dmDapModelDao.deleteCmunitDirect(dmBussDataVerifyVo);
                    }
                    break;
                case DmConstant.BizCodeModel
                        .MODEL_REINS_OUTWARD:
                    dmDapModelDao.deleteCmunitFacOut(dmBussDataVerifyVo);
                    break;
                default:
            }
        }
        //删除dm的数据
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmDapModelDao.deleteTgtData(dmBussDataVerifyVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

    }

    private void updateDataPushSignalStatus(String drawType, String bizCode, String taskCode, Integer odsVerifyDataCount) {
        //数量为0时，直接更新校验状态为成功
        Integer rowCount;
        String taskStatus = DmConstant.VerifyTaskStatus.VERIFY_SUCCESS_STATUS;
        String dealMsg = null;

        rowCount = dmDomainDataPushSignalService.getRowCount(bizCode, taskCode);
        if (rowCount > 0) {
            taskStatus = DmConstant.VerifyTaskStatus.VERIFYING_STATUS;
            if (!rowCount.equals(odsVerifyDataCount)) {
                taskStatus = DmConstant.VerifyTaskStatus.VERIFY_FAIL_STATUS;
                dealMsg = "TableLevelAnomalies";
            }
        }
        //更新信号表的状态
        dmDomainDataPushSignalService.updateTaskStatus(bizCode, taskCode, taskStatus, dealMsg);
        if (DmConstant.DrawType.FILE_EXCHANGE.equals(drawType)) {
            dmDomainBussFileExchangeModelDao.updateTaskStatus(bizCode, taskCode, taskStatus, dealMsg);
        }
    }


    @DmTrackDataVerifyProcess(traceProc = "dataVerifyTesting")
    @DmTrackProcess
    @Override
    public void dataVerifyTesting(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException {
        DmDomainDataVerifyServiceImpl dmDomainDataVerifyService = applicationContext.getBean(DmDomainDataVerifyServiceImpl.class);
        //日志初始化
        DmLogDataVerifyVo dmLogDataVerifyVo = new DmLogDataVerifyVo();
        String traceCode = DmConstant.VerifyCode.TRACE_CODE_VERIFY_TESTING;
        dmLogDataVerifyVo.setTraceCode(traceCode);


        String tableBusinessNo = dmDapModelDao.getTableBusinessNo(dmBussDataVerifyVo);
        /**
         *
         * 1. 根据checkType来获取校验规则顺序
         * 2. 通过checkType来获取对应的校验规则
         * 3. 封装校验规则到一个Runnable数组中
         * 3. 提交执行
         */
        List<String> validTaskCode = dmDomainDataPushSignalDao.getValidTaskCode(dmBussDataVerifyVo.getBizCode(), dmBussDataVerifyVo.getYearMonth());
        List<String> validTaskCodeWithSuffix = validTaskCode.stream()
                .map(taskCode -> "'" + taskCode + "'") // 为每个元素添加单引号
                .collect(Collectors.toList());
        // 获取校验规则
        List<DmConfCheckRuleVo> validCheckRules = dmDomainConfCheckRuleDao.getValidCheckRules(dmBussDataVerifyVo, validTaskCodeWithSuffix);
        validCheckRules.stream().map(DmConfCheckRuleVo::getCheckType).distinct().sorted().forEach(item -> {
            ArrayList<Runnable> runnables = new ArrayList<>();
            validCheckRules.stream().filter(itemRule -> item.equals(itemRule.getCheckType())).forEach(itemRule ->
                    runnables.add(() -> {
                        DmLogDataVerifyVo threadDmLogDataVerifyVo = new DmLogDataVerifyVo();
                        ClassUtil.copyProperties(dmLogDataVerifyVo, threadDmLogDataVerifyVo);
                        String checkRuleSql = itemRule.getCheckRuleSql();
                        String ruleCode = itemRule.getRuleCode();

                        checkRuleSql = this.dealRuleFactorReplace(checkRuleSql, "1", dmBussDataVerifyVo);
                        checkRuleSql = this.dealRuleFactorReplace(checkRuleSql, "2", dmBussDataVerifyVo);

                        itemRule.setCheckRuleSql(checkRuleSql);
                        itemRule.setBusinessNo(tableBusinessNo);
                        ClassUtil.copyProperties(dmLogDataVerifyVo, threadDmLogDataVerifyVo);
                        //执行校验
                        DmConfCheckRuleVo insertRuleVo = new DmConfCheckRuleVo();
                        ClassUtil.copyProperties(itemRule, insertRuleVo);
                        dmDomainDataVerifyService.dataVerifyTestCheckRule(dmBussDataVerifyVo, insertRuleVo);
                    }));
            // 线程池名称为null
            List<Exception> exceptions = ThreadUtils.execute(runnables, ThreadConstant.RULE_THREAD_POOL, true);
            if (exceptions.size() > 0) {
                throw new DmVerifyException(exceptions.get(0).getMessage());
            }

        });

    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyTesting")
    public void dataVerifyTestCheckRule(DmBussDataVerifyVo dmBussDataVerifyVo, DmConfCheckRuleVo insertRuleVo) {
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmDomainLogCheckRuleDao.insertLogCheckRule(insertRuleVo, dmBussDataVerifyVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyAfter")
    @Override
    public void dataVerifyAfter(DmBussDataVerifyVo dmBussDataVerifyVo) {
        DmDomainDataVerifyServiceImpl dmDomainDataVerifyService = applicationContext.getBean(DmDomainDataVerifyServiceImpl.class);
        // 更新ods表状态
        dmDomainDataVerifyService.dataVerifyResult(dmBussDataVerifyVo);
        // 插入到dm中
        dmDomainDataVerifyService.dataVerifyOdsToDm(dmBussDataVerifyVo);

        // 后续操作
        dmDomainDataVerifyService.dataVerifyComplete(dmBussDataVerifyVo);

    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyComplete")
    public void dataVerifyComplete(DmBussDataVerifyVo dmBussDataVerifyVo) {
        String tempVerityIs = this.dapTempVerityIs(dmBussDataVerifyVo.getBizCode());

        if (DmConstant.DapTempVerityIs.VERITY_YES.equals(tempVerityIs)) {
            //回写ods并删除过程表
            this.rewriteDapTable(dmBussDataVerifyVo);
        }
        // 更新统计信息
        dmDomainDuctStatService.paringStat(dmBussDataVerifyVo.getEntityId(), dmBussDataVerifyVo.getTaskCode(), dmBussDataVerifyVo.getBizTypeId());

        //查询成功，失败，不处理的记录条数
        DapModelStatResultVo dapModelStatResultVo = dmDomainDuctStatService.getStatStatusCount("DM_STAT_" + dmBussDataVerifyVo.getBizCode()
                , dmBussDataVerifyVo.getTaskCode());
        if (DmConstant.DrawType.FILE_EXCHANGE.equals(dmBussDataVerifyVo.getDrawType())) {
            dmDomainBussFileExchangeModelDao.updateVerifyResult(dapModelStatResultVo, dmBussDataVerifyVo);
        }
        dmDomainDataPushSignalService.updateVerifyResult(dapModelStatResultVo, dmBussDataVerifyVo);
        // 校验全部成功更新状态
        if (dmBussDataVerifyVo.getErrorCount() == 0) {
            dmDomainPeriodService.executionDetail(dmBussDataVerifyVo.getEntityId(), dmBussDataVerifyVo.getYearMonth(), dmBussDataVerifyVo.getBizCode());
        }

        //统计
        /*DmDuctStatVo dmDuctStatVo = new DmDuctStatVo();
        ClassUtil.copyProperties(dmBussDataVerifyVo, dmDuctStatVo);
        dmStatSummaryDao.reStatByCdt(dmDuctStatVo);*/
        //基础同步数据
        if (DmConstant.BusinessTypeCode.TYPE_SEVEN.equals(dmBussDataVerifyVo.getTypeGroup())) {
            DataSyncReqVo dataSyncReqVo = new DataSyncReqVo();
            dataSyncReqVo.setBizCode(dmBussDataVerifyVo.getBizCode());
            dataSyncReqVo.setUserId(dmBussDataVerifyVo.getUserId());
            dataSyncReqVo.setTaskCode(dmBussDataVerifyVo.getTaskCode());
            new Thread(() -> {
                try {
                    bbsDataSyncClient.syncConfData(dataSyncReqVo);
                } catch (Exception e) {
                    log.error("Failed to sync data", e);
                }
            }).start();
        }
    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyResult")
    @DmTrackProcess
    public void dataVerifyResult(DmBussDataVerifyVo dmBussDataVerifyVo) {


        //1、查询是否存在校验日志
        DmLogCheckRuleVo dmLogCheckRuleVo = new DmLogCheckRuleVo();
        ClassUtil.copyProperties(dmBussDataVerifyVo, dmLogCheckRuleVo);
        Integer logCheckRuleCount = dmDomainLogCheckRuleDao.getLogCheckRuleCount(dmLogCheckRuleVo, dmBussDataVerifyVo.getTraceNo());

        dmBussDataVerifyVo.setErrorCount(logCheckRuleCount);
        //1-1、存在失败的校验进行状态更新
        if (logCheckRuleCount > 0) {
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> odsModelDao.updateFailStatus(dmBussDataVerifyVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());
        }

        //2、更新为成功状态
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> odsModelDao.updateSuccessStatus(dmBussDataVerifyVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @DmTrackDataVerifyProcess(traceProc = "dataVerifyOdsToDm")
    @DmTrackProcess
    public void dataVerifyOdsToDm(DmBussDataVerifyVo dmBussDataVerifyVo) {
        Long bizTypeId = dmBussDataVerifyVo.getBizTypeId();
        String taskCode = dmBussDataVerifyVo.getTaskCode();
        String drawType = dmBussDataVerifyVo.getDrawType();

        String bizCode = dmBussDataVerifyVo.getBizCode();


        //1、查询是否满足插入tgt的状态（6）
        Integer successDataCount = dmDapModelDao.getSuccessDataCount(dmBussDataVerifyVo);
        if (successDataCount > 0) {
            /**
             * 1. 根据dm_conf_table_column这个表col_code从映射表中获取对应的相关联的字段，
             *         如果不存在，则在所有的src_colume字段前面加上t.
             *         如果存在，则获取dm_conf_table_column_ref这张表的rule_config来对join查询进行拼接
             * 2. 查询出公共字段
             * 3. 根据对应的字段，将src和tgt的字段放在对应的位置
             */
            // 获取无需映射字段
            DapModelColumnMappingVo mappingColumn = dmDapModelDao.getMappingColumn(bizTypeId);
            // 获取需要映射的字段
            DapModelColumnRefMappingVo refMappingColumn = dmDapModelDao.getRefMappingColumn(bizCode, bizTypeId);
            //获取固定字段
            DapModelColumnMappingVo originalColumn = dmDapModelDao.getOriginalColumn();

            //插入tgt表
            DapModelInsertVo dapModelInsertVo = new DapModelInsertVo();
            dapModelInsertVo.setBizCode(bizCode);
            dapModelInsertVo.setTgtCommColumn(originalColumn.getTgtColumn());
            dapModelInsertVo.setTgtColumn(mappingColumn.getTgtColumn());
            dapModelInsertVo.setSrcCommColumn(originalColumn.getSrcColumn());
            dapModelInsertVo.setSrcColumn(mappingColumn.getSrcColumn());
            dapModelInsertVo.setTaskCode(taskCode);
            dapModelInsertVo.setDrawType(drawType);
            dapModelInsertVo.setSrcTableName(dmBussDataVerifyVo.getTableName());
            if (refMappingColumn != null) {
                dapModelInsertVo.setRuleMapping(refMappingColumn.getRuleMapping());
                dapModelInsertVo.setTgtRefColumn(refMappingColumn.getTgtColumn());
                dapModelInsertVo.setSrcRefColumnWithPrefix(refMappingColumn.getSrcColumnPrefix());
            }

            // 插入dm
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmDapModelDao.insertTgtData(dapModelInsertVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());

            //更新状态dm为成功（4、6）
            String cmUnitIs = dmBussDataVerifyVo.getCmunitIs();
            if ("1".equals(cmUnitIs)) {
                List<String> strings = bizCodeMap.get(bizCode);
                strings.forEach(item -> {
                    DmDomainIcgService dmDomainIcgService = icgServiceMap.get(item);
                    DmIcgProcVo dmIcgProcVo = new DmIcgProcVo();
                    dmIcgProcVo.setYearMonth(dmBussDataVerifyVo.getYearMonth());
                    dmIcgProcVo.setTaskCode(dmBussDataVerifyVo.getTaskCode());
                    dmIcgProcVo.setEntityId(dmBussDataVerifyVo.getEntityId());
                    // 计量单元到单还是到险种
                    dmIcgProcVo.setCmunitTo(DmConstant.BizCodeModel.MODEL_POLICY_MAIN.equals(bizCode) ?
                            DmConstant.CmunitTo.POLICY : DmConstant.CmunitTo.RISK);
                    //dmDomainIcgService.cmunitIdentify(dmIcgProcVo);
                });
            }
        }


    }

    /**
     * 有效单校验
     *
     * @param dmBussDataVerifyVo
     */
    @Override
    public void dataVerifyValidPolicy(DmBussDataVerifyVo dmBussDataVerifyVo) {
        DmLogDataVerifyVo dmLogDataVerifyVo = new DmLogDataVerifyVo();
        dmLogDataVerifyVo.setTraceCode(DmConstant.VerifyCode.TRACE_CODE_VERIFY_VALID_POLICY + DmConstant.VerifyCode.TRACE_START);
        dataVerifyLog(dmLogDataVerifyVo, dmBussDataVerifyVo);
        // 判断案件主表模型是否生效
        int validTable = dmConfTableDao.findValidModel(dmBussDataVerifyVo.getBizCode());
        List<DmConfigValidPolicyRuleVo> dmConfigValidPolicyRuleVos = dmDomainConfCheckRuleDao.listValidPolicyRule(dmBussDataVerifyVo.getTaskCode());
        dmConfigValidPolicyRuleVos.forEach(item -> {
            if (!(("ACCORDING_TO_CLAIM_STATUS_RULE".equalsIgnoreCase(item.getRuleCode()) ||
                    "ACCORDING_TO_OUTSTANDING_AMOUNT_RULE".equalsIgnoreCase(item.getRuleCode())) &&
                    validTable < 0)) {
                // 校验规则开始执行前日志
                dmLogDataVerifyVo.setTraceCode(item.getRuleCode());
                dmLogDataVerifyVo.setTraceMsg(DmConstant.VerifyCode.TRACE_CODE_VERIFY_VALID_POLICY + DmConstant.VerifyCode.TRACE_START + item.getRuleSql());
                dataVerifyLog(dmLogDataVerifyVo, dmBussDataVerifyVo);
                // 执行校验规则
                dmDapModelDao.updatePolicyVaildStatus(dmBussDataVerifyVo, item.getRuleSql());
                // 校验规则执行后日志
                dmLogDataVerifyVo.setTraceCode(item.getRuleCode());
                dmLogDataVerifyVo.setTraceMsg(DmConstant.VerifyCode.TRACE_CODE_VERIFY_VALID_POLICY + DmConstant.VerifyCode.TRACE_END + item.getRuleSql());
                dataVerifyLog(dmLogDataVerifyVo, dmBussDataVerifyVo);
            }
        });
    }

    /**
     * 有效单补偿校验
     * 1. 先根据对应的条件获取到需要补偿的单
     * 2. 根据补偿的单获取到真正需要补偿的数据
     * 3. 对查找出来的补偿数据进行重新校验
     *
     * @param dmBussDataVerifyVo
     * @throws DmVerifyException
     */
    @Override
    public void dataVerifyCompensate(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException {
        DmLogDataVerifyVo dmLogDataVerifyVo = new DmLogDataVerifyVo();
        dmLogDataVerifyVo.setTraceCode(DmConstant.VerifyCode.TRACE_CODE_VERIFY_COMPENSATE + DmConstant.VerifyCode.TRACE_START);
        dataVerifyLog(dmLogDataVerifyVo, dmBussDataVerifyVo);
        //查询entityCode
        BbsConfEntityVo byEntityId = bbsConfEntityFeignClient.findByEntityId(dmBussDataVerifyVo.getEntityId());
        String entityCode = byEntityId.getEntityCode();
        // 设置查询参数
        DmModelCompensateDataVo dmBussDataCompensateVo = new DmModelCompensateDataVo();
        ClassUtil.copyProperties(dmBussDataVerifyVo, dmBussDataCompensateVo);
        dmBussDataCompensateVo.setEntityCode(entityCode);
        dmModelCompensateService.insertNeedCompensatePolicy(dmBussDataCompensateVo);
        Long needCompensatePolicySize = dmModelCompensateService.getNeedCompensatePolicySize();
        // 不存在需要补偿的单
        if (needCompensatePolicySize < 0) {
            dmLogDataVerifyVo.setTraceCode(DmConstant.VerifyCode.TRACE_CODE_VERIFY_COMPENSATE);
            dmLogDataVerifyVo.setTraceMsg("not compensate data");
        } else {
            // 插入实际需要补偿的表
            dmModelCompensateService.getModelCompensateData(dmBussDataCompensateVo);
            // 插入补偿单日志
            dmModelCompensateService.insertLogCompensatePolicy(dmBussDataVerifyVo.getTaskCode());
            // 对补偿的数据重新拉取校验
            dmModelCompensateService.compensateDataVerify(dmBussDataVerifyVo);
            // 补偿机制校验完成日志
            dmLogDataVerifyVo.setTraceCode(DmConstant.VerifyCode.TRACE_CODE_VERIFY_COMPENSATE + DmConstant.VerifyCode.TRACE_END);
            dataVerifyLog(dmLogDataVerifyVo, dmBussDataVerifyVo);

        }
    }

    @Override
    public void dataVerifyLog(DmLogDataVerifyVo dmLogDataVerifyVo, DmBussDataVerifyVo dmBussDataVerifyVo) {
        ClassUtil.copyProperties(dmBussDataVerifyVo, dmLogDataVerifyVo);
        String traceStatus = dmLogDataVerifyVo.getTraceStatus();
        if (StringUtil.isEmpty(traceStatus)) {
            dmLogDataVerifyVo.setTraceStatus(DmConstant.VerifyCode.TRACE_STATUS_SUCCESS);
        }

        String traceNo = dmLogDataVerifyVo.getTraceNo();
        if (StringUtil.isEmpty(traceNo)) {
            dmLogDataVerifyVo.setTraceNo(String.valueOf(dmDomainDataVerifyDao.getTraceNo()));
        }

        dmLogDataVerifyDao.insertLog(dmLogDataVerifyVo);
    }

    @Override
    public Boolean checkValidYearMonthIs(Long entityId, String yearMonth, String bizCode) {
        int indexOf = yearMonth.indexOf("JS");
        Boolean flag = false;

        if (indexOf != -1) {
            String preparingYearMonth = dmDomainBussPeriodService.getPreparingYearMonth(entityId, bizCode);
            if (preparingYearMonth != null && preparingYearMonth.substring(7, 12).equals(yearMonth.substring(7, 12))) {
                flag = true;
            }
        } else {
            String validYearMonth = dmDomainBussPeriodService.getValidYearMonth(entityId, null, bizCode);
            if (validYearMonth == null || Integer.valueOf(validYearMonth) <= Integer.valueOf(yearMonth)) {
                flag = true;
            }
        }

        return flag;
    }

    /**
     * 查询entity_code字段是否存在
     *
     * @param bizCode
     * @return
     */
    private Boolean existsColumn(String bizCode, String tableColumn) {
        String tableName = DmConstant.DbStruct.DM_TABLE_PREFIX + bizCode;
        Integer tableColumn1 = dmTableStructDao.findTableColumn(tableName, tableColumn);
        Boolean bool = false;

        if (tableColumn1 > 0 && !DmConstant.BizCodeModel.MODEL_BASE_ENTITY.equals(bizCode)) {
            bool = true;
        }
        return bool;
    }


    /**
     * 替换规则中因子
     *
     * @param ruleSql
     * @param ruleFactorType
     * @return
     */
    private String dealRuleFactorReplace(String ruleSql, String ruleFactorType, DmBussDataVerifyVo dmBussDataVerifyVo) {
        String afterRuleSql = "";

        switch (ruleFactorType) {
            case "1":
                afterRuleSql = ruleSql.replace("#ALLOW_AMOUNT#", "10");
                break;
            case "2":
                if (DmConstant.DapTempVerityIs.VERITY_YES.equals(dapTempVerityIs(dmBussDataVerifyVo.getBizCode()))) {
                    String oldStr = "(?i)" + DmConstant.DbStruct.ODS_USER_SCHEMA + "." + DmConstant.DbStruct.ODS_TABLE_PREFIX + dmBussDataVerifyVo.getBizCode();
                    String newStr = DmConstant.DbStruct.DM_DUCT_TABLE_PREFIX + dmBussDataVerifyVo.getBizCode() + "_" + dmBussDataVerifyVo.getEntityId();
                    return ruleSql.replaceAll(oldStr, newStr);
                }
            default:
                afterRuleSql = ruleSql;
        }

        return afterRuleSql;
    }

    /**
     * 是否通过临时表校验
     *
     * @param bizCode
     * @return
     */
    private String dapTempVerityIs(String bizCode) {
        String verityIs = DmConstant.DapTempVerityIs.VERITY_NO;
        switch (bizCode) {
            case DmConstant.BizCodeModel.MODEL_ACC_PAYMENT:
            case DmConstant.BizCodeModel.MODEL_REINS_OUTWARD_DETAIL:
                //verityIs = DmConstant.DapTempVerityIs.VERITY_YES;
                break;
            default:
        }

        return verityIs;
    }

    /**
     * 返回需要用到表
     *
     * @param bizCode
     * @return
     */
    private String dapVerifyTable(String bizCode, Long entity_id) {
        //是否使用duct过程表进行校验
        String tempVerityIs = this.dapTempVerityIs(bizCode);
        String tableName = DmConstant.DbStruct.ODS_USER_SCHEMA + "." + DmConstant.DbStruct.ODS_TABLE_PREFIX + bizCode;
        if (DmConstant.DapTempVerityIs.VERITY_YES.equals(tempVerityIs)) {
            tableName = DmConstant.DbStruct.DM_DUCT_TABLE_PREFIX + bizCode + "_" + entity_id;
        }
        return tableName;
    }

    /**
     * 创建临时表的
     *
     * @param dmBussDataVerifyVo
     */
    private void createDapTempTable(DmBussDataVerifyVo dmBussDataVerifyVo) {
        Long bizTypeId = dmBussDataVerifyVo.getBizTypeId();
        DmConfTableVo voById = dmConfTableDao.findVoById(bizTypeId);
        String bizCode = voById.getBizCode();
        // 源表
        String odsTableName = DmConstant.DbStruct.ODS_USER_SCHEMA + "." + DmConstant.DbStruct.ODS_TABLE_PREFIX + bizCode;
        // 过程表
        String ductTableName = DmConstant.DbStruct.DM_DUCT_TABLE_PREFIX + bizCode + "_" + dmBussDataVerifyVo.getEntityId();
        //删除表
        if (dmTableStructDao.queryTableIsExist(DmConstant.DbStruct.DM_USER_SCHEMA, ductTableName) != 0) {
            dmTableStructDao.dropTable(DmConstant.DbStruct.DM_USER_SCHEMA, ductTableName);
        }

        //创建表,并插入数据
        DapModelConditionVo dapModelConditionVo = new DapModelConditionVo();
        ClassUtil.copyProperties(dmBussDataVerifyVo, dapModelConditionVo);
        dmDapModelDuctDao.createDuctTable(ductTableName, odsTableName);
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> {
            dmDapModelDuctDao.insertDuctData(dapModelConditionVo, ductTableName, odsTableName
                    , SliceAttributes.init(dataSliceQuantity, dataSliceNo));
        }, appConfig.getDataSliceQuantity());
        dmTableStructDao.createIndex(ductTableName, ductTableName + "_idx_mod_" + appConfig.getDataSliceQuantity()
                , String.format("mod(id,%d)", appConfig.getDataSliceQuantity()));
        dmDapModelDuctDao.addDuctTableKey(ductTableName);
    }

    @Transactional
    public void rewriteDapTable(DmBussDataVerifyVo dmBussDataVerifyVo) {
        Long bizTypeId = dmBussDataVerifyVo.getBizTypeId();

        DmConfTableVo voById = dmConfTableDao.findVoById(bizTypeId);
        String bizCode = voById.getBizCode();
        String odsTableName = DmConstant.DbStruct.ODS_USER_SCHEMA + "." + DmConstant.DbStruct.ODS_TABLE_PREFIX + bizCode;
        String ductTableName = DmConstant.DbStruct.DM_DUCT_TABLE_PREFIX + bizCode + "_" + dmBussDataVerifyVo.getEntityId();

        DapModelConditionVo dapModelConditionVo = new DapModelConditionVo();
        ClassUtil.copyProperties(dmBussDataVerifyVo, dapModelConditionVo);

        //临时表的状态更新回到ods中
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) ->
                        dmDapModelDuctDao.rewriteOdsStatus(ductTableName, odsTableName, dmBussDataVerifyVo.getTaskCode()
                                , SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        // 删除过程表
        dmTableStructDao.dropTable(DmConstant.DbStruct.DM_USER_SCHEMA, ductTableName);
    }

}
