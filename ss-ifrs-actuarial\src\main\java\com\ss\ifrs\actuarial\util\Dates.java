package com.ss.ifrs.actuarial.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 日期时间工具类
 * <AUTHOR>
 */
public class Dates {

    /**
     * 日期时间格式匹配
     */
    private static final Map<Pattern, DateTimeFormatter> PFS = new HashMap<>();

    static {
        // yyyy/MM/dd
        PFS.put(Pattern.compile("\\d{4}/\\d{1,2}/\\d{1,2}"),
                DateTimeFormat.forPattern("yyyy/MM/dd"));
        // yyyy-MM-dd
        PFS.put(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2}"),
                DateTimeFormat.forPattern("yyyy-MM-dd"));
        // yyyy-MM-dd HH:mm:ss.SSS
        PFS.put(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}\\.\\d{1,3}"),
                DateTimeFormat.forPattern("yyyy-M-d H:m:s.SSS"));
        // yyyy-M-d H:m:s
        PFS.put(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}"),
                DateTimeFormat.forPattern("yyyy-M-d H:m:s"));
        // yyyy-M-d H:m
        PFS.put(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}"),
                DateTimeFormat.forPattern("yyyy-M-d H:m"));
        // yyyy-M-d H
        PFS.put(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}"),
                DateTimeFormat.forPattern("yyyy-M-d H"));
        // yyyyMMdd
        PFS.put(Pattern.compile("\\d{8}"),
                DateTimeFormat.forPattern("yyyyMMdd"));
        // yyyyMMdd
        PFS.put(Pattern.compile("\\d{6}"),
                DateTimeFormat.forPattern("yyyyMM"));
        // yyyy
        PFS.put(Pattern.compile("\\d{4}"),
                DateTimeFormat.forPattern("yyyy"));
    }

    public static Date lastDay(String yearMonth) {
        return DateTimeFormat.forPattern("yyyyMM").parseDateTime(yearMonth).dayOfMonth().withMaximumValue().toDate();
    }

    public static Date lastDay(DateTime dateTime) {
        return dateTime.dayOfMonth().withMaximumValue().toDate();
    }

    public static Date lastDay(Date date) {
        return lastDay(new DateTime(date));
    }

    public static Date truncDay(DateTime date) {
        return date.dayOfMonth().roundFloorCopy().toDate();
    }

    public static DateTime truncDayToDateTime(DateTime date) {
        return date.dayOfMonth().roundFloorCopy();
    }

    public static Date truncMonth(Date date) {
        return truncMonthToDateTime(date).toDate();
    }



    public static DateTime truncMonthToDateTime(Date date) {
        return new DateTime(date).monthOfYear().roundFloorCopy();
    }

    public static DateTime truncMonthToDateTime(DateTime date) {
        return date.monthOfYear().roundFloorCopy();
    }

    public static int monthsBetween(String startYearMonth, String endYearMonth) {
        DateTime startDateTime = DateTimeFormat.forPattern("yyyyMM").parseDateTime(startYearMonth);
        DateTime endDateTime = DateTimeFormat.forPattern("yyyyMM").parseDateTime(endYearMonth);
        return monthsBetween(startDateTime, endDateTime);
    }

    public static int monthsBetween(String startYearMonth, Date endDate) {
        DateTime startDateTime = DateTimeFormat.forPattern("yyyyMM").parseDateTime(startYearMonth);
        return monthsBetween(startDateTime, new DateTime(endDate));
    }

    public static int monthsBetween(Date startDate, String endYearMonth) {
        DateTime endDateTime = DateTimeFormat.forPattern("yyyyMM").parseDateTime(endYearMonth);
        return Months.monthsBetween(new DateTime(startDate), endDateTime).getMonths();
    }

    public static int monthsBetween(DateTime start, DateTime end) {
        return Months.monthsBetween(truncMonthToDateTime(start), truncMonthToDateTime(end)).getMonths();
    }

    public static int monthsBetween(Date start, Date end) {
        return Months.monthsBetween(truncMonthToDateTime(start), truncMonthToDateTime(end)).getMonths();
    }

    public static int daysBetween(Date start, Date end) {
        return daysBetween(new DateTime(start), new DateTime(end));
    }

    public static int daysBetween(DateTime start, DateTime end) {
        return org.joda.time.Days.daysBetween(truncDayToDateTime(start), truncDayToDateTime(end)).getDays();
    }

    public static String toChar(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    /**
     * 将字符串转成日期
     */
    public static Date toDate(String dateStr) {
        dateStr = StringUtils.trimToNull(dateStr);
        if (dateStr == null) {
            return null;
        }
        for (Map.Entry<Pattern, DateTimeFormatter> pf : PFS.entrySet()) {
            if (pf.getKey().matcher(dateStr).matches()) {
                return pf.getValue().parseDateTime(dateStr).toDate();
            }
        }
        throw new IllegalArgumentException("Cann't covert date " + dateStr);
    }

    public static String preYearMonth(String yearMonth) {
        return DateTimeFormat.forPattern("yyyyMM").parseDateTime(yearMonth).minusMonths(1).toString("yyyyMM");
    }

    public static String getYearMonth(String yearMonth, int offset) {
        return DateTimeFormat.forPattern("yyyyMM").parseDateTime(yearMonth)
                .plusMonths(offset).toString("yyyyMM");
    }

    public static Date min(Date date1, Date date2) {
        // 处理 null 的情况
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.compareTo(date2) < 0 ? date1 : date2;
    }

    public static Date max(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.compareTo(date2) > 0 ? date1 : date2;
    }


}
