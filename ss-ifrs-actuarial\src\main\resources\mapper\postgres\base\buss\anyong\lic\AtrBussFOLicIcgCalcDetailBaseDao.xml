<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-08-11 10:56:32 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussFOLicIcgCalcDetailDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="ULT_ORI" property="ultOri" jdbcType="DECIMAL" />
    <result column="ULT" property="ult" jdbcType="DECIMAL" />
    <result column="PAID_MODE" property="paidMode" jdbcType="DECIMAL" />
    <result column="IBNR_CUR" property="ibnrCur" jdbcType="DECIMAL" />
    <result column="IBNR_PRE" property="ibnrPre" jdbcType="DECIMAL" />
    <result column="OS_CUR" property="osCur" jdbcType="DECIMAL" />
    <result column="OS_PRE" property="osPre" jdbcType="DECIMAL" />
    <result column="ULAE_CUR" property="ulaeCur" jdbcType="DECIMAL" />
    <result column="ULAE_PRE" property="ulaePre" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, MAIN_ID, DEV_NO, ULT_ORI, ULT, PAID_MODE, IBNR_CUR, IBNR_PRE, OS_CUR, OS_PRE,
    ULAE_CUR, ULAE_PRE
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
        and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="mainId != null ">
        and MAIN_ID = #{mainId,jdbcType=DECIMAL}
      </if>
      <if test="devNo != null ">
        and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="ultOri != null ">
        and ULT_ORI = #{ultOri,jdbcType=DECIMAL}
      </if>
      <if test="ult != null ">
        and ULT = #{ult,jdbcType=DECIMAL}
      </if>
      <if test="paidMode != null ">
        and PAID_MODE = #{paidMode,jdbcType=DECIMAL}
      </if>
      <if test="ibnrCur != null ">
        and IBNR_CUR = #{ibnrCur,jdbcType=DECIMAL}
      </if>
      <if test="ibnrPre != null ">
        and IBNR_PRE = #{ibnrPre,jdbcType=DECIMAL}
      </if>
      <if test="osCur != null ">
        and OS_CUR = #{osCur,jdbcType=DECIMAL}
      </if>
      <if test="osPre != null ">
        and OS_PRE = #{osPre,jdbcType=DECIMAL}
      </if>
      <if test="ulaeCur != null ">
        and ULAE_CUR = #{ulaeCur,jdbcType=DECIMAL}
      </if>
      <if test="ulaePre != null ">
        and ULAE_PRE = #{ulaePre,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
        and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.mainId != null ">
        and MAIN_ID = #{condition.mainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.devNo != null ">
        and DEV_NO = #{condition.devNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.ultOri != null ">
        and ULT_ORI = #{condition.ultOri,jdbcType=DECIMAL}
      </if>
      <if test="condition.ult != null ">
        and ULT = #{condition.ult,jdbcType=DECIMAL}
      </if>
      <if test="condition.paidMode != null ">
        and PAID_MODE = #{condition.paidMode,jdbcType=DECIMAL}
      </if>
      <if test="condition.ibnrCur != null ">
        and IBNR_CUR = #{condition.ibnrCur,jdbcType=DECIMAL}
      </if>
      <if test="condition.ibnrPre != null ">
        and IBNR_PRE = #{condition.ibnrPre,jdbcType=DECIMAL}
      </if>
      <if test="condition.osCur != null ">
        and OS_CUR = #{condition.osCur,jdbcType=DECIMAL}
      </if>
      <if test="condition.osPre != null ">
        and OS_PRE = #{condition.osPre,jdbcType=DECIMAL}
      </if>
      <if test="condition.ulaeCur != null ">
        and ULAE_CUR = #{condition.ulaeCur,jdbcType=DECIMAL}
      </if>
      <if test="condition.ulaePre != null ">
        and ULAE_PRE = #{condition.ulaePre,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
        and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="mainId != null ">
        and MAIN_ID = #{mainId,jdbcType=DECIMAL}
      </if>
      <if test="devNo != null ">
        and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="ultOri != null ">
        and ULT_ORI = #{ultOri,jdbcType=DECIMAL}
      </if>
      <if test="ult != null ">
        and ULT = #{ult,jdbcType=DECIMAL}
      </if>
      <if test="paidMode != null ">
        and PAID_MODE = #{paidMode,jdbcType=DECIMAL}
      </if>
      <if test="ibnrCur != null ">
        and IBNR_CUR = #{ibnrCur,jdbcType=DECIMAL}
      </if>
      <if test="ibnrPre != null ">
        and IBNR_PRE = #{ibnrPre,jdbcType=DECIMAL}
      </if>
      <if test="osCur != null ">
        and OS_CUR = #{osCur,jdbcType=DECIMAL}
      </if>
      <if test="osPre != null ">
        and OS_PRE = #{osPre,jdbcType=DECIMAL}
      </if>
      <if test="ulaeCur != null ">
        and ULAE_CUR = #{ulaeCur,jdbcType=DECIMAL}
      </if>
      <if test="ulaePre != null ">
        and ULAE_PRE = #{ulaePre,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    where ID in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail">
    insert into ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="mainId != null">
        MAIN_ID,
      </if>
      <if test="devNo != null">
        DEV_NO,
      </if>
      <if test="ultOri != null">
        ULT_ORI,
      </if>
      <if test="ult != null">
        ULT,
      </if>
      <if test="paidMode != null">
        PAID_MODE,
      </if>
      <if test="ibnrCur != null">
        IBNR_CUR,
      </if>
      <if test="ibnrPre != null">
        IBNR_PRE,
      </if>
      <if test="osCur != null">
        OS_CUR,
      </if>
      <if test="osPre != null">
        OS_PRE,
      </if>
      <if test="ulaeCur != null">
        ULAE_CUR,
      </if>
      <if test="ulaePre != null">
        ULAE_PRE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="mainId != null">
        #{mainId,jdbcType=DECIMAL},
      </if>
      <if test="devNo != null">
        #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="ultOri != null">
        #{ultOri,jdbcType=DECIMAL},
      </if>
      <if test="ult != null">
        #{ult,jdbcType=DECIMAL},
      </if>
      <if test="paidMode != null">
        #{paidMode,jdbcType=DECIMAL},
      </if>
      <if test="ibnrCur != null">
        #{ibnrCur,jdbcType=DECIMAL},
      </if>
      <if test="ibnrPre != null">
        #{ibnrPre,jdbcType=DECIMAL},
      </if>
      <if test="osCur != null">
        #{osCur,jdbcType=DECIMAL},
      </if>
      <if test="osPre != null">
        #{osPre,jdbcType=DECIMAL},
      </if>
      <if test="ulaeCur != null">
        #{ulaeCur,jdbcType=DECIMAL},
      </if>
      <if test="ulaePre != null">
        #{ulaePre,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_FO_LIC_ICG_CALC_DETAIL values
      (#{item.id,jdbcType=DECIMAL},
      #{item.mainId,jdbcType=DECIMAL}, #{item.devNo,jdbcType=DECIMAL}, #{item.ultOri,jdbcType=DECIMAL},
      #{item.ult,jdbcType=DECIMAL}, #{item.paidMode,jdbcType=DECIMAL}, #{item.ibnrCur,jdbcType=DECIMAL},
      #{item.ibnrPre,jdbcType=DECIMAL}, #{item.osCur,jdbcType=DECIMAL}, #{item.osPre,jdbcType=DECIMAL},
      #{item.ulaeCur,jdbcType=DECIMAL}, #{item.ulaePre,jdbcType=DECIMAL})
    </foreach>
    select 1
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail">
    update ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    <set>
      <if test="mainId != null">
        MAIN_ID = #{mainId,jdbcType=DECIMAL},
      </if>
      <if test="devNo != null">
        DEV_NO = #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="ultOri != null">
        ULT_ORI = #{ultOri,jdbcType=DECIMAL},
      </if>
      <if test="ult != null">
        ULT = #{ult,jdbcType=DECIMAL},
      </if>
      <if test="paidMode != null">
        PAID_MODE = #{paidMode,jdbcType=DECIMAL},
      </if>
      <if test="ibnrCur != null">
        IBNR_CUR = #{ibnrCur,jdbcType=DECIMAL},
      </if>
      <if test="ibnrPre != null">
        IBNR_PRE = #{ibnrPre,jdbcType=DECIMAL},
      </if>
      <if test="osCur != null">
        OS_CUR = #{osCur,jdbcType=DECIMAL},
      </if>
      <if test="osPre != null">
        OS_PRE = #{osPre,jdbcType=DECIMAL},
      </if>
      <if test="ulaeCur != null">
        ULAE_CUR = #{ulaeCur,jdbcType=DECIMAL},
      </if>
      <if test="ulaePre != null">
        ULAE_PRE = #{ulaePre,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail">
    update ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    <set>
      <if test="record.mainId != null">
        MAIN_ID = #{record.mainId,jdbcType=DECIMAL},
      </if>
      <if test="record.devNo != null">
        DEV_NO = #{record.devNo,jdbcType=DECIMAL},
      </if>
      <if test="record.ultOri != null">
        ULT_ORI = #{record.ultOri,jdbcType=DECIMAL},
      </if>
      <if test="record.ult != null">
        ULT = #{record.ult,jdbcType=DECIMAL},
      </if>
      <if test="record.paidMode != null">
        PAID_MODE = #{record.paidMode,jdbcType=DECIMAL},
      </if>
      <if test="record.ibnrCur != null">
        IBNR_CUR = #{record.ibnrCur,jdbcType=DECIMAL},
      </if>
      <if test="record.ibnrPre != null">
        IBNR_PRE = #{record.ibnrPre,jdbcType=DECIMAL},
      </if>
      <if test="record.osCur != null">
        OS_CUR = #{record.osCur,jdbcType=DECIMAL},
      </if>
      <if test="record.osPre != null">
        OS_PRE = #{record.osPre,jdbcType=DECIMAL},
      </if>
      <if test="record.ulaeCur != null">
        ULAE_CUR = #{record.ulaeCur,jdbcType=DECIMAL},
      </if>
      <if test="record.ulaePre != null">
        ULAE_PRE = #{record.ulaePre,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    where ID in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail">
    select count(1) from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>