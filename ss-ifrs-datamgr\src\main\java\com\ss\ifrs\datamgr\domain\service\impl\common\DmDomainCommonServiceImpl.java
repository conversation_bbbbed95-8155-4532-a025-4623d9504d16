package com.ss.ifrs.datamgr.domain.service.impl.common;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.datamgr.dao.log.DmLogBussCmUnitDao;
import com.ss.ifrs.datamgr.domain.service.common.DmDomainCommonService;
import com.ss.ifrs.datamgr.feign.BmsActProcFeignClient;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DmDomainCommonServiceImpl implements DmDomainCommonService {

    @Autowired
    private BmsActProcFeignClient bmsActProcFeignClient;

    @Autowired
    private DmLogBussCmUnitDao dmLogBussCmUnitDao;


    @Override
    public ActOverviewVo getActOverviewVoByObject(ActOverviewVo actOverviewVo) {
        ActOverviewVo result = new ActOverviewVo();
        try {
            BaseResponse<Object> actOverviewByObject = bmsActProcFeignClient.findActOverviewByObject(actOverviewVo);
            if (ObjectUtils.isNotEmpty(actOverviewByObject) && actOverviewByObject.getResCode().equals(ResCodeConstant.ResCode.SUCCESS) && ObjectUtils.isNotEmpty(actOverviewByObject.getResData())) {
                result = JSONObject.parseObject(JSONObject.toJSONString(actOverviewByObject.getResData()),
                        ActOverviewVo.class);
            }
        } catch (Exception e) {
            log.error("Description Failed to obtain flow node information ：{}", e.getMessage());
        }
        return result;
    }

    @Override
    public ActOverviewVo getPrevActOverviewByProcCode(String procCode) {
        ActOverviewVo result = new ActOverviewVo();
        try {
            BaseResponse<ActOverviewVo> prevByCode = bmsActProcFeignClient.findPrevByCode(SystemConstant.DmIdentity.APP_CODE, procCode);
            if (ObjectUtils.isNotEmpty(prevByCode) && prevByCode.getResCode().equals(ResCodeConstant.ResCode.SUCCESS) && ObjectUtils.isNotEmpty(prevByCode.getResData())) {
                result = JSONObject.parseObject(JSONObject.toJSONString(prevByCode.getResData()),
                        ActOverviewVo.class);
            }
        }catch (Exception e){
            log.error("Description Failed to obtain flow node information ：{}", e.getMessage());
        }
        return result;
    }


}
