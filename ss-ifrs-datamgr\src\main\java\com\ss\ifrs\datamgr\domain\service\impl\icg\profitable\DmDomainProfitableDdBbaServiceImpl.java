package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.profitable.DmDomainProfitableDdBbaDao;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableBbaService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.library.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;

@Service(DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_BBA")
public class DmDomainProfitableDdBbaServiceImpl implements DmDomainProfitableBbaService {

    @Qualifier((DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_COMMON"))
    @Autowired
    private DmDomainProfitableCommonService dmDomainProfitableCommonService;

    @Autowired
    private DmDomainProfitableDdBbaDao dmDomainProfitableDdBbaDao;

    @Override
    public void calCmUnitDevelopAmount(DmIcgProcVo dmIcgProcVo) {
        // 清除之前的月度已赚保费
        dmDomainProfitableDdBbaDao.deleteCmUnitDevelopAmount(dmIcgProcVo.getDataKey());
        Date beginDate = null;
        // 获取yearMonth第一条
        try {
            beginDate = DateUtil.strToDate(dmIcgProcVo.getYearMonth() + "01", "yyyyMMdd");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        // 获取发展期数量
        int devMonthCount = dmDomainProfitableDdBbaDao.getDevMonthCount(dmIcgProcVo.getDataKey(), beginDate);
        for (int i = 0; i < devMonthCount; i++) {
            // 获取当期发展期的起始时间
            Date newBegindate = DateUtil.addMonths(beginDate, i);
            // 获取当期发展期的结束时间
            Date endDate = DateUtil.getLastDateByYearMonth(DateUtil.dateToStr(newBegindate, "yyyyMM"));
            dmIcgProcVo.setBeginDate(newBegindate);
            dmIcgProcVo.setEndDate(endDate);
            dmDomainProfitableDdBbaDao.calCmUnitDevelopAmount(dmIcgProcVo,i);
        }

    }

    @Override
    public void calIacfRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdBbaDao.calIacfRate(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdBbaDao.calCmUnitExpectFutureAmount(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdBbaDao.calCmUnitExpectLossAmount(dmIcgProcVo);
    }

    @Override
    public void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdBbaDao.calCmUnitCostRate(dmIcgProcVo);
    }

    @Override
    public void Profitable(DmIcgProcVo dmIcgProcVo) {
        // 初始化盈亏判断计量单元
        dmDomainProfitableDdBbaDao.deleteDuctCmunitProfitable(dmIcgProcVo);
        dmDomainProfitableDdBbaDao.insertDuctCmunitProfitable(dmIcgProcVo);
        int countDuctCmunitProfitable = dmDomainProfitableDdBbaDao.getCountDuctCmunitProfitable(dmIcgProcVo.getDataKey(), null);
        if (countDuctCmunitProfitable == 0) {
            return;
        }
        // 初始化参数
        dmDomainProfitableCommonService.prepareQuota(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareStressLossRate(dmIcgProcVo);

        dmDomainProfitableCommonService.prepareDevelopQuota(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareInterestRate(dmIcgProcVo);

        // 获取月度已赚保费
        this.calCmUnitDevelopAmount(dmIcgProcVo);
        // 配置检查
        dmDomainProfitableCommonService.checkFactor(dmIcgProcVo);
        int errorCount = dmDomainProfitableDdBbaDao.getCountDuctCmunitProfitable(dmIcgProcVo.getDataKey(), "2");
        // 配置检查不通过执行操作
        if ( errorCount > 0 ){
            dmDomainProfitableDdBbaDao.updateProfitableEstimateCheckFail(dmIcgProcVo.getDataKey());
            return;
        }

        // 跟單IACF率
        this.calIacfRate(dmIcgProcVo);
        // 预期未来现金流现值, 维持费用, 跟单IACF费用,非跟单IACF费用
        this.calCmUnitExpectFutureAmount(dmIcgProcVo);
        // 预期赔付现金流现值
        this.calCmUnitExpectLossAmount(dmIcgProcVo);
        // 基本情景成本率，压力情景成本率
        this.calCmUnitCostRate(dmIcgProcVo);

        // 更新盈亏结果
        dmDomainProfitableDdBbaDao.Profitable(dmIcgProcVo);
        // 盈亏结果回写计量单元表
        dmDomainProfitableDdBbaDao.updateProfitableEstimateResult(dmIcgProcVo);


    }
}
