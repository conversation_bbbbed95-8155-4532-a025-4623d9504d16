package com.ss.ifrs.datamgr.domain.service.push;

import com.ss.ifrs.datamgr.pojo.push.DataPushAccVo;
import com.ss.ifrs.datamgr.pojo.push.DataPushVo;

public interface DmDomainDataPushService {

    /**
     * 费用分摊数据推送
     * @param vo
     */
    void expDataPush(DataPushVo vo);

    /**
     * 精算平台数据推送
     *
     * @param vo
     */
    void atrDataPush(DataPushVo vo);

    /**
     * 会计平台数据推送
     * @param vo
     */
    void accDataPush(DataPushVo vo);
    /*
    * 会计平台按日推送
    * */
    void accDataPushDiary(DataPushAccVo vo);
}
