package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.profitable.DmDomainProfitableDdPaaDao;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitablePaaService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_PAA")
public class DmDomainProfitableDdPaaServiceImpl implements DmDomainProfitablePaaService {

    @Qualifier((DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_COMMON"))
    @Autowired
    private DmDomainProfitableCommonService dmDomainProfitableCommonService;

    @Autowired
    private DmDomainProfitableDdPaaDao dmDomainProfitableDdPaaDao;

    @Override
    public void calCmUnitDevelopAmount(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmSetAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdPaaDao.deleteSetAmount(dmIcgProcVo.getDataKey());
        dmDomainProfitableDdPaaDao.calCmSetAmount(dmIcgProcVo);
    }

    @Override
    public void calIacfRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdPaaDao.calIacfRate(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdPaaDao.calCmUnitExpectFutureAmount(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdPaaDao.calCmUnitExpectLossAmount(dmIcgProcVo);
    }

    @Override
    public void calCmSetCostRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdPaaDao.calCmSetCostRate(dmIcgProcVo);
    }

    @Override
    public void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableDdPaaDao.calCmUnitCostRate(dmIcgProcVo);
    }
    @Override
    public void Profitable(DmIcgProcVo dmIcgProcVo) {
        // 初始化盈亏判断计量单元
        dmDomainProfitableDdPaaDao.deleteDuctCmunitProfitable(dmIcgProcVo);
        dmDomainProfitableDdPaaDao.insertDuctCmunitProfitable(dmIcgProcVo);
        int countDuctCmunitProfitable = dmDomainProfitableDdPaaDao.getCountDuctCmunitProfitable(dmIcgProcVo.getDataKey(),null);
        if ( countDuctCmunitProfitable == 0){
            return;
        }
        // 初始化参数
        dmDomainProfitableCommonService.prepareQuota(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareDevelopQuota(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareInterestRate(dmIcgProcVo);
        // 设置集合保费，费用
        this.calCmSetAmount(dmIcgProcVo);
        // 参数配置检查
        dmDomainProfitableCommonService.checkFactor(dmIcgProcVo);
        int errorCount = dmDomainProfitableDdPaaDao.getCountDuctCmunitProfitable(dmIcgProcVo.getDataKey(), "2");
        // 检查不通过执行操作
        if ( errorCount != 0 ){
            dmDomainProfitableDdPaaDao.updateProfitableEstimateCheckFail(dmIcgProcVo.getDataKey());
            return;
        }

        // 跟單IACF率
        this.calIacfRate(dmIcgProcVo);
        // 集合成本率
        this.calCmSetCostRate(dmIcgProcVo);
        // 预期未来现金流现值, 维持费用, 跟单IACF费用,非跟单IACF费用
        this.calCmUnitExpectFutureAmount(dmIcgProcVo);
        // 预期赔付现金流现值
        this.calCmUnitExpectLossAmount(dmIcgProcVo);
        // 基本情景成本率(非金融风险调整)
        this.calCmUnitCostRate(dmIcgProcVo);

        // 更新盈亏结果
        dmDomainProfitableDdPaaDao.Profitable(dmIcgProcVo);

        // 盈亏结果回写计量单元表
        dmDomainProfitableDdPaaDao.updateProfitableEstimateResult(dmIcgProcVo);

    }
}
