package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AtrBussIbnrClaimImportService {
    
    Page<AtrBussClaimImportMainVo> searchXoClaimPage(AtrBussClaimImportMainVo ibnrImportMainVo, Pageable pageParam);

    void ibnrDataConfirm(AtrBussClaimImportMainVo ibnrImportMainVo, Long userId);

    List<AtrBussClaimImportMainVo> findCLList(AtrBussClaimImportMainVo ibnrImportMainVo);

    AtrBussClaimImportMainVo findById(Long becfMainId);

    AtrBussClaimImportMainVo deleteCLByBecfId(Long becfMainId);

   AtrBussClaimImportMainVo findByVo(AtrBussClaimImportMainVo ibnrImportMainVo);

    void claimExcelImport(MultipartFile file, AtrBussClaimImportMainVo ibnrImportMainVo, Long userId) throws Exception ;
    
}
