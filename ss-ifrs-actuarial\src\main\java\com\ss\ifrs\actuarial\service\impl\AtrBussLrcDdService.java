package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrBussRPGKey;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd.*;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 预期保费现金流 service （直保&临分分入业务）
 *
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussLrcDdService extends AbstractAtrBussLrcService {

    private static final String Q_LAPSE_RATIO = EcfUtil.Q_LAPSE_RATIO;
    private static final String Q_CLAIM_SETTLED_PATTERN = EcfUtil.Q_LRC_CLAIM_SETTLED_PATTERN;
    private static final String Q_CLAIM_RATIO = EcfUtil.Q_LRC_CLAIM_RATIO;
    private static final String Q_MT_RATIO = EcfUtil.Q_LRC_MT_RATIO;
    private static final String Q_ULAE_RATIO = EcfUtil.Q_LRC_ULAE_RATIO;
    private static final String Q_RA_RATIO = EcfUtil.Q_LRC_RA_RATIO;

    private final Map<AtrBussRPGKey, AtrBussLrcDdIcg> icgMap = new HashMap<>();

    // 存储包含endorse_type_code为15或16的policy_no
    private final Set<String> specialEndorseTypePolicies = new HashSet<>();

    /*  part index start */
    private final IndexFactory<AtrDapLrcDdPaymentPlan> paymentPlanIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidIacfIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidIacfIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidBadDebtIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidBadDebtIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curExpAllocInIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curExpAllocOutIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> preExpAllocInIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> preExpAllocOutIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidNetFeeIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidNetFeeIndex = new IndexFactory<>();
    private final IndexFactory<AtrDuctLrcDdIcuPre> preIcuIndex = new IndexFactory<>();
    // 统计新旧保费，index 0 为旧保费,index 1 为新保费
    private final Map<List<String>, BigDecimal[]> icgPremiumIndex = new HashMap<>();
    /*  part index end */


    public void entry(String actionNo, Long entityId, String yearMonth) {
        initEnvParams(actionNo, entityId, yearMonth, "DD");
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            initAbp(abp);
            logDebug("collectData");
            collectData();
            logDebug("calcIcu");
            calcIcu();
            logDebug("calcIcg");
            calcIcg();
            logDebug("saveIcgPremium");
            saveIcgPremium();
            logDebug("abp-end");
            abp.end();
            logDebug("end");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException(e);
        }
    }

    private void calcIcg() {
        icgMap.values().forEach(this::calcIcg);
    }

    private void calcIcg(AtrBussLrcDdIcg icg) {
        long mainId = icgMainIdGen.incrementAndGet();

        int remainingMonths = icg.getRemainingMonths();
        String riskClassCode = icg.getRiskClassCode();
        String icgNo = icg.getIcgNo();
        int maxClmQuotaDevNo = icg.getMaxClmQuotaDevNo();
        Map<Integer, BigDecimal> devEdPremiumMap = icg.getDevEdPremiumMap();
        Map<Integer, BigDecimal> devIacfMap = icg.getDevIacfMap();
        Map<Integer, BigDecimal> devNetFeeMap = icg.getDevNetFeeMap();
        Map<Integer, BigDecimal> devBadDebtMap = icg.getDevBadDebtMap();
        Map<Integer, BigDecimal> devIaehcInMap = icg.getDevIaehcInMap();
        Map<Integer, BigDecimal> devIaehcOutMap = icg.getDevIaehcOutMap();
        Map<Integer, BigDecimal> devEdNetFeeMap = icg.getDevEdNetFeeMap();
        Map<Integer, BigDecimal> devEdIacfMap = icg.getDevEdIacfMap();
        Map<Integer, BigDecimal> devEdIaehcInMap = icg.getDevEdIaehcInMap();
        Map<Integer, BigDecimal> devEdIaehcOutMap = icg.getDevEdIaehcOutMap();

        // 预期赔付率
        icg.setClaimRate(BigDecimal.valueOf(nvl(getQuota(Q_CLAIM_RATIO, riskClassCode, icgNo))));
        // 维持费用率
        icg.setMtRate(BigDecimal.valueOf(nvl(getQuota(Q_MT_RATIO, riskClassCode, icgNo))));
        // 退保率
        icg.setLapseRate(BigDecimal.valueOf(nvl(getQuota(Q_LAPSE_RATIO, riskClassCode, icgNo))));
        // 间接理赔费用率
        icg.setUlaeRate(BigDecimal.valueOf(nvl(getQuota(Q_ULAE_RATIO, riskClassCode, icgNo))));
        // 非金融风险调整率
        icg.setRaRate(BigDecimal.valueOf(nvl(getQuota(Q_RA_RATIO, riskClassCode, icgNo))));

        // 基于赔付计算的最大发展期
        int maxClmDevNo = 0;
        if (maxClmQuotaDevNo > 0) {
            maxClmDevNo = remainingMonths + maxClmQuotaDevNo - 1;
        }

        // 最大发展期
        int maxDevNo = Math.max(remainingMonths, maxClmDevNo);

        // 初始化发展期
        List<AtrBussLrcDdIcgDev> devVos = new ArrayList<>();
        for (int i = 0; i <= maxDevNo; i++) {
            AtrBussLrcDdIcgDev devVo = new AtrBussLrcDdIcgDev();
            devVos.add(devVo);
            devVo.setMainId(mainId);
            devVo.setDevNo(i);
            devVo.setYearMonth(yearMonth);
            devVo.setEdPremium(devEdPremiumMap.get(i));
        }

        // 退保费、维持费用
        for (int i = 1; i <= remainingMonths; i++) {
            AtrBussLrcDdIcgDev devVo = devVos.get(i);
            BigDecimal edPremium = nvl(devVo.getEdPremium());
            BigDecimal lapse = round(edPremium.multiply(icg.getLapseRate()));
            devVo.setLapse(lapse);
            devVo.setMtFee(round(edPremium.subtract(lapse).multiply(icg.getMtRate())));
        }

        // 预期赔付、间接理赔费用、RA
        BigDecimal claimRatio = icg.getClaimRate();
        for (int i = 1; i <= maxClmDevNo; i++) {
            BigDecimal claim = BigDecimal.ZERO;
            for (int d = 1; d <= i && d <= remainingMonths; d++) {
                int q = i - d + 1;
                if (q > maxClmQuotaDevNo) {
                    continue;
                }
                AtrBussLrcDdIcgDev devVo = devVos.get(d);
                BigDecimal edPremium = nvl(devVo.getEdPremium());
                BigDecimal lapse = nvl(devVo.getLapse());
                BigDecimal pattern = BigDecimal.valueOf(nvl(getQuota(Q_CLAIM_SETTLED_PATTERN, riskClassCode, icgNo, q)));
                claim = round(claim.add(edPremium.subtract(lapse).multiply(claimRatio).multiply(pattern)));
            }

            AtrBussLrcDdIcgDev devVo = devVos.get(i);
            BigDecimal ulae = round(claim.multiply(icg.getUlaeRate()));
            BigDecimal mtFee = nvl(devVo.getMtFee());
            devVo.setClaim(claim);
            devVo.setUlae(ulae);
            devVo.setRa(round(claim.add(mtFee).add(ulae).multiply(icg.getRaRate())));
        }

        // 同步qtc性能优化，将icu发展期的数据合并到icg发展期
        for (AtrBussLrcDdIcgDev devVo : devVos) {
            Integer devNo = devVo.getDevNo();
            devVo.setRecvPremium(icg.getDevRecvPremiumMap().get(devNo));
            devVo.setNetFee(devNetFeeMap.get(devNo));
            devVo.setIacf(devIacfMap.get(devNo));
            devVo.setIaehcIn(devIaehcInMap.get(devNo));
            devVo.setIaehcOut(devIaehcOutMap.get(devNo));
            devVo.setBadDebt(devBadDebtMap.get(devNo));
            devVo.setEdNetFee(devEdNetFeeMap.get(devNo));
            devVo.setEdIacf(devEdIacfMap.get(devNo));
            devVo.setEdIaehcIn(devEdIaehcInMap.get(devNo));
            devVo.setEdIaehcOut(devEdIaehcOutMap.get(devNo));
        }

        icg.setId(mainId);
        abp.insert(icg);
        devVos.forEach(abp::insert);
    }

    private void collectData() {
        logDebug("initMainIdGen");
        initMainIdGen();
        partitionBaseData();
        logDebug("collectQuotaDef");
        collectQuotaDef();
        logDebug("collectQuota");
        collectQuota();
        logDebug("collectPreIcgPremium");
        collectPreIcgPremium();

    }

    private void collectExpAlloc(Map<?, ?> paramMap) {
        curExpAllocInIndex.clear();
        curExpAllocOutIndex.clear();
        List<AtrSD7> list = atrBussLrcDdDao.findExpAlloc(paramMap);
        List<AtrSD7> expAllocIn = list.stream().filter(t -> "0".equals(t.getS4())).collect(Collectors.toList());
        for (AtrSD7 a : expAllocIn) {
            String policyNo = a.getS1();
            String endorseSeqNo = a.getS2();
            String kindCode = a.getS3();
            List<String> unitKey = createUnitKey(policyNo, endorseSeqNo, kindCode);
            curExpAllocInIndex.plus(unitKey, BigDecimal.valueOf(nvl(a.getD1())));
        }
        List<AtrSD7> expAllocOut = list.stream().filter(t -> "1".equals(t.getS4())).collect(Collectors.toList());
        for (AtrSD7 a : expAllocOut) {
            String policyNo = a.getS1();
            String endorseSeqNo = a.getS2();
            String kindCode = a.getS3();
            List<String> unitKey = createUnitKey(policyNo, endorseSeqNo, kindCode);
            curExpAllocOutIndex.plus(unitKey, BigDecimal.valueOf(nvl(a.getD1())));
        }
    }

    private void collectPreExpAlloc(Map<?, ?> paramMap) {
        preExpAllocInIndex.clear();
        preExpAllocOutIndex.clear();
        
        List<AtrSD7> list = atrBussLrcDdDao.finPreExpAlloc(paramMap);

        for (AtrSD7 a : list) {
            String policyNo = a.getS1();
            String endorseSeqNo = a.getS2();
            String kindCode = a.getS3();
            String schemeStatus = a.getS4();
            List<String> unitKey = createUnitKey(policyNo, endorseSeqNo, kindCode);
            BigDecimal amount = BigDecimal.valueOf(nvl(a.getD1()));
            
            if ("0".equals(schemeStatus)) {
                preExpAllocInIndex.plus(unitKey, amount);
            } else if ("1".equals(schemeStatus)) {
                preExpAllocOutIndex.plus(unitKey, amount);
            }
        }
    }

    private void calcIcu() {
        for (int partNo = 0; partNo < parts; partNo++) {
            Map<String, Object> paramMap = new HashMap<>(commonParamMap);
            paramMap.put("pn", partNo);

            List<AtrBussLrcDdIcu> baseVos = new ArrayList<>();

            // 并行准备该批次计算所需的数据
            List<String> marks = new ArrayList<>();
            marks.add("collectPaymentPlan");
            marks.add("collectPreIcu");
            marks.add("collectDapPaid");
            marks.add("collectExpAlloc");
            marks.add("collectPreExpAlloc");
            marks.add("getPartBaseVos");
            ThreadUtil.runThreadsThrow(marks, (mark) -> {
                logDebug(mark, "start");
                if ("collectPaymentPlan".equals(mark)) {
                    collectPaymentPlan(paramMap);
                } else if ("collectPreIcu".equals(mark)) {
                    collectPreIcu(paramMap);
                } else if ("collectDapPaid".equals(mark)) {
                    collectDapPaid(paramMap);
                } else if ("collectExpAlloc".equals(mark)) {
                    collectExpAlloc(paramMap);
                } else if ("collectPreExpAlloc".equals(mark)) {
                    collectPreExpAlloc(paramMap);
                } else if ("getPartBaseVos".equals(mark)) {
                    baseVos.addAll(atrBussLrcDdDao.getPartBaseVos(paramMap));
                }
                logDebug(mark, "end");
            }, marks::size);

            logDebug("calcIcu-part-" + partNo);
            baseVos.forEach(this::calcIcu);
            logDebug("calcIcu-part-" + partNo + "-end");
        }
    }

    private void collectDapPaid(Map<?, ?> paramMap) {
        prePaidNetFeeIndex.clear();
        curPaidNetFeeIndex.clear();
        prePaidPremiumIndex.clear();
        curPaidPremiumIndex.clear();
        prePaidIacfIndex.clear();
        curPaidIacfIndex.clear();
        prePaidBadDebtIndex.clear();
        curPaidBadDebtIndex.clear();

        List<AtrDapLrcDdPaid> vos = atrBussLrcDdDao.findDapPaid(paramMap);
        for (AtrDapLrcDdPaid vo : vos) {
            String yearMonth = vo.getYearMonth();
            List<?> key = Arrays.asList(vo.getPolicyNo(), vo.getEndorseSeqNo(), vo.getKindCode());
            if (yearMonth.compareTo(this.yearMonth) < 0) {
                prePaidPremiumIndex.plus(key, nvl(vo.getPremium()));
                prePaidNetFeeIndex.plus(key, nvl(vo.getNetFee()));
                prePaidIacfIndex.plus(key, nvl(vo.getIacf()));
                prePaidBadDebtIndex.plus(key, nvl(vo.getBadDebt()));
            } else if (yearMonth.equals(this.yearMonth)) {
                curPaidPremiumIndex.plus(key, nvl(vo.getPremium()));
                curPaidNetFeeIndex.plus(key, nvl(vo.getNetFee()));
                curPaidIacfIndex.plus(key, nvl(vo.getIacf()));
                curPaidBadDebtIndex.plus(key, nvl(vo.getBadDebt()));
            }
        }
    }

    private void collectPreIcu(Map<String, Object> paramMap) {
        preIcuIndex.clear();
        List<AtrDuctLrcDdIcuPre> vos = atrBussLrcDdDao.findPreIcu(paramMap);
        for (AtrDuctLrcDdIcuPre vo : vos) {
            preIcuIndex.add(createUnitKey(vo.getPolicyNo(), vo.getEndorseSeqNo(), vo.getKindCode()), vo);
        }
    }

    private void collectPaymentPlan(Map<String, Object> paramMap) {
        paymentPlanIndex.clear();
        List<AtrDapLrcDdPaymentPlan> paymentPlans = atrBussLrcDdDao.findPaymentPlan(paramMap);
        for (AtrDapLrcDdPaymentPlan paymentPlan : paymentPlans) {
            String policyNo = paymentPlan.getPolicyNo();
            String endorseSeqNo = paymentPlan.getEndorseSeqNo();
            String kindCode = paymentPlan.getKindCode();
            List<?> key = Arrays.asList(policyNo, endorseSeqNo, kindCode);
            paymentPlanIndex.add(key, paymentPlan);
        }
    }

    @Override
    protected void initAbp(AsyncBatchProcessor abp) {
        super.initAbp(abp);
        abp.addType(AtrBussLrcDdIcu.class);
        abp.addType(AtrBussLrcDdIcuDev.class);
        abp.addType(AtrBussLrcDdIcg.class);
        abp.addType(AtrBussLrcDdIcgDev.class);
        abp.addType(AtrBussDdLrcIcgPremium.class);
    }

    private void initMainIdGen() {
        icuMainIdGen = new AtomicLong(atrBussLrcDdDao.getIcuMaxMainId());
        icgMainIdGen = new AtomicLong(atrBussLrcDdDao.getIcgMaxMainId());
    }

    private void collectPreIcgPremium() {
        List<AtrBussDdLrcIcgPremium> atrBussDdLrcIcgPremiums = atrBussLrcDdDao.listPreIcgPremium(commonParamMap);

        atrBussDdLrcIcgPremiums.forEach(item -> {
            List<String> key = Arrays.asList(item.getPortfolioNo(), item.getIcgNo(), item.getRiskClassCode());
            icgPremiumIndex.put(key, new BigDecimal[]{item.getNewPremium().add(item.getOldPremium()), BigDecimal.ZERO});
        });
    }

    private void partitionBaseData() {
        logDebug("insertMaxExpiryDateTable");
        atrBussLrcDdDao.truncateMaxExpiryDateTable();
        atrBussLrcDdDao.insertMaxExpiryDateTable(commonParamMap);
        EcfUtil.analyseTable(jdbcTemplate, "atr_temp_dd_lrc_g_max_expiry_date");

        logDebug("insertPrePaidPremiumTable");
        atrBussLrcDdDao.truncatePrePaidPremiumTable();
        atrBussLrcDdDao.insertPrePaidPremiumTable(commonParamMap);
        EcfUtil.analyseTable(jdbcTemplate, "atr_temp_dd_lrc_u_pre_paid_premium");

        logDebug("insertPartitionBaseData");
        atrBussLrcDdDao.truncateBaseData();
        atrBussLrcDdDao.partitionBaseData(commonParamMap);
    }

    private void calcIcu(AtrBussLrcDdIcu icu) {
        long mainId = icuMainIdGen.incrementAndGet();

        String policyNo = icu.getPolicyNo();
        String endorseSeqNo = icu.getEndorseSeqNo();
        String kindCode = icu.getKindCode();
        BigDecimal premium = nvl(icu.getPremium());
        BigDecimal netFee = nvl(icu.getNetFee());
        BigDecimal netFeeRate = premium.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                roundR(netFee.divide(premium, 15, RoundingMode.HALF_UP));
        Date effectiveDate = icu.getEffectiveDate();
        Date expiryDate = icu.getExpiryDate();

        List<?> unitKey = createUnitKey(policyNo, endorseSeqNo, kindCode);
        BigDecimal preBadDebt = prePaidBadDebtIndex.one(unitKey);

        calcIcgPremium(icu.getPortfolioNo(), icu.getIcgNo(), icu.getRiskClassCode(), icu.getContractDate(), premium);

        // 剩余月份
        int remainingMonths = 0;
        if (Dates.toChar(expiryDate, "yyyyMM").compareTo(yearMonth) > 0) {
            remainingMonths = Dates.monthsBetween(yearMonth, expiryDate);
        }

        // 最大发展期
        int maxDevNo = paymentPlanIndex.list(unitKey).stream()
                .map(t -> Dates.monthsBetween(evDateBom, t.getPayYearMonth()))
                .max(Integer::compareTo)
                .orElse(0);
        maxDevNo = ObjectUtils.max(1, remainingMonths, maxDevNo);

        // 获取特殊处理类型（已在SQL中计算）
        int specialProcessType = nvl(icu.getSpecialProcessType(), 0);
        if (specialProcessType == 1 || specialProcessType == 2) {
            // 特殊处理：类型1只计算第0期，类型2不计算发展期现金流
            maxDevNo = 0;
            remainingMonths = 0;
        }

        AtrDuctLrcDdIcuPre preIcu = preIcuIndex.one(unitKey, new AtrDuctLrcDdIcuPre());

        // 上期累计已赚
        BigDecimal preCumlEdPremium = nvl(preIcu.getPreCumlEdPremium())
                .add(nvl(preIcu.getCurEdPremium()));

        BigDecimal preCumlEdNetFee = nvl(preIcu.getPreCumlEdNetFee())
                .add(nvl(preIcu.getCurEdNetFee()));
        BigDecimal preCumlEdIacf = nvl(preIcu.getPreCumlEdIacf())
                .add(nvl(preIcu.getCurEdIacf()));
        BigDecimal preCumlEdIaehcIn = nvl(preIcu.getPreCumlEdIaehcIn())
                .add(nvl(preIcu.getCurEdIaehcIn()));
        BigDecimal preCumlEdIaehcOut = nvl(preIcu.getPreCumlEdIaehcOut())
                .add(nvl(preIcu.getCurEdIaehcOut()));
        // 上期累计实收保费
        BigDecimal preCumlPaidPremium = prePaidPremiumIndex.one(unitKey, BigDecimal.ZERO);
        // 上期累计净额手续费
        BigDecimal preCumlPaidNetFee = prePaidNetFeeIndex.one(unitKey, BigDecimal.ZERO);
        // 当期实付
        BigDecimal curPaidPremium = curPaidPremiumIndex.one(unitKey, BigDecimal.ZERO);
        // 所有实收
        BigDecimal allPaidPremium = round(preCumlPaidPremium.add(curPaidPremium));
        // 当期实收 net_fee
        BigDecimal curPaidNetFee = curPaidNetFeeIndex.one(unitKey, BigDecimal.ZERO);
        // 所有实收 net_fee
        BigDecimal allPaidNetFee = round(preCumlPaidNetFee.add(curPaidNetFee));
        // 是否保费完全实收
        boolean isAllPaidPremium = premium.abs().compareTo(allPaidPremium.abs()) <= 0;
        // 是否保费完全实收 net_fee
        boolean isAllPaidNetFee = netFee.abs().compareTo(allPaidNetFee.abs()) <= 0;
        // 当期实收 iacf
        BigDecimal curPaidIacf = curPaidIacfIndex.one(unitKey, BigDecimal.ZERO);
        // 往期实收 iacf
        BigDecimal prePaidIacf = prePaidIacfIndex.one(unitKey, BigDecimal.ZERO);
        // 非跟单获取费用
        BigDecimal iaehcIn = curExpAllocInIndex.one(unitKey, BigDecimal.ZERO);
        BigDecimal iaehcOut = curExpAllocOutIndex.one(unitKey, BigDecimal.ZERO);

        // 往期非跟单获取费用
        BigDecimal preIaehcIn = preExpAllocInIndex.one(unitKey, BigDecimal.ZERO);
        icu.setPreIaehcIn(preIaehcIn);
        BigDecimal preIaehcOut = preExpAllocOutIndex.one(unitKey, BigDecimal.ZERO);
        icu.setPreIaehcOut(preIaehcOut);
        // 是否已过期
        boolean expiryIs = !expiryDate.after(evDate);
        // 承保总天数
        int totalDays = Dates.daysBetween(effectiveDate, expiryDate) + 1;

        // 初始化发展期
        List<AtrBussLrcDdIcuDev> devVos = new ArrayList<>();
        for (int i = 0; i <= maxDevNo; i++) {
            AtrBussLrcDdIcuDev dev = new AtrBussLrcDdIcuDev();
            devVos.add(dev);
            dev.setMainId(mainId);
            dev.setDevNo(i);
            dev.setYearMonth(yearMonth);

            Date devDate = devDate(i);
            Date devDateBom = Dates.truncMonth(devDate);
            String devYearMonth = Dates.toChar(devDateBom, "yyyyMM");

            // 已赚保费及相关
            if (specialProcessType == 1 && i == 0) {
                // 特殊处理类型1：只计算第0期，第0期的已赚金额 = 签单保费 - 历史累计已赚
                BigDecimal specialEdPremium = premium.subtract(preCumlEdPremium);
                BigDecimal specialEdRate = premium.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                        roundR(specialEdPremium.divide(premium, 15, RoundingMode.HALF_UP));

                dev.setEdRate(specialEdRate);
                dev.setEdPremium(round(specialEdPremium));
                dev.setEdIacf(round(nvl(icu.getIacf()).multiply(specialEdRate)));
            } else if (specialProcessType == 2) {
                // 特殊处理类型2：不计算发展期现金流
                dev.setEdRate(BigDecimal.ZERO);
                dev.setEdIacf(BigDecimal.ZERO);
                dev.setEdPremium(BigDecimal.ZERO);
            } else if (i <= remainingMonths) {
                // 正常处理：计算已赚保费
                double days = 0;
                if (totalDays > 0) {
                    // 终保日期在评估期期末 前
                    if (expiryIs) {
                        // 首次计量
                        if (icu.getDapYearMonth().equals(yearMonth) && i == 0) {
                            days = totalDays;
                        }else {
                            // 非首次计量
                            days =  Math.max(Dates.daysBetween(devDateBom,expiryDate),0) + 1;
                        }
                    } else {
                        // 未终保的情况

                        // 生效日期在当前评估期期期末 后
                        if (effectiveDate.after(devDate)) {
                            days = 0;
                            // 生效日期在当前评估期期初 后
                        } else if (!effectiveDate.before(devDateBom)) {
                            // 失效日期在当前评估期期末 前
                            if (!expiryDate.after(devDate)) {
                                days = totalDays;
                            } else {
                                // 失效日期在当前评估期期末后
                                days = Dates.daysBetween(effectiveDate, devDate) + 1;
                            }
                            // 生效日期在当前评估期前
                        } else {
                            // 失效日期在当前评估期期末 前
                            if (!expiryDate.after(devDate)) {
                                days = Dates.daysBetween(devDateBom, expiryDate) + 1;
                            } else {
                                if ( i == 0 && icu.getDapYearMonth().equals(yearMonth) && icu.getIssueDate().after(effectiveDate) ) {
                                    days = Dates.daysBetween(effectiveDate, devDate) + 1;
                                }else{
                                    days = Dates.daysBetween(devDateBom, devDate) + 1;
                                }
                            }
                        }
                    }
                }

                BigDecimal daysDecimal = BigDecimal.valueOf(days);
                BigDecimal totalDaysDecimal = BigDecimal.valueOf(totalDays);
                BigDecimal edRate = totalDaysDecimal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                        roundR(daysDecimal.divide(totalDaysDecimal, 15, RoundingMode.HALF_UP));

                dev.setEdRate(edRate);
                dev.setEdPremium(round(premium.multiply(edRate)));
                dev.setEdIacf(round(nvl(icu.getIacf()).multiply(edRate)));
            } else {
                // 超出剩余月数的发展期，不计算已赚
                dev.setEdRate(BigDecimal.ZERO);
                dev.setEdIacf(BigDecimal.ZERO);
                dev.setEdPremium(BigDecimal.ZERO);
            }

            /*
               -- 第 0 期应收 =   评估月的实收
               -- 如果累计实收比应收大， 则第 1 期只放 (应收保费 - 累计实收), 不再算后面期次
               -- 第 1 期应收 =  (当期及之前的缴费计划保费之和 - 累计实收) + 第 1 期缴费
               -- 第 n 期应收 =  第 n 期缴费
             */
            // 应收保费
            if (specialProcessType == 2) {
                // 不计算发展期现金流的情况，应收保费为0
                dev.setRecvPremium(BigDecimal.ZERO);
            } else if (i == 0) {
                dev.setRecvPremium(curPaidPremium);
            } else if (isAllPaidPremium) {
                if (i == 1) {
                    dev.setRecvPremium(round(premium.subtract(allPaidPremium)));
                }
            } else {
                List<AtrDapLrcDdPaymentPlan> plans = paymentPlanIndex.list(unitKey);
                BigDecimal curPlanAmount = BigDecimal.ZERO;
                BigDecimal prePlanAmount = BigDecimal.ZERO;
                for (AtrDapLrcDdPaymentPlan plan : plans) {
                    if (plan.getPayYearMonth() != null) {
                        if (plan.getPayYearMonth().equals(devYearMonth)) {
                            curPlanAmount = round(curPlanAmount.add(nvl(plan.getPremium())));
                        } else if (plan.getPayYearMonth().compareTo(yearMonth) <= 0) {
                            prePlanAmount = round(prePlanAmount.add(nvl(plan.getPremium())));
                        }
                    }
                }
                if (i == 1) {
                    dev.setRecvPremium(round(prePlanAmount.subtract(allPaidPremium).add(curPlanAmount)));
                } else {
                    dev.setRecvPremium(curPlanAmount);
                }
            }

            // net_fee
            if ("FB".equals(icu.getBusinessType())) {
                if (specialProcessType == 2) {
                    // 不计算发展期现金流的情况
                    dev.setEdNetFee(BigDecimal.ZERO);
                    dev.setNetFee(BigDecimal.ZERO);
                } else {
                    dev.setEdNetFee(round(netFee.multiply(dev.getEdRate())));
                    if (i == 0) {
                    dev.setNetFee(curPaidNetFee);
                } else if (isAllPaidNetFee) {
                    if (i == 1) {
                        dev.setNetFee(round(netFee.subtract(allPaidNetFee)));
                    }
                } else {
                    List<AtrDapLrcDdPaymentPlan> plans = paymentPlanIndex.list(unitKey);
                    BigDecimal curPlanAmount = BigDecimal.ZERO;
                    BigDecimal prePlanAmount = BigDecimal.ZERO;
                    for (AtrDapLrcDdPaymentPlan plan : plans) {
                        BigDecimal planNetFee = round(nvl(plan.getPremium()).multiply(netFeeRate));
                        if (plan.getPayYearMonth() != null) {
                            if (plan.getPayYearMonth().equals(devYearMonth)) {
                                curPlanAmount = round(curPlanAmount.add(planNetFee));
                            } else if (plan.getPayYearMonth().compareTo(yearMonth) <= 0) {
                                prePlanAmount = round(prePlanAmount.add(planNetFee));
                            }
                        }
                    }
                    if (i == 1) {
                        dev.setNetFee(round(prePlanAmount.subtract(allPaidNetFee).add(curPlanAmount)));
                    } else {
                        dev.setNetFee(curPlanAmount);
                    }
                }
                }
            }

            // 减值现金流
            if (specialProcessType == 2) {
                // 不计算发展期现金流的情况
                dev.setBadDebt(BigDecimal.ZERO);
            } else if (i == 0) {
                dev.setBadDebt(curPaidBadDebtIndex.one(unitKey, BigDecimal.ZERO));
            }

            // iacf
            if (specialProcessType == 2) {
                // 不计算发展期现金流的情况
                dev.setIacf(BigDecimal.ZERO);
            } else if (i == 0) {
                // 当期iacf （实收）
                dev.setIacf(curPaidIacf);
            } else if (i == 1) {
                // 剩余iacf
                dev.setIacf(nvl(icu.getIacf()).subtract(prePaidIacf).subtract(curPaidIacf));
            }

            // iaehc
            if (specialProcessType == 2) {
                // 不计算发展期现金流的情况
                dev.setIaehcIn(BigDecimal.ZERO);
                dev.setIaehcOut(BigDecimal.ZERO);
                dev.setEdIaehcIn(BigDecimal.ZERO);
                dev.setEdIaehcOut(BigDecimal.ZERO);
            } else {
                if (i == 0) {
                    dev.setIaehcIn(iaehcIn);
                    dev.setIaehcOut(iaehcOut);
                }
                dev.setEdIaehcIn(iaehcIn.add(preIaehcIn).multiply(dev.getEdRate()));
                dev.setEdIaehcOut(iaehcOut.add(preIaehcOut).multiply(dev.getEdRate()));
            }
        }

        // 调整第0期的已赚费用，避免遗漏当期新增费用
        if (!devVos.isEmpty()) {
            AtrBussLrcDdIcuDev dev0 = devVos.get(0);
            if (dev0 != null) {
                // 计算所有期间的已赚比例总和
                BigDecimal totalEdRate = devVos.stream()
                        .filter(Objects::nonNull)  // 过滤null元素
                        .map(dev -> nvl(dev.getEdRate()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 计算未摊分的比例（1 - 已赚比例总和）
                BigDecimal unallocatedRate = BigDecimal.ONE.subtract(totalEdRate);

                // 用未摊分比例乘以当期iaehc费用，加到第0期
                BigDecimal iaehcInAdjustment = iaehcIn.multiply(unallocatedRate);
                BigDecimal iaehcOutAdjustment = iaehcOut.multiply(unallocatedRate);

                dev0.setEdIaehcIn(round(nvl(dev0.getEdIaehcIn()).add(iaehcInAdjustment)));
                dev0.setEdIaehcOut(round(nvl(dev0.getEdIaehcOut()).add(iaehcOutAdjustment)));
            }
        }


        
        // 执行尾差处理
        handleAmountTailDifference(devVos, premium, netFee, icu.getIacf(), iaehcIn.add(preIaehcIn), iaehcOut.add(preIaehcOut),
                preCumlEdPremium, preCumlEdNetFee,
                preCumlEdIacf,preCumlEdIaehcIn,preCumlEdIaehcOut);
        
        // 未来保费现金流（含当期）
        BigDecimal recvPremium = devVos.stream()
                .map(dev -> nvl(dev.getRecvPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 已赚保费汇总
        BigDecimal edPremiumSum = devVos.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 进行各项校验
        validateIcuConsistency(policyNo, endorseSeqNo, kindCode, premium, preCumlPaidPremium, recvPremium,
                edPremiumSum, preIcu, devVos, preCumlPaidNetFee, prePaidIacf, iaehcIn.add(preIaehcIn), iaehcOut.add(preIaehcOut),preCumlEdIaehcIn,preCumlEdIaehcOut);

        icu.setId(mainId);
        icu.setActionNo(actionNo);
        icu.setEntityId(entityId);
        icu.setYearMonth(yearMonth);
        icu.setRemainingMonths(remainingMonths);
        icu.setNetFeeRate(netFeeRate);
        icu.setTotalBadDebt(preBadDebt == null ? BigDecimal.ZERO : preBadDebt.add(nvl(devVos.get(0).getBadDebt())));
        icu.setIaehcIn(iaehcIn);
        icu.setIaehcOut(iaehcOut);
        icu.setPreCumlPaidPremium(preCumlPaidPremium);
        icu.setPreCumlPaidNetFee(preCumlPaidNetFee);
        icu.setPreCumlPaidIacf(prePaidIacf);
        icu.setPreCumlEdPremium(preCumlEdPremium);



        icu.setPreCumlEdNetFee(preCumlEdNetFee);
        icu.setPreCumlEdIacf(preCumlEdIacf);
        icu.setPreCumlEdIaehcIn(preCumlEdIaehcIn);
        icu.setPreCumlEdIaehcOut(preCumlEdIaehcOut);

        // 安全获取dev_no=0的发展期数据，避免NP风险
        if (!devVos.isEmpty()) {
            AtrBussLrcDdIcuDev dev0 = devVos.get(0);
            if (dev0 != null) {
                icu.setCurEdPremium(nvl(dev0.getEdPremium()));
                icu.setCurEdNetFee(nvl(dev0.getEdNetFee()));
                icu.setCurEdIacf(nvl(dev0.getEdIacf()));
                icu.setCurEdIaehcIn(nvl(dev0.getEdIaehcIn()));
                icu.setCurEdIaehcOut(nvl(dev0.getEdIaehcOut()));

                // 设置冗余字段，存储dev_no=0的关键数据，避免推送时关联子表
                icu.setCurBadDebt(nvl(dev0.getBadDebt()));
                icu.setCurPaidPremium(nvl(dev0.getRecvPremium()));
                icu.setCurIacf(nvl(dev0.getIacf()));
                icu.setCurPaidNetFee(nvl(dev0.getNetFee()));
            } else {
                // dev0为null时设置默认值
                icu.setCurEdPremium(BigDecimal.ZERO);
                icu.setCurEdNetFee(BigDecimal.ZERO);
                icu.setCurEdIacf(BigDecimal.ZERO);
                icu.setCurEdIaehcIn(BigDecimal.ZERO);
                icu.setCurEdIaehcOut(BigDecimal.ZERO);
                icu.setCurBadDebt(BigDecimal.ZERO);
                icu.setCurPaidPremium(BigDecimal.ZERO);
                icu.setCurIacf(BigDecimal.ZERO);
                icu.setCurPaidNetFee(BigDecimal.ZERO);
            }
        } else {
            // devVos为空时设置默认值
            icu.setCurEdPremium(BigDecimal.ZERO);
            icu.setCurEdNetFee(BigDecimal.ZERO);
            icu.setCurEdIacf(BigDecimal.ZERO);
            icu.setCurEdIaehcIn(BigDecimal.ZERO);
            icu.setCurEdIaehcOut(BigDecimal.ZERO);
            icu.setCurBadDebt(BigDecimal.ZERO);
            icu.setCurPaidPremium(BigDecimal.ZERO);
            icu.setCurIacf(BigDecimal.ZERO);
            icu.setCurPaidNetFee(BigDecimal.ZERO);
        }

        abp.insert(icu);
        devVos.forEach(abp::insert);
        collectIcg(icu, devVos);
    }

    private void saveIcgPremium() {
        icgPremiumIndex.forEach((key, value) -> {
            AtrBussDdLrcIcgPremium atrBussDdLrcIcgPremium = new AtrBussDdLrcIcgPremium();
            atrBussDdLrcIcgPremium.setActionNo(actionNo);
            atrBussDdLrcIcgPremium.setYearMonth(yearMonth);
            atrBussDdLrcIcgPremium.setPortfolioNo(key.get(0));
            atrBussDdLrcIcgPremium.setIcgNo(key.get(1));
            atrBussDdLrcIcgPremium.setRiskClassCode(key.get(2));
            atrBussDdLrcIcgPremium.setOldPremium(value[0]);
            atrBussDdLrcIcgPremium.setNewPremium(value[1]);
            abp.insert(atrBussDdLrcIcgPremium);
        });
    }

    // 新旧保费汇总，合同确认日期为当月则为新保费，旧保费采用上一个月的
    private void calcIcgPremium(String portfolioNo, String icgNo, String riskClassCode, Date contractDate, BigDecimal premium) {
        List<String> key = Arrays.asList(portfolioNo, icgNo, riskClassCode);
        if (Dates.toChar(contractDate, "yyyyMM").equals(yearMonth)) {
            if (icgPremiumIndex.containsKey(key)) {
                BigDecimal[] doubles = icgPremiumIndex.get(key);
                doubles[1] = doubles[1].add(premium);
                icgPremiumIndex.put(key, doubles);

            } else {
                icgPremiumIndex.put(key, new BigDecimal[]{BigDecimal.ZERO, premium});
            }
        }
    }

    private void collectIcg(AtrBussLrcDdIcu icu, List<AtrBussLrcDdIcuDev> icuDevs) {
        String riskClassCode = icu.getRiskClassCode();
        String portfolioNo = icu.getPortfolioNo();
        String icgNo = icu.getIcgNo();
        AtrBussRPGKey key = new AtrBussRPGKey();
        key.setRiskClassCode(riskClassCode);
        key.setPortfolioNo(portfolioNo);
        key.setIcgNo(icgNo);
        AtrBussLrcDdIcg icg = icgMap.get(key);
        BigDecimal totalPremium = BigDecimal.ZERO;
        BigDecimal totalNetFee = BigDecimal.ZERO;
        BigDecimal totalIacf = BigDecimal.ZERO;
        BigDecimal totalIaehcIn = BigDecimal.ZERO;
        BigDecimal totalIaehcOut = BigDecimal.ZERO;

        // 总保费/总手续费/总跟单获取费用/总非跟单获取费用
        for (AtrBussLrcDdIcuDev icuDev : icuDevs) {
            if (icuDev.getRecvPremium() != null) {
                totalPremium = totalPremium.add(icuDev.getRecvPremium());
            }
            if (icuDev.getNetFee() != null) {
                totalNetFee = totalNetFee.add(icuDev.getNetFee());
            }
            if (icuDev.getIacf() != null) {
                totalIacf = totalIacf.add(icuDev.getIacf());
            }
            if (icuDev.getIaehcIn() != null) {
                totalIaehcIn = totalIaehcIn.add(icuDev.getIaehcIn());
            }
            if (icuDev.getIaehcOut() != null) {
                totalIaehcOut = totalIaehcOut.add(icuDev.getIaehcOut());
            }
        }

        if (icg == null) {
            icg = new AtrBussLrcDdIcg();
            BeanUtils.copyProperties(icu, icg);
            icg.setRemainingMonths(icu.getRemainingMonths());
            icg.setMaxClmQuotaDevNo(getMaxClmPatternDevNo(riskClassCode, icgNo));

            icg.setBadDebt(icu.getTotalBadDebt());
            icg.setTotalPremium(icu.getPremium());
            icg.setTotalNetFee(icu.getNetFee());
            icg.setIacf(icu.getIacf());
            icg.setIaehcIn(icu.getIaehcIn().add(icu.getPreIaehcIn()));
            icg.setIaehcOut(icu.getIaehcOut().add(icu.getPreIaehcOut()));
            // 设置新增的三个字段
            icg.setBusinessType(icu.getBusinessType());
            icg.setPlJudgeRslt(icu.getPlJudgeRslt());
            icg.setIcgName(icu.getIcgName());
            icgMap.put(key, icg);
        } else {
            if (icg.getRemainingMonths() < icu.getRemainingMonths()) {
                icg.setRemainingMonths(icu.getRemainingMonths());
            }
            icg.setIacf(round(icu.getIacf().add(icg.getIacf())));
            icg.setIaehcIn(round(icu.getIaehcIn().add(icu.getPreIaehcIn()).add(icg.getIaehcIn())));
            icg.setIaehcOut(round(icu.getIaehcOut().add(icu.getPreIaehcOut()).add(icg.getIaehcOut())));
            icg.setTotalPremium(round(icu.getPremium().add(icg.getTotalPremium())));
            icg.setTotalNetFee(round(icu.getNetFee().add(icg.getTotalNetFee())));
            icg.setBadDebt(round(icu.getTotalBadDebt().add(icg.getBadDebt())));
        }

        for (AtrBussLrcDdIcuDev icuDev : icuDevs) {
            Integer devNo = icuDev.getDevNo();
            putDevValue(icg.getDevEdPremiumMap(), devNo, icuDev.getEdPremium());
            putDevValue(icg.getDevRecvPremiumMap(), devNo, icuDev.getRecvPremium());
            putDevValue(icg.getDevNetFeeMap(), devNo, icuDev.getNetFee());
            putDevValue(icg.getDevIacfMap(), devNo, icuDev.getIacf());
            putDevValue(icg.getDevIaehcInMap(), devNo, icuDev.getIaehcIn());
            putDevValue(icg.getDevIaehcOutMap(), devNo, icuDev.getIaehcOut());
            putDevValue(icg.getDevBadDebtMap(), devNo, icuDev.getBadDebt());
            putDevValue(icg.getDevEdNetFeeMap(), devNo, icuDev.getEdNetFee());
            putDevValue(icg.getDevEdIacfMap(), devNo, icuDev.getEdIacf());
            putDevValue(icg.getDevEdIaehcInMap(), devNo, icuDev.getEdIaehcIn());
            putDevValue(icg.getDevEdIaehcOutMap(), devNo, icuDev.getEdIaehcOut());
        }
    }

    private List<String> createUnitKey(String policyNo, String endorseSeqNo, String kindCode) {
        return Arrays.asList(policyNo, endorseSeqNo, kindCode);
    }

    private boolean isDifferenceOutsideTolerance(BigDecimal val1, BigDecimal val2, BigDecimal tolerance) {

        return nvl(val1).subtract(nvl(val2)).abs().compareTo(tolerance) > 0;
    }

    /**
     * 金额一致性校验
     *
     * @param policyNo 保单号
     * @param endorseSeqNo 批单序号
     * @param kindCode 险别编码
     * @param premium 保费
     * @param preCumlPaidPremium 上期累计实收保费
     * @param recvPremium 未来保费现金流
     * @param edPremiumSum 已赚保费汇总
     * @param preIcu 上期计量单元
     * @param devVos 发展期列表
     * @param preCumlPaidNetFee 上期累计实收净额结算
     * @param prePaidIacf 上期实收跟单获取费用
     * @param iaehcIn 非跟单获取费用(对内)
     * @param iaehcOut 非跟单获取费用(对外)
     */
    private void validateIcuConsistency(String policyNo, String endorseSeqNo, String kindCode,
                                        BigDecimal premium, BigDecimal preCumlPaidPremium, BigDecimal recvPremium,
                                        BigDecimal edPremiumSum, AtrDuctLrcDdIcuPre preIcu, List<AtrBussLrcDdIcuDev> devVos,
                                        BigDecimal preCumlPaidNetFee, BigDecimal prePaidIacf,
                                        BigDecimal iaehcIn, BigDecimal iaehcOut,BigDecimal preCumlEdIaehcIn,BigDecimal preCumlEdIaehcOut) {


        BigDecimal TOLERANCE = new BigDecimal("0.1");
        // --- 校验1：直保临分保费一致性校验 ---
        // 校验 1.1: 上期累计实收 + 未来保费现金流 vs 签单保费
        BigDecimal totalPaidPremium = preCumlPaidPremium.add(recvPremium);
        if (isDifferenceOutsideTolerance(totalPaidPremium, premium, TOLERANCE)) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的签单保费为:%s,累计实收和未来保费现金流汇总为:%s 不一致（超出容差%s）"
                    , policyNo, endorseSeqNo, kindCode, premium, totalPaidPremium, TOLERANCE));
        }

        // 校验 1.2: 累计已赚保费 vs 签单保费
        BigDecimal totalEarnedPremium = nvl(preIcu.getPreCumlEdPremium()).add(nvl(preIcu.getCurEdPremium())).add(edPremiumSum);
        if (isDifferenceOutsideTolerance(totalEarnedPremium, premium, TOLERANCE)) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的签单保费为:%s,累计已赚保费为:%s 不一致（超出容差%s）"
                    , policyNo, endorseSeqNo, kindCode, premium, totalEarnedPremium, TOLERANCE));
        }

        // --- 校验2：直保临分净额结算一致性校验 ---
        // 累计实付净额结算 + 未来净额结算现金流 vs 累计已赚净额结算
        BigDecimal netFeeSum = devVos.stream().map(dev -> nvl(dev.getNetFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal edNetFeeSum = devVos.stream().map(dev -> nvl(dev.getEdNetFee())).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalPaidNetFee = nvl(preCumlPaidNetFee).add(netFeeSum);
        BigDecimal totalEarnedNetFee = nvl(preIcu.getPreCumlEdNetFee()).add(nvl(preIcu.getCurEdNetFee())).add(edNetFeeSum);

        if (isDifferenceOutsideTolerance(totalPaidNetFee, totalEarnedNetFee, TOLERANCE)) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的净额结算不一致（超出容差%s），累计实付净额结算+未来净额结算为:%s,累计已赚净额结算为:%s"
                    , policyNo, endorseSeqNo, kindCode, TOLERANCE, totalPaidNetFee, totalEarnedNetFee));
        }

        // --- 校验3：直保临分跟单获取费用一致性校验 ---
        // 累计实付跟单获取费用 + 未来跟单获取费用现金流 vs 累计已赚跟单获取费用现金流
        BigDecimal iacfSum = devVos.stream().map(dev -> nvl(dev.getIacf())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal edIacfSum = devVos.stream().map(dev -> nvl(dev.getEdIacf())).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalPaidIacf = prePaidIacf.add(iacfSum);
        BigDecimal totalEarnedIacf = nvl(preIcu.getPreCumlEdIacf()).add(nvl(preIcu.getCurEdIacf())).add(edIacfSum);

        if (isDifferenceOutsideTolerance(totalPaidIacf, totalEarnedIacf, TOLERANCE)) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的跟单获取费用不一致（超出容差%s），累计实付跟单获取费用+未来跟单获取费用为:%s,累计已赚跟单获取费用为:%s"
                    , policyNo, endorseSeqNo, kindCode, TOLERANCE, totalPaidIacf, totalEarnedIacf));
        }

        // --- 校验4：直保临分非跟单获取费用一致性校验 ---
        // 累计实付非跟单获取费用 vs 累计已赚非跟单获取费用现金流
        BigDecimal edIaehcInSum = devVos.stream().map(dev -> nvl(dev.getEdIaehcIn())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalEarnedIaehcIn = edIaehcInSum.add(preCumlEdIaehcIn);

        if (isDifferenceOutsideTolerance(iaehcIn, totalEarnedIaehcIn, TOLERANCE)) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的非跟单获取费用(对内)不一致（超出容差%s），累计实付非跟单获取费用为:%s,累计已赚非跟单获取费用为:%s"
                    , policyNo, endorseSeqNo, kindCode, TOLERANCE, iaehcIn, totalEarnedIaehcIn));
        }

        BigDecimal edIaehcOutSum = devVos.stream().map(dev -> nvl(dev.getEdIaehcOut())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalEarnedIaehcOut = edIaehcOutSum.add(preCumlEdIaehcOut);

        if (isDifferenceOutsideTolerance(iaehcOut, totalEarnedIaehcOut, TOLERANCE)) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的非跟单获取费用(对外)不一致（超出容差%s），累计实付非跟单获取费用为:%s,累计已赚非跟单获取费用为:%s"
                    , policyNo, endorseSeqNo, kindCode, TOLERANCE, iaehcOut, totalEarnedIaehcOut));
        }
    }

    /**
     * 处理各种金额的尾差
     * 
     * @param devVos 发展期列表
     * @param premium 保费
     * @param netFee 净额结算手续费
     * @param iacf 跟单获取费用
     * @param iaehcIn 非跟单获取费用-对内
     * @param iaehcOut 非跟单获取费用-对外
     * @param preCumlEdPremium 上期累计已赚保费
     * @param preCumlEdNetFee 上期累计已赚净额结算手续费
     * @param preCumlEdIacf 上期累计已赚跟单获取费用
     */
    private void handleAmountTailDifference(List<AtrBussLrcDdIcuDev> devVos, 
                                          BigDecimal premium, BigDecimal netFee, BigDecimal iacf, 
                                          BigDecimal iaehcIn, BigDecimal iaehcOut,
                                          BigDecimal preCumlEdPremium, BigDecimal preCumlEdNetFee,
                                          BigDecimal preCumlEdIacf,BigDecimal preCumlEdIaehcIn,BigDecimal preCumlEdIaehcOut) {
        // 已赚保费尾差处理
        AtrBussLrcDdIcuDev targetDevVo = null;
        BigDecimal maxEdPremium = BigDecimal.ZERO;

        for (AtrBussLrcDdIcuDev dev : devVos) {
            if (dev.getEdPremium() != null && dev.getEdPremium().compareTo(BigDecimal.ZERO) != 0) {
                if (targetDevVo == null || dev.getEdPremium().compareTo(maxEdPremium) > 0) {
                    targetDevVo = dev;
                    maxEdPremium = dev.getEdPremium();
                }
            }
        }
        BigDecimal edPremiumSum = devVos.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetDevVo != null && premium != null) {
            BigDecimal gap = premium.subtract(preCumlEdPremium).subtract(edPremiumSum);
            targetDevVo.setEdPremium(round(targetDevVo.getEdPremium().add(gap)));
        }
        
        // 净额结算手续费尾差处理
        AtrBussLrcDdIcuDev targetNetFeeDevVo = null;
        BigDecimal maxEdNetFee = BigDecimal.ZERO;
        for (AtrBussLrcDdIcuDev dev : devVos) {
            if (dev.getEdNetFee() != null && dev.getEdNetFee().compareTo(BigDecimal.ZERO) != 0) {
                if (targetNetFeeDevVo == null || dev.getEdNetFee().compareTo(maxEdNetFee) > 0) {
                    targetNetFeeDevVo = dev;
                    maxEdNetFee = dev.getEdNetFee();
                }
            }
        }
        BigDecimal edNetFeeSum = devVos.stream()
                .map(dev -> nvl(dev.getEdNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetNetFeeDevVo != null && netFee != null) {
            BigDecimal netFeeGap = netFee.subtract(preCumlEdNetFee).subtract(edNetFeeSum);
            targetNetFeeDevVo.setEdNetFee(round(targetNetFeeDevVo.getEdNetFee().add(netFeeGap)));
        }
        
        // 跟单获取费用尾差处理
        AtrBussLrcDdIcuDev targetIacfDevVo = null;
        BigDecimal maxEdIacf = BigDecimal.ZERO;
        for (AtrBussLrcDdIcuDev dev : devVos) {
            if (dev.getEdIacf() != null && dev.getEdIacf().compareTo(BigDecimal.ZERO) != 0) {
                if (targetIacfDevVo == null || dev.getEdIacf().compareTo(maxEdIacf) > 0) {
                    targetIacfDevVo = dev;
                    maxEdIacf = dev.getEdIacf();
                }
            }
        }
        BigDecimal edIacfSum = devVos.stream()
                .map(dev -> nvl(dev.getEdIacf()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetIacfDevVo != null && iacf != null) {
            BigDecimal iacfGap = iacf.subtract(preCumlEdIacf).subtract(edIacfSum);
            targetIacfDevVo.setEdIacf(round(targetIacfDevVo.getEdIacf().add(iacfGap)));
        }

        // 非跟单获取费用-对内尾差处理
        AtrBussLrcDdIcuDev targetIaehcInDevVo = null;
        BigDecimal maxEdIaehcIn = BigDecimal.ZERO;
        for (AtrBussLrcDdIcuDev dev : devVos) {
            if (dev.getEdIaehcIn() != null && dev.getEdIaehcIn().compareTo(BigDecimal.ZERO) != 0) {
                if (targetIaehcInDevVo == null || dev.getEdIaehcIn().compareTo(maxEdIaehcIn) > 0) {
                    targetIaehcInDevVo = dev;
                    maxEdIaehcIn = dev.getEdIaehcIn();
                }
            }
        }
        BigDecimal edIaehcInSum = devVos.stream()
                .map(dev -> nvl(dev.getEdIaehcIn()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetIaehcInDevVo != null && iaehcIn != null) {
            BigDecimal iaehcInGap = iaehcIn.subtract(preCumlEdIaehcIn).subtract(edIaehcInSum);
            targetIaehcInDevVo.setEdIaehcIn(round(targetIaehcInDevVo.getEdIaehcIn().add(iaehcInGap)));
        }
        
        // 非跟单获取费用-对外尾差处理
        AtrBussLrcDdIcuDev targetIaehcOutDevVo = null;
        BigDecimal maxEdIaehcOut = BigDecimal.ZERO;
        for (AtrBussLrcDdIcuDev dev : devVos) {
            if (dev.getEdIaehcOut() != null && dev.getEdIaehcOut().compareTo(BigDecimal.ZERO) != 0) {
                if (targetIaehcOutDevVo == null || dev.getEdIaehcOut().compareTo(maxEdIaehcOut) > 0) {
                    targetIaehcOutDevVo = dev;
                    maxEdIaehcOut = dev.getEdIaehcOut();
                }
            }
        }
        BigDecimal edIaehcOutSum = devVos.stream()
                .map(dev -> nvl(dev.getEdIaehcOut()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetIaehcOutDevVo != null && iaehcOut != null) {
            BigDecimal iaehcOutGap = iaehcOut.subtract(preCumlEdIaehcOut).subtract(edIaehcOutSum);
            targetIaehcOutDevVo.setEdIaehcOut(round(targetIaehcOutDevVo.getEdIaehcOut().add(iaehcOutGap)));
        }
    }

    /**
     * 计算过渡期已赚保费
     * 
     * @param yearMonth 评估期（格式：yyyyMM）
     * @param entityId 机构ID
     */
    public void calculateEdPremium(String yearMonth, Long entityId) {

        // 初始化环境参数
        String actionNo = EcfUtil.createActionNo();
        initEnvParams(actionNo, entityId, yearMonth, "DD");
        
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            // 初始化批处理器
            abp.addType(AtrBussTransitionEdPremium.class);
            
            // 设置分区数量
            final int parts = 100; // 分区数量
            
            // 获取评估期开始和结束日期
            DateTime evDateBom = new DateTime(Dates.toDate(yearMonth + "01"));
            DateTime evDateEom = evDateBom.dayOfMonth().withMaximumValue();
            Date evStartDate = evDateBom.toDate();
            Date evEndDate = evDateEom.toDate();
            int daysInMonth = evDateEom.getDayOfMonth(); // 当月天数

            atrBussLrcDdDao.truncateTransitionTempTable();
            
            // 构建插入临时表所需参数
            Map<String, Object> tempDataParams = new HashMap<>(commonParamMap);
            tempDataParams.put("parts", parts);
            tempDataParams.put("evStartDate", evStartDate);

            atrBussLrcDdDao.insertTransitionTempData(tempDataParams);

            for (int partNo = 0; partNo < parts; partNo++) {
                Map<String, Object> partParams = new HashMap<>(commonParamMap);
                partParams.put("pn", partNo);
                
                // 获取当前分区的保单数据
                List<AtrBussLrcDdIcu> baseVos = atrBussLrcDdDao.getTransitionPartBaseVos(partParams);
                if (baseVos.isEmpty()) {
                    continue; // 跳过空分区
                }

                
                // 创建线程安全的结果集合
                final List<AtrBussTransitionEdPremium> resultList = Collections.synchronizedList(new ArrayList<>());
                
                // 使用并发计算
                ThreadUtil.runThreadsThrow(baseVos, vo -> {
                    // 计算已赚保费
                    AtrBussTransitionEdPremium edPremium = calculateSingleEdPremium(
                        vo, entityId, yearMonth, evStartDate, evEndDate, daysInMonth);
                    
                    // 添加到线程安全的结果列表
                    if (edPremium != null) {
                        resultList.add(edPremium);
                    }
                }, () -> EcfUtil.THREADS_CALC); // 使用EcfUtil中定义的计算线程数
                
                // 批量插入当前分区的已赚保费结果
                if (!resultList.isEmpty()) {
                    // 使用AsyncBatchProcessor插入数据
                    resultList.forEach(abp::insert);
                }
            }
            abp.end();
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("计算DD业务过渡期已赚保费异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 计算单个保单的已赚保费
     * 
     * @param vo 保单数据
     * @param entityId 实体ID
     * @param yearMonth 评估期
     * @param evStartDate 评估期开始日期
     * @param evEndDate 评估期结束日期
     * @param daysInMonth 当月天数
     * @return 已赚保费对象
     */
    private AtrBussTransitionEdPremium calculateSingleEdPremium(
            AtrBussLrcDdIcu vo, Long entityId, String yearMonth, 
            Date evStartDate, Date evEndDate, int daysInMonth) {
        
        // 获取基本信息
        String policyNo = vo.getPolicyNo();
        String endorseSeqNo = vo.getEndorseSeqNo();
        String kindCode = vo.getKindCode();
        BigDecimal premium = nvl(vo.getPremium());
        Date effectiveDate = vo.getEffectiveDate();  // 起保日期
        Date expiryDate = vo.getExpiryDate();        // 终保日期
        
        // 承保总天数
        int totalDays = Dates.daysBetween(effectiveDate, expiryDate) + 1;
        if (totalDays <= 0) {
            return null; // 无效的保单日期，跳过
        }
        
        // 计算当期已赚天数
        double earnedDays;
        
        // 情况1: 保险期间完全在评估期内（起保日期和终保日期都在评估期内）
        if (!effectiveDate.before(evStartDate) && !effectiveDate.after(evEndDate) &&
            !expiryDate.before(evStartDate) && !expiryDate.after(evEndDate)) {
            // 计算公式：保险期间总天数 / 保险期间总天数 * premium = premium
            earnedDays = totalDays;
        } 
        // 情况2: 起保日期在当前评估期内，终保日期大于当前评估期
        else if (!effectiveDate.before(evStartDate) && !effectiveDate.after(evEndDate) &&
                 expiryDate.after(evEndDate)) {
            // 计算公式：（当前评估期的最后一天-起保日期）的天数 / (终保日期-起保日期+1) * premium
            earnedDays = Dates.daysBetween(effectiveDate, evEndDate) + 1;
        } 
        // 情况3: 起保日期小于当前评估期，终保日期在当前评估期内
        else if (effectiveDate.before(evStartDate) &&
                !expiryDate.before(evStartDate) && !expiryDate.after(evEndDate)) {
            // 计算公式：（终保日期-当前评估期的第一天+1）/ (终保日期-起保日期+1) * premium
            earnedDays = Dates.daysBetween(evStartDate, expiryDate) + 1;
        } 
        // 情况4: 起保日期小于当前评估期且终保日期大于当前评估期（跨期保单）
        else if (effectiveDate.before(evStartDate) && expiryDate.after(evEndDate)) {
            // 计算公式：（当前评估期的最后一天-当前评估期的第一天+1）/ (终保日期-起保日期+1) * premium
            earnedDays = daysInMonth;
        } 
        // 情况5: 保险期间完全在评估期外
        else {
            // 没有已赚部分
            return null;
        }
        
        // 计算已赚保费比例
        BigDecimal edRate = earnedDays <= 0 ? BigDecimal.ZERO : 
                BigDecimal.valueOf(earnedDays).divide(BigDecimal.valueOf(totalDays), 15, RoundingMode.HALF_UP);
        
        // 计算已赚保费
        BigDecimal edPremium = round(premium.multiply(edRate));
        
        // 创建已赚保费对象
        AtrBussTransitionEdPremium result = new AtrBussTransitionEdPremium();
        result.setEntityId(entityId);
        result.setPolicyNo(policyNo);
        result.setEndorseSeqNo(endorseSeqNo);
        result.setKindCode(kindCode);
        result.setYearMonth(yearMonth);
        result.setEdPremium(edPremium);
        
        return result;
    }

    /**
     * 备份并清理历史保单数据
     * 
     * <p>清理条件：
     * <ul>
     * <li>issue_date在2023年之前</li>
     * <li>保单终保日期小于2024.12.31</li>
     * <li>2024.12.31后没有缴费计划</li>
     * </ul>
     * 
     * <p>只有当保单的所有批单都满足上述条件时，才会清理该保单的数据。
     * 如果保单下有任何一个批单不满足条件，则该保单的所有数据都会保留。
     * 
     * <p>会同时备份以下表：
     * <ul>
     * <li>atr_dap_dd_unit → atr_dap_dd_unit_bak</li>
     * <li>atr_dap_dd_payment_plan → atr_dap_dd_payment_plan_bak</li>
     * <li>atr_dap_dd_paid → atr_dap_dd_paid_bak</li>
     * <li>atr_dap_dd_exp_alloc → atr_dap_dd_exp_alloc_bak</li>
     * </ul>
     *
     * @param entityId 实体ID
     */
    public void backupAndCleanHistoricalData(Long entityId) {

        try {
            // 定义截止日期
            final String cutoffDate = "20241231";

            // 第一步：创建或清空临时表存储需要处理的保单号和批单号
            createTempPoliciesTable();
            atrBussLrcDdDao.truncateTempPoliciesTable();
            
            // 第二步：找出符合条件的保单号和批单号
            Map<String, Object> params = new HashMap<>();
            params.put("entityId", entityId);
            params.put("cutoffDate", cutoffDate);
            
            // 插入符合条件的保单到临时表
            atrBussLrcDdDao.identifyPoliciesToClean(params);
            
            // 第三步：并发执行备份和删除操作

            // 定义需要处理的表任务
            List<String> tableTasks = Arrays.asList(
                "Unit", 
                "PaymentPlan", 
                "Paid", 
                "ExpAlloc"
            );
            
            // 并发执行每个表的备份和删除操作
            ThreadUtil.runThreadsThrow(tableTasks, (tableType) -> {
                switch (tableType) {
                    case "Unit":
                        atrBussLrcDdDao.createUnitBackupTable();
                        atrBussLrcDdDao.deleteUnitData();
                        break;
                    case "PaymentPlan":
                        atrBussLrcDdDao.createPaymentPlanBackupTable();
                        atrBussLrcDdDao.deletePaymentPlanData();
                        break;
                    case "Paid":
                        atrBussLrcDdDao.createPaidBackupTable();
                        atrBussLrcDdDao.deletePaidData();
                        break;
                    case "ExpAlloc":
                        atrBussLrcDdDao.createExpAllocBackupTable();
                        atrBussLrcDdDao.deleteExpAllocData();
                        break;
                    default:
                        break;
                }
            }, tableTasks::size);
            
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("备份和清理历史保单数据异常: " + e.getMessage(), e);
        }
    }

    /**
     * 创建临时表存储需要删除的保单号
     */
    private void createTempPoliciesTable() {
        try {
            jdbcTemplate.execute("DROP TABLE IF EXISTS atr_temp_policies_to_clean");
            jdbcTemplate.execute("CREATE TABLE atr_temp_policies_to_clean (policy_no VARCHAR(50) PRIMARY KEY)");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("创建临时表异常: " + e.getMessage(), e);
        }
    }

    /**
     * 判断过渡期已赚保费表中是否存在指定机构的数据
     * 
     * @param entityId 机构ID
     * @return 存在返回true，不存在返回false
     */
    public boolean hasTransitionData(Long entityId) {
        return atrBussLrcDdDao.hasTransitionData(entityId);
    }
}
