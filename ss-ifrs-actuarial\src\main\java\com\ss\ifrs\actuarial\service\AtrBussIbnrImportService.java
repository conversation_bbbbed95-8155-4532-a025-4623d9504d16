package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface AtrBussIbnrImportService {

    Page<AtrBussIbnrImportMainVo> searchLicPage(AtrBussIbnrImportMainVo ibnrImportMainVo, Pageable pageParam);

    Page<AtrBussClaimImportMainVo> searchXoClaimPage(AtrBussClaimImportMainVo ibnrImportMainVo, Pageable pageParam);

    String licExcelImport(MultipartFile file, AtrBussIbnrImportMainVo ibnrImportMainVo, Long userId) throws Exception ;

    Boolean licDataConfirm(AtrBussIbnrImportMainVo ibnrImportMainVo, Long userId);

    List<AtrBussIbnrImportMainVo> findIbnrList(AtrBussIbnrImportMainVo ibnrImportMainVo);

    AtrBussIbnrImportMainVo findById(Long becfMainId);

    AtrBussIbnrImportMainVo deleteIbnrByBecfId(Long becfMainId);

    /**
     * @description: IBNR数据导入
     * @param : [file, ibnrImportMainVo, userId]
     * @Date 2025/4/8
     * */
    void ibnrExcelImport(MultipartFile file, AtrBussIbnrImportMainVo ibnrImportMainVo, Long userId) throws Exception ;

    AtrBussIbnrImportMainVo findByVo(AtrBussIbnrImportMainVo ibnrImportMainVo);


    void claimExcelImport(MultipartFile file, AtrBussIbnrImportMainVo ibnrImportMainVo, Long userId) throws Exception ;

    /**
     * @description: 查询有无可计算的IBNR导入数据
     * @param : [ibnrImportMainVo]
     * @Date 2025/4/8
    * */
    Boolean hasIbnrData(AtrBussIbnrImportMainVo ibnrImportMainVo);
}
