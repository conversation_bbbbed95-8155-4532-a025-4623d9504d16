package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.com.vo.CompanyVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.ss.platform.core.conf.FeignAuthConfig;

/**
 * 调用BPL部门信息的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-AUTHZTH", configuration = {FeignAuthConfig.class})
public interface BplCompanyFeignClient {
    /**
     * "根据部门id查找部门信息"
     *
     * @param companyId 部门编码
     * @return 部门信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/saa/company/find_by_id/{companyId}")
    CompanyVo findByEntityId(@PathVariable("companyId") Long companyId);

    /**
     * "根据部门编码查找部门信息"
     *
     * @param entityCode 部门编码
     * @return 部门信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/saa/company/find_by_code/{entityCode}")
    CompanyVo findByEntityCode(@PathVariable("entityCode") String entityCode);
}
