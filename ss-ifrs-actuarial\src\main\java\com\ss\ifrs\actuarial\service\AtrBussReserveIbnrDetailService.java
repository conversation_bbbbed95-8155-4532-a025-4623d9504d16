package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrExcelVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import org.springframework.transaction.UnexpectedRollbackException;

import java.util.List;

public interface AtrBussReserveIbnrDetailService {

    List<AtrBussReserveIbnrDetailVo> findCumulative(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo);

    List<AtrBussReserveIbnrDetailVo> findReparations(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo);

    List<AtrBussReserveIbnrDetailVo> findBFLossRate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo);

    List<AtrBussReserveIbnrDetailVo> findBFCalculate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo);

    //链梯法第三步
    List<AtrBussReserveIbnrDetailVo> showLtIbnrData(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo);


    /**
     * @Method findListByIbnrId
     * <AUTHOR>
     * @Date 2022/8/22
     * @Description 根据reserveIbnrId查找明细数据列表
     * @param reserveIbnrId 主表主键ID
     * @Return IBNR明细数据列表
     */
    List<AtrBussReserveIbnrExcelVo> findListByIbnrId(Long reserveIbnrId);

    public void updateValueAndCDFCalculate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) throws UnexpectedRollbackException;

    public void updateValueAndOSCalculate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) throws UnexpectedRollbackException;

    List<AtrExcelCellVo> generateIbnrCells(List<AtrExcelCellVo> cells, Object object, String language);
}
