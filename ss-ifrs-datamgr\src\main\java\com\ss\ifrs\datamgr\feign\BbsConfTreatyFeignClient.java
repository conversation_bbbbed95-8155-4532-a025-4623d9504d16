package com.ss.ifrs.datamgr.feign;


import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfTreatyVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 调用BPL合约的接口
 *
 * <AUTHOR>
 * @Date 2022/4/7
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsConfTreatyFeignClient {

    @ApiOperation(value = "根据合约号查询合约")
    @RequestMapping(value = "/conf_treaty/find_by_code/{referenceNo}", method = RequestMethod.GET)
    BbsConfTreatyVo findByTreatyNo(@RequestParam(value = "referenceNo") String referenceNo) ;

    @ApiOperation(value = "合约号查询")
    @RequestMapping(value = "/conf_treaty/find_list", method = RequestMethod.POST)
    List<BbsConfTreatyVo> findList(@RequestBody BbsConfTreatyVo vo) ;

    @ApiOperation(value = "验证treatyNo是否存在")
    @RequestMapping(value = "/conf_treaty/validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    BaseResponse<Map<String, String>> validateRoleCode(@RequestBody BbsConfTreatyVo vo);
}
