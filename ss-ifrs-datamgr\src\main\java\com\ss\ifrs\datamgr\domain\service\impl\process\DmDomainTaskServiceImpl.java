package com.ss.ifrs.datamgr.domain.service.impl.process;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.DmBussDataCheckDao;
import com.ss.ifrs.datamgr.domain.service.process.DmDomainTaskService;
import com.ss.ifrs.datamgr.feign.BmsActProcFeignClient;
import com.ss.ifrs.datamgr.feign.BmsTrackBussActionFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.BussActionStateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DmDomainTaskServiceImpl implements DmDomainTaskService {

    @Autowired
    private BmsTrackBussActionFeignClient bmsTrackBussActionFeignClient;

    @Autowired
    private DmBussDataCheckDao atrBussDataCheckDao;

    @Autowired
    private BmsActProcFeignClient bmsActProcFeignClient;

    @Override
    @Async("ruleThreadPool")
    public void mergeDmProcessState(BussActionStateVo bussActionStateVo) {
        try {
            bmsTrackBussActionFeignClient.synchronizeProcessNodeStatus(bussActionStateVo);
        } catch (Exception e) {
            log.error("Merge Expense Process State :{}", e.getMessage());
        }

    }

    @Override
    @Async("ruleThreadPool")
    public void refreshBussActionState(BussActionStateVo bussActionStateVo) {
        bmsTrackBussActionFeignClient.refreshBussActionState(bussActionStateVo);
    }

    @Override
    public void configCheck(DmConfBussPeriodVo dmConfBussPeriodVo) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("taskCode", dmConfBussPeriodVo.getTaskCode());
            param.put("entityId", dmConfBussPeriodVo.getEntityId());
            param.put("bookCode", null);
            param.put("yearMonth", dmConfBussPeriodVo.getYearMonth());
            param.put("procId", dmConfBussPeriodVo.getProcId());
            param.put("userId", dmConfBussPeriodVo.getCreatorId());

            // 调用执行
            atrBussDataCheckDao.checkRule(param);
        } catch (UnexpectedRollbackException e) {
            throw e;
        }
    }

    @Override
    public ActOverviewVo getActOverviewVoByObject(ActOverviewVo actOverviewVo) {
        ActOverviewVo result = new ActOverviewVo();
        try {
            BaseResponse<Object> actOverviewByObject = bmsActProcFeignClient.findActOverviewByObject(actOverviewVo);
            if (ObjectUtils.isNotEmpty(actOverviewByObject) && actOverviewByObject.getResCode().equals(ResCodeConstant.ResCode.SUCCESS) && ObjectUtils.isNotEmpty(actOverviewByObject.getResData())) {
                result = JSONObject.parseObject(JSONObject.toJSONString(actOverviewByObject.getResData()),
                        ActOverviewVo.class);
            }
        } catch (Exception e) {
            log.error("Description Failed to obtain flow node information ：{}", e.getMessage());
        }
        return result;
    }


}
