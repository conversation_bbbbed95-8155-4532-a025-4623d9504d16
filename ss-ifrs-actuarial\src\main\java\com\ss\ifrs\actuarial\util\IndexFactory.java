package com.ss.ifrs.actuarial.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class IndexFactory<T> {

    private final Map<List<?>, List<T>> map = new HashMap<>();

    @SuppressWarnings("unchecked")
    public void add(List<?> key, T value) {
        key = alterKey((List<Object>) key);
        if (!map.containsKey(key)) {
            map.put(key, new ArrayList<>());
        }
        map.get(key).add(value);
    }

    @SuppressWarnings("unchecked")
    public void plus(List<?> key, Double value) {
        if (value == null) {
            return;
        }
        key = alterKey((List<Object>) key);
        if (!map.containsKey(key)) {
            map.put(key, new ArrayList<>());
        }
        List<T> list = map.get(key);
        if (list.isEmpty()) {
            list.add((T) value);
        } else {
            double oldValue = 0D;
            if (list.get(0) != null) {
                if (list.get(0) instanceof Number) {
                    oldValue = ((Number) list.get(0)).doubleValue();
                } else {
                    throw new ClassCastException("Cannot cast " + list.get(0).getClass() + " to double");
                }
            }
            Object v = oldValue + value;
            list.set(0, (T) v);
        }
    }

    @SuppressWarnings("unchecked")
    public void plus(List<?> key, BigDecimal value) {
        if (value == null) {
            return;
        }
        key = alterKey((List<Object>) key);
        if (!map.containsKey(key)) {
            map.put(key, new ArrayList<>());
        }
        List<T> list = map.get(key);
        if (list.isEmpty()) {
            list.add((T) value);
        } else {
            BigDecimal oldValue = BigDecimal.ZERO;
            if (list.get(0) != null) {
                if (list.get(0) instanceof BigDecimal) {
                    oldValue = (BigDecimal) list.get(0);
                } else {
                    throw new ClassCastException("Cannot cast " + list.get(0).getClass() + " to BigDecimal");
                }
            }
            Object v = oldValue.add(value);
            list.set(0, (T) v);
        }
    }

    @SuppressWarnings("unchecked")
    public List<T> list(List<?> key) {
        key = alterKey((List<Object>) key);
        if (!map.containsKey(key)) {
            return Collections.emptyList();
        }
        return map.get(key);
    }

    public T one(List<?> key) {
        return one(key, null);
    }

    public T one(List<?> key, T defaultValue) {
        List<T> list = list(key);
        T value = list.isEmpty() ? null : list.get(0);
        return value == null ? defaultValue : value;
    }

    @SuppressWarnings("unchecked")
    public boolean exists(List<?> key) {
        key = alterKey((List<Object>) key);
        return map.containsKey(key);
    }

    public void clear() {
        map.clear();
    }

    /**
     * 获取内部存储的所有数据映射
     * @return 数据映射
     */
    public Map<List<?>, List<T>> getMap() {
        return map;
    }
    
    /**
     * 获取所有键的集合
     * @return 键的集合
     */
    public Set<List<?>> keys() {
        return map.keySet();
    }

    /**
     * 调整 key，   <br>
     * 主要是 number 类型匹配时， equals 方法会检查类型 ， 类似 Long、 Integer匹配不了， 但实际只要求数值相等即可;
     * 为此， 所有数字全部调整为 double
     */
    private List<?> alterKey(List<Object> key) {
        if (key == null || key.isEmpty()) {
            return key;
        }

        boolean unSet = false;

        for (int i = 0; i < key.size(); i++) {
            Object obj = key.get(i);
            if (obj instanceof Number && !(obj instanceof BigDecimal)) {
                try {
                    key.set(i, ((Number) obj).doubleValue());
                } catch (UnsupportedOperationException e) {
                    unSet = true;
                    break;
                }
            }
        }

        // 不支持 set 方法的 List, 换个新的
        if (unSet) {
            List<Object> key2 = new ArrayList<>();
            for (Object obj : key) {
                if (obj instanceof Number && !(obj instanceof BigDecimal)) {
                    obj = ((Number) obj).doubleValue();
                }
                key2.add(obj);
            }
            key = key2;
        }

        return key;
    }

}
