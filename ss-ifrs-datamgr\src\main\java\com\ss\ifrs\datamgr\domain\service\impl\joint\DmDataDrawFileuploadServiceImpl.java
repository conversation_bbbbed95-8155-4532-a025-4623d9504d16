package com.ss.ifrs.datamgr.domain.service.impl.joint;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.conf.AppFilePathConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfDrawTypeDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableColumnDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainDataDraw;
import com.ss.ifrs.datamgr.domain.service.joint.DmDataDrawFileuploadService;
import com.ss.ifrs.datamgr.domain.service.joint.DmDomainDataPushSignalService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfControl;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfDrawType;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableColumnVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmTaskCodeAdapterVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDrawColumnParseVo;
import com.ss.ifrs.datamgr.pojo.log.vo.DmDuctDrawLogVo;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodService;
import com.ss.ifrs.datamgr.service.conf.DmConfControlService;
import com.ss.ifrs.datamgr.service.conf.DmConfTableService;
import com.ss.ifrs.datamgr.service.log.DmDuctDrawLogService;
import com.ss.ifrs.datamgr.util.ExcelImportUtils;
import com.ss.ifrs.datamgr.util.FilePathUtils;
import com.ss.library.utils.DateUtil;
import com.ss.library.utils.FileUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.util.ClassUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DmDataDrawFileuploadServiceImpl extends DmDomainDataDraw implements DmDataDrawFileuploadService {

    @Autowired
    AppFilePathConfig appFilePathConfig;
    @Autowired
    DmConfTableService dmConfTableService;

    @Autowired
    DmConfBussPeriodService dmConfBussPeriodService;
    @Autowired
    DmDuctDrawLogService dmDuctDrawLogService;

    @Autowired
    DmConfTableColumnDao dmConfTableColumnDao;
    @Autowired
    DmDomainDataPushSignalService dmDomainDataPushSignalService;

    @Autowired
    DmConfControlService dmConfControlService;
    @Autowired
    DmConfDrawTypeDao dmConfDrawTypeDao;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public Map<String, Object> dataDrawAction(MultipartFile file, DmDataDrawVo dmDataDrawVo) throws Exception {

        // 总控开关
        DmConfControl dmConfControl = dmConfControlService.searchConfigControl();
        // 文件上传开关
        DmConfDrawType dmConfDrawType = dmConfDrawTypeDao.findDataByType(DmConstant.DrawType.FILE_UPLOAD);
        if (DmConstant.ControlType.Close.equals(dmConfControl.getOpenedIs())
                || DmConstant.ControlType.Close.equals(dmConfDrawType.getOpenedIs())) {
            return new HashMap<>(0);
        }
        Map<String, Object> dealMap = uploadExcelData(file, dmDataDrawVo);
        return dealMap;
    }

    public Map<String, Object> uploadExcelData(MultipartFile file, DmDataDrawVo dmDataDrawVo) throws Exception {
        Map<String, Object> dealMap = new HashMap<>();
        //校验业务期间是否在准备中、已准备、处理中
        DmConfBussPeriodVo dmConfBussPeriodVo = new DmConfBussPeriodVo();
        dmConfBussPeriodVo.setEntityId(dmDataDrawVo.getEntityId());
        dmConfBussPeriodVo.setYearMonth(dmDataDrawVo.getYearMonth());
        DmConfBussPeriodVo dealYearMonth = dmConfBussPeriodService.findDealYearMonth(dmConfBussPeriodVo);
        if (dealYearMonth == null) {
            DmDuctDrawLogVo dmDuctDrawLogVo = ClassUtil.convert(dmDataDrawVo, DmDuctDrawLogVo.class);
            dmDuctDrawLogVo.setLogMsg("dmBusinessPeriodDeal");
            dmDuctDrawLogService.addExtractLog(dmDuctDrawLogVo);
            return dealMap;
        }

        String format = DateUtil.nowStr("yyyy/MM/dd/");
        String basePath = FilePathUtils.getBasePath(appFilePathConfig);
        String excelImportPath = FilePathUtils.getDataFilePathExcelImportPath(appFilePathConfig);

        String dataUploadPath = FilePathUtils.getBussPathDataUpload(appFilePathConfig) + "/" + format + dmDataDrawVo.getBizTypeId() + "/";//增加模型id作为区分
        Long entityId = dmDataDrawVo.getEntityId();
        DmTaskCodeAdapterVo dmTaskCodeAdapterVo = new DmTaskCodeAdapterVo();
        dmTaskCodeAdapterVo.setEntityId(entityId);
        dmTaskCodeAdapterVo.setDrawType(dmDataDrawVo.getDrawType());
        dmTaskCodeAdapterVo.setYearMonth(dmDataDrawVo.getYearMonth());
        String taskCode = this.getTaskCode(dmTaskCodeAdapterVo);

        String saveFileName = taskCode + dmDataDrawVo.getFileName().substring(dmDataDrawVo.getFileName().lastIndexOf("."));
        String storageFilePath = basePath + excelImportPath + dataUploadPath;
        dmDataDrawVo.setFilePath(dataUploadPath + saveFileName);
        DmConfTableVo dmConfTableVo = dmConfTableService.findById(dmDataDrawVo.getBizTypeId());
        //保存任务号跟数量
        dmDataDrawVo.setTaskCode(taskCode);
        dmDataDrawVo.setBizCode(dmConfTableVo.getBizCode());
        dmDataDrawVo.setRowCount(0L);

        //添加文件上传处理中记录
        DmDuctDrawLogVo dmDuctDrawLogVo = ClassUtil.convert(dmDataDrawVo, DmDuctDrawLogVo.class);
        dmDuctDrawLogVo.setLogState("0");
        dmDuctDrawLogService.addExtractLog(dmDuctDrawLogVo);



        new Thread(()->{
            asyncUploadData(file,dmConfTableVo,storageFilePath,saveFileName,taskCode,dmDataDrawVo);
        }).start();

        dealMap.put("result","1");
        return dealMap;
    }

    public void asyncUploadData(MultipartFile file, DmConfTableVo dmConfTableVo, String storageFilePath, String saveFileName, String taskCode, DmDataDrawVo dmDataDrawVo) {
        try {
            DmDataDrawFileuploadServiceImpl dmDataDrawFileuploadService = applicationContext.getBean(DmDataDrawFileuploadServiceImpl.class);
            FileUtil.uploadFile(file, storageFilePath, taskCode);

            List<DmConfTableColumnVo> listByColTypeCode = dmConfTableColumnDao.findListByBizTypeId(dmConfTableVo.getBizTypeId());
            List<DmDrawColumnParseVo> parseList = listByColTypeCode.stream().map(
                            columnVo -> new DmDrawColumnParseVo(columnVo.getColCode(), columnVo.getColType(), columnVo.getColLength(), columnVo.getNeedIs()))
                    .collect(Collectors.toList());
            File file2 = new File(storageFilePath, saveFileName);
            FileInputStream fs = new FileInputStream(file2);
            //解析excel
            List<List<DmDrawColumnParseVo>> lists = ExcelImportUtils.parseExcel(fs, parseList);
            if (CollectionUtils.isEmpty(lists)) {
                log.error("File Upload Fail : Cannot upload an empty data template!");
                DmDuctDrawLogVo dmDuctDrawLogVo = ClassUtil.convert(dmDataDrawVo, DmDuctDrawLogVo.class);
                dmDuctDrawLogVo.setLogState("2");
                dmDuctDrawLogVo.setLogMsg("Cannot upload an empty data template!");
                dmDuctDrawLogService.updateExtractLog(dmDuctDrawLogVo);
                return;
            }

            //设置实际数据量
            dmDataDrawVo.setRowCount(Long.valueOf(lists.size()));

            //生成默认数据
            for (List<DmDrawColumnParseVo> dmParseExcelVos : lists) {
                dmParseExcelVos.addAll(this.createDefaultValue(taskCode, dmDataDrawVo.getCreatorId(), dmConfTableVo.getSrcSeqName() == null ? null : dmConfTableVo.getSrcSeqName().toLowerCase()));
            }

            this.saveExcelData(lists, DmConstant.DbStruct.ODS_TABLE_PREFIX + dmConfTableVo.getBizCode());


            //修改文件上传记录状态
            DmDuctDrawLogVo dmDuctDrawLogVo = ClassUtil.convert(dmDataDrawVo, DmDuctDrawLogVo.class);
            dmDuctDrawLogVo.setLogState("1");
            dmDuctDrawLogService.updateExtractLog(dmDuctDrawLogVo);

            //插入信号数据
            dmDomainDataPushSignalService.fileUploadSignalSyncDataPushSignal(taskCode, dmDataDrawVo.getYearMonth(), dmConfTableVo.getBizCode());
            //校验数据
            dmDataDrawFileuploadService.verifyData(dmDataDrawVo);

        } catch (Exception ex) {
            log.error("File Upload Fail : ", ex);
            DmDuctDrawLogVo dmDuctDrawLogVo = ClassUtil.convert(dmDataDrawVo, DmDuctDrawLogVo.class);
            dmDuctDrawLogVo.setLogState("2");
            dmDuctDrawLogVo.setLogMsg(ex.getMessage());
            dmDuctDrawLogService.updateExtractLog(dmDuctDrawLogVo);
        }
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_VERIFY_DATA)
    public void verifyData(DmDataDrawVo dmDataDrawVo) {
        DmDataDrawFileuploadServiceImpl dmDataDrawFileuploadService = applicationContext.getBean(DmDataDrawFileuploadServiceImpl.class);
        dmDataDrawFileuploadService.queueDataVerify(dmDataDrawVo);
    }

    public void saveExcelData(List<List<DmDrawColumnParseVo>> lists, String tableName) {
        if (CollectionUtils.isEmpty(lists)) {
            return;
        }
        int spitLen = 100;
        int queueSize = lists.size() <= spitLen ? 1 : (int) Math.ceil(lists.size() / spitLen) + 1;
        List handleList = null;
        for (int i = 0; i < queueSize; i++) {
            if ((i + 1) == queueSize) {
                int startIndex = i * spitLen;
                int endIndex = lists.size();
                handleList = lists.subList(startIndex, endIndex);
            } else {
                int startIndex = i * spitLen;
                int endIndex = (i + 1) * spitLen;
                handleList = lists.subList(startIndex, endIndex);
            }
            //确保 保存的handleList中有数据
            if (CollectionUtils.isNotEmpty(handleList)) {
                this.saveDrawData(handleList, tableName);
            }
        }
    }

    private Map<String, Object> buildMap(Long entityId, String taskCode, Long bizTypeId, Long userId) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtil.isNotEmpty(entityId)) {
            map.put("entityId", entityId);
        }
        if (StringUtil.isNotEmpty(taskCode)) {
            map.put("taskCode", taskCode);
        }
        if (StringUtil.isNotEmpty(bizTypeId)) {
            map.put("bizTypeId", bizTypeId);
        }
        if (userId != null) {
            map.put("userId", userId);
        }
        map.put("drawType", '3');
        return map;
    }
}
