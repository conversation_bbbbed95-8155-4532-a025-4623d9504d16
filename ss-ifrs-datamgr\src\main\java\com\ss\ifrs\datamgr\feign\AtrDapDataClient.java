package com.ss.ifrs.datamgr.feign;

import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


@FeignClient(name = "SS-IFRS-ACTUARIAL", configuration = {FeignAuthConfig.class})
public interface AtrDapDataClient {
    @RequestMapping(value = "/dap_ecf/clearDapData/{table}/{yearMonth}", method = RequestMethod.POST)
    BaseResponse<String> cleanDapData(@PathVariable("table") String table, @PathVariable("yearMonth") String yearMonth);
}
