package com.ss.ifrs.datamgr.domain.service.impl.icg.majorrisk;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.domain.abst.DmMajorTest;
import com.ss.ifrs.datamgr.domain.service.icg.majorrisk.DmDomainMajorRiskService;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractCodeVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRuleVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmunitMajorTestVo;
import com.ss.ifrs.datamgr.service.conf.DmConfContractCodeService;
import com.ss.ifrs.datamgr.service.conf.DmConfContractRiskDefService;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitTreatyInwardService;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitTreatyOutwardService;
import com.ss.ifrs.datamgr.service.ods.OdsReinsBillService;
import com.ss.ifrs.datamgr.service.ods.OdsReinsOutwardService;
import com.ss.ifrs.datamgr.service.ods.OdsReinsTreatyService;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DateUtil;
import com.ss.library.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service(DmConstant.BusinessModel.MODEL_TREATY_OUT + "MajorTest")
public class DmDomainMajorRiskToServiceImpl extends DmMajorTest implements DmDomainMajorRiskService {

    @Autowired
    private DmBussCmunitTreatyOutwardService dmBussCmunitTreatyOutwardService;
    @Autowired
    private DmConfContractCodeService dmConfContractCodeService;
    @Autowired
    private DmConfContractRiskDefService dmConfContractRiskDefService;

    @Autowired
    private OdsReinsTreatyService odsReinsTreatyService;

    @Override
    public String getRuleParsing(Long cmUnitId, String codeCode, String businessModel, String yearMonth) {
        DmCmunitMajorTestVo dmCmunitMajorTestVo = dmBussCmunitTreatyOutwardService.getDmCmunitMajorTestVo(cmUnitId);
        DmConfContractCodeVo codeInfoByCodeCode = dmConfContractCodeService.getCodeInfoByCodeCode(codeCode, businessModel);
        ClassUtil.copyProperties(codeInfoByCodeCode, dmCmunitMajorTestVo);
        if (codeInfoByCodeCode != null) {
            dmCmunitMajorTestVo.setColumnName(codeInfoByCodeCode.getColumnRef());
            dmCmunitMajorTestVo.setMatchType(Integer.parseInt(codeInfoByCodeCode.getMatchType()));
        }
        dmCmunitMajorTestVo.setCmunitId(cmUnitId);
        Date lastDateByYearMonth = DateUtil.getLastDateByYearMonth(yearMonth);
        dmCmunitMajorTestVo.setEndDate(lastDateByYearMonth);
        dmCmunitMajorTestVo.setReinsDirection("O");
        dmCmunitMajorTestVo.setBusinessModel("TO");
        String value = "";
        switch (codeInfoByCodeCode.getRelatedTable().toLowerCase()) {
            case "dm_buss_cmunit_treaty_outward":
                value = dmBussCmunitTreatyOutwardService.getCmunitInfoById(dmCmunitMajorTestVo);
                break;
            case "dm_conf_contract_riskdef":
                value = dmConfContractRiskDefService.getRiskDefInfo(dmCmunitMajorTestVo);
                break;
            case "dm_reins_treaty":
                value = odsReinsTreatyService.getReinsTreatyInfo(dmCmunitMajorTestVo);
            default:
                value = getOtherValue(dmCmunitMajorTestVo);

        }
        return StringUtil.isEmpty(value) ? codeInfoByCodeCode.getDefaultValue() : value;
    }

    @Override
    public String getFactorValueCalc(Long cmUnitId, Long entityId, String bussDefCode, Long ruleId, String ruleType, String businessModel, String yearMonth,String businessSubdivisionType) {
        String majorRisk = "";
        List<DmConfContractRuleVo> ruleInfo = getRuleInfo(entityId, ruleId, bussDefCode, ruleType, businessModel,null);
        if (ruleInfo == null || ruleInfo.size() < 1) {
            return "R:";
        }
        for (int i = 0; i < ruleInfo.size(); i++) {
            DmConfContractRuleVo item = ruleInfo.get(i);
            if (item.getFirValue() == null) {
                item.setFirValue("0");
            } else {
                item.setFirValue(
                        execute(DmConstant.BusinessModel.MODEL_TREATY_OUT + "MajorTest"
                                , item.getFirValue(), cmUnitId, businessModel, yearMonth));
            }
            if (item.getSecValue() == null) {
                item.setSecValue("0");
            } else {
                item.setSecValue(
                        execute(DmConstant.BusinessModel.MODEL_TREATY_OUT + "MajorTest"
                                , item.getSecValue(), cmUnitId, businessModel, yearMonth));
            }
            Long result = getResult(item.getFirValue(), item.getRuleOperator(), item.getSecValue());
            if (result == 1) {
                String trueType = item.getTrueType();
                if ("1".equals(trueType)) {
                    majorRisk = getFactorValueCalc(cmUnitId, entityId, bussDefCode, item.getTrueRuleId(), trueType, businessModel, yearMonth,null);
                } else {
                    majorRisk = execute(DmConstant.BusinessModel.MODEL_TREATY_OUT + "MajorTest"
                            , item.getTrueValue(), cmUnitId, businessModel, yearMonth);
                }
            } else {
                String falseType = item.getFalseType();
                if ("1".equals(falseType)) {
                    majorRisk = getFactorValueCalc(cmUnitId, entityId, bussDefCode, item.getFalseRuleId(), falseType, businessModel, yearMonth,null);
                } else {
                    majorRisk = execute(DmConstant.BusinessModel.MODEL_TREATY_OUT + "MajorTest"
                            , item.getFalseValue(), cmUnitId, businessModel, yearMonth);
                }
            }
        }
        return majorRisk;
    }
}
