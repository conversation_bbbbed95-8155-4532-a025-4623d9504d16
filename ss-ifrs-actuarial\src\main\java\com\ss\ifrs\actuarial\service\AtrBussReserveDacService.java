package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveDacDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveDacVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import java.util.List;

public interface AtrBussReserveDacService {

    void reserveDacExtract(AtrBussReserveDacVo atrBussReserveDacVo, Long userId);

    /**
     * @description: upr主表查询
     * @param : [atrBussReserveDacVo,pageParam]
     * @return: Page<AtrBussReserveDacVo>
     * @author: wuyh.
     * @createTime: 2021/3/10 18:38
     */
    Page<AtrBussReserveDacVo> searchPage(AtrBussReserveDacVo atrBussReserveDacVo, Pageable pageParam);


    Page<AtrBussReserveDacDetailVo> findForDataTables(AtrBussReserveDacVo atrBussReserveDacVo, Pageable pageParam);

    //校验是否有确认的计量版本
    Boolean checkIsConfirm(AtrBussReserveDacVo atrBussReserveDacVo);

    //计量版本确认
    void confirm(AtrBussReserveDacVo atrBussReserveDacVo, Long userId);

    List<AtrBussReserveDacDetailVo> findDownloadList(AtrBussReserveDacVo atrBussReserveDacVo);

    //dap准备金版本删除
    void delete(Long reserveDacId, Long userId);
}
