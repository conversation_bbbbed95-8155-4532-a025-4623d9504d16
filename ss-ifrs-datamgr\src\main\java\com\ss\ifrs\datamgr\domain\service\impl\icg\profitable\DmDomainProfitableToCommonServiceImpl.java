package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_TREATY_OUT + "PROFITABLE_COMMON")
public class DmDomainProfitableToCommonServiceImpl implements DmDomainProfitableCommonService {

    @Override
    public void prepareQuota(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void prepareDevelopQuota(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void prepareStressLossRate(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void prepareInterestRate(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void checkFactor(DmIcgProcVo dmIcgProcVo) {

    }
}
