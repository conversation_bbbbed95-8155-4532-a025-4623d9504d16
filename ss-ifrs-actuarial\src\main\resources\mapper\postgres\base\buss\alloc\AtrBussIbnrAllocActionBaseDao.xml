<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocActionDao">
  <!-- 本文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="confirm_is" property="confirmIs" jdbcType="VARCHAR" />
    <result column="confirm_user" property="confirmUser" jdbcType="BIGINT" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, action_no, entity_id, year_month, business_source_code, status, confirm_is, confirm_user, 
    confirm_time, creator_id, create_time, updator_id, update_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and year_month = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
          and status = #{status,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
          and confirm_is = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmUser != null ">
          and confirm_user = #{confirmUser,jdbcType=BIGINT}
      </if>
      <if test="confirmTime != null ">
          and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=BIGINT}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=BIGINT}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.actionNo != null and condition.actionNo != ''">
          and action_no = #{condition.actionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=BIGINT}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and year_month = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
          and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.status != null and condition.status != ''">
          and status = #{condition.status,jdbcType=VARCHAR}
      </if>
      <if test="condition.confirmIs != null and condition.confirmIs != ''">
          and confirm_is = #{condition.confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.confirmUser != null ">
          and confirm_user = #{condition.confirmUser,jdbcType=BIGINT}
      </if>
      <if test="condition.confirmTime != null ">
          and confirm_time = #{condition.confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=BIGINT}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=BIGINT}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and year_month = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
          and status = #{status,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
          and confirm_is = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmUser != null ">
          and confirm_user = #{confirmUser,jdbcType=BIGINT}
      </if>
      <if test="confirmTime != null ">
          and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=BIGINT}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=BIGINT}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_alloc_action
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_alloc_action
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_alloc_action
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_alloc_action
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_alloc_action
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    <selectKey resultType="long" keyProperty="id" order="BEFORE">
      select nextval('atr_seq_buss_ibnr_alloc_action')
    </selectKey>
    insert into atr_buss_ibnr_alloc_action
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actionNo != null">
        action_no,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="yearMonth != null">
        year_month,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="confirmIs != null">
        confirm_is,
      </if>
      <if test="confirmUser != null">
        confirm_user,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actionNo != null">
        #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="confirmIs != null">
        #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmUser != null">
        #{confirmUser,jdbcType=BIGINT},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_ibnr_alloc_action
     (id, action_no, entity_id, 
      year_month, business_source_code, 
      status, confirm_is, confirm_user, 
      confirm_time, creator_id, create_time, 
      updator_id, update_time)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.actionNo,jdbcType=VARCHAR}, #{item.entityId,jdbcType=BIGINT}, 
        #{item.yearMonth,jdbcType=VARCHAR}, #{item.businessSourceCode,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=VARCHAR}, #{item.confirmIs,jdbcType=VARCHAR}, #{item.confirmUser,jdbcType=BIGINT}, 
        #{item.confirmTime,jdbcType=TIMESTAMP}, #{item.creatorId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updatorId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    update atr_buss_ibnr_alloc_action
    <set>
      <if test="actionNo != null">
        action_no = #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="yearMonth != null">
        year_month = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="confirmIs != null">
        confirm_is = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmUser != null">
        confirm_user = #{confirmUser,jdbcType=BIGINT},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    update atr_buss_ibnr_alloc_action
    <set>
      <if test="record.actionNo != null">
        action_no = #{record.actionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=BIGINT},
      </if>
      <if test="record.yearMonth != null">
        year_month = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmIs != null">
        confirm_is = #{record.confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmUser != null">
        confirm_user = #{record.confirmUser,jdbcType=BIGINT},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_buss_ibnr_alloc_action
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_buss_ibnr_alloc_action
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_buss_ibnr_alloc_action
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultType="Long" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    select count(1) from atr_buss_ibnr_alloc_action
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>