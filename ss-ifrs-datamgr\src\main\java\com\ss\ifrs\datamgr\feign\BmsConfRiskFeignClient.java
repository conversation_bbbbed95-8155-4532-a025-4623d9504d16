package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfRiskVo;
import com.ss.platform.core.conf.FeignAuthConfig;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 调用BPL险种的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsConfRiskFeignClient {
    @ApiOperation(value = "根据险种代码查询险种")
    @RequestMapping(value = "/risk/find_by_risk_code/{riskCode}", method = RequestMethod.GET)
    BbsConfRiskVo findByRiskCode(@RequestParam(value = "riskCode") String riskCode);
}
