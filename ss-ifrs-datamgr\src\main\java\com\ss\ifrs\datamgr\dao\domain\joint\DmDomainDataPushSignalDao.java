package com.ss.ifrs.datamgr.dao.domain.joint;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelConditionVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelStatResultVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmOdsDataPushSignalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DmDomainDataPushSignalDao {

    Integer getRowCount(String bizCode, String taskCode);

    Integer updateTaskStatus(String bizCode, String taskCode, String taskStatus, String dealMsg);

    Integer updateVerifyResult(@Param("dapModelStatResultVo") DapModelStatResultVo dapModelStatResultVo, @Param("dmBussDataVerifyVo") DmBussDataVerifyVo dmBussDataVerifyVo);

    String getMaxTaskCode(String taskCodePrefix);

    List<DmOdsDataPushSignalVo> findTodoList(DmDataDrawVo dmDataDrawVo);

    List<String> getValidTaskCode(String bizCode,String yearMonth);

    Integer fileUploadSignalSyncDataPushSignal(@Param("taskCode") String taskCode,@Param("yearMonth") String yearMonth,@Param("bizCode") String bizCode);
}
