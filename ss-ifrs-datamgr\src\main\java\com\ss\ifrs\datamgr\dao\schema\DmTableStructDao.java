package com.ss.ifrs.datamgr.dao.schema;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface DmTableStructDao {
    /**
     * 查询表是否存在
     * @param schemaName schema名
     * @param tableName 表名
     * @return 表的数量
     */
    int queryTableIsExist(String schemaName, String tableName);

    /**
     * 删除表
     * @param schemaName schema名
     * @param tableName 表名
     */
    void dropTable(String schemaName,String tableName);

    /**
     * 查找视图
     * @param schemaName schema名
     * @param viewName 视图名称
     * @return
     */
    int findValidView(String schemaName,String viewName);

    /**
     * 删除视图
     * @param schemaName schema名
     * @param viewName 视图名称
     */
    void dropView(String schemaName,String viewName);


    /**
     * 查询schema下是否有表和字段存在
     *
     * @param tableName
     * @param columnName
     * @param schema
     * @return
     */
    Integer queryTableAndColumnIsExist(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("schema") String schema);

    /**
     * 创建一个带ID的表
     *
     * @param tableName
     */
    void createTable(@Param("tableName") String tableName);

    /**
     * 修改表注释
     *
     * @param tableName
     * @param comment
     * @param schema
     */
    void modifyTableComment(@Param("tableName") String tableName, @Param("comment") String comment, @Param("schema") String schema);

    /**
     * 新增表字段
     *
     * @param tableName
     * @param columnType
     */
    void alterTableAddColumn(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("columnType") String columnType, @Param("schema") String schema);

    /**
     * 修改表字段注释
     *
     * @param tableName
     * @param columnName
     * @param comment
     * @param schema
     */
    void modifyTableColumnComment(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("comment") String comment, @Param("schema") String schema);

    /**
     * 判断表字段是否存在
     * @param tableName
     * @param tableColumnName
     * @return
     */
    Integer findTableColumn(String tableName, String tableColumnName);


    /**
     * 删除字段
     *
     * @param table
     * @param column
     * @param schema
     */
    void dropColumn(@Param("table") String table, @Param("column") String column, @Param("schema") String schema);

    void createIndex(String tableName, String indexName, String indexColumn);

    Boolean checkIfPartitionedTableExists(String tableName);

    void createPartitionedTable(String mainTableName, String partitionName, String value);

}
