<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-07-03 11:34:28 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocDdResultDao">
  <!-- 本文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocDdResult">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="portfolio_no" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
    <result column="icg_no_name" property="icgNoName" jdbcType="VARCHAR" />
    <result column="evaluate_approach" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="pl_judge_rslt" property="plJudgeRslt" jdbcType="VARCHAR" />
    <result column="acc_year_month" property="accYearMonth" jdbcType="VARCHAR" />
    <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="kind_code" property="kindCode" jdbcType="VARCHAR" />
    <result column="business_type" property="businessType" jdbcType="VARCHAR" />
    <result column="ep_amount" property="epAmount" jdbcType="NUMERIC" />
    <result column="ep_ratio" property="epRatio" jdbcType="NUMERIC" />
    <result column="ibnr_amount" property="ibnrAmount" jdbcType="NUMERIC" />
    <result column="fin_detail_code" property="finDetailCode" jdbcType="VARCHAR" />
    <result column="fin_product_code" property="finProductCode" jdbcType="VARCHAR" />
    <result column="fin_sub_product_code" property="finSubProductCode" jdbcType="VARCHAR" />
    <result column="fin_acc_channel" property="finAccChannel" jdbcType="VARCHAR" />
    <result column="center_code" property="centerCode" jdbcType="VARCHAR" />
    <result column="dept_id" property="deptId" jdbcType="VARCHAR" />
    <result column="channel_id" property="channelId" jdbcType="VARCHAR" />
    <result column="company_code4" property="companyCode4" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, action_no, entity_id, year_month, portfolio_no, icg_no, icg_no_name, evaluate_approach, 
    pl_judge_rslt, acc_year_month, policy_no, risk_class_code, kind_code, business_type, 
    ep_amount, ep_ratio, ibnr_amount, fin_detail_code, fin_product_code, fin_sub_product_code, 
    fin_acc_channel, center_code, dept_id, channel_id, company_code4, creator_id, create_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and year_month = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and icg_no = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNoName != null and icgNoName != ''">
          and icg_no_name = #{icgNoName,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and evaluate_approach = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="plJudgeRslt != null and plJudgeRslt != ''">
          and pl_judge_rslt = #{plJudgeRslt,jdbcType=VARCHAR}
      </if>
      <if test="accYearMonth != null and accYearMonth != ''">
          and acc_year_month = #{accYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="kindCode != null and kindCode != ''">
          and kind_code = #{kindCode,jdbcType=VARCHAR}
      </if>
      <if test="businessType != null and businessType != ''">
          and business_type = #{businessType,jdbcType=VARCHAR}
      </if>
      <if test="epAmount != null ">
          and ep_amount = #{epAmount,jdbcType=NUMERIC}
      </if>
      <if test="epRatio != null ">
          and ep_ratio = #{epRatio,jdbcType=NUMERIC}
      </if>
      <if test="ibnrAmount != null ">
          and ibnr_amount = #{ibnrAmount,jdbcType=NUMERIC}
      </if>
      <if test="finDetailCode != null and finDetailCode != ''">
          and fin_detail_code = #{finDetailCode,jdbcType=VARCHAR}
      </if>
      <if test="finProductCode != null and finProductCode != ''">
          and fin_product_code = #{finProductCode,jdbcType=VARCHAR}
      </if>
      <if test="finSubProductCode != null and finSubProductCode != ''">
          and fin_sub_product_code = #{finSubProductCode,jdbcType=VARCHAR}
      </if>
      <if test="finAccChannel != null and finAccChannel != ''">
          and fin_acc_channel = #{finAccChannel,jdbcType=VARCHAR}
      </if>
      <if test="centerCode != null and centerCode != ''">
          and center_code = #{centerCode,jdbcType=VARCHAR}
      </if>
      <if test="deptId != null and deptId != ''">
          and dept_id = #{deptId,jdbcType=VARCHAR}
      </if>
      <if test="channelId != null and channelId != ''">
          and channel_id = #{channelId,jdbcType=VARCHAR}
      </if>
      <if test="companyCode4 != null and companyCode4 != ''">
          and company_code4 = #{companyCode4,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=BIGINT}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.actionNo != null and condition.actionNo != ''">
          and action_no = #{condition.actionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=BIGINT}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and year_month = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.portfolioNo != null and condition.portfolioNo != ''">
          and portfolio_no = #{condition.portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
          and icg_no = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNoName != null and condition.icgNoName != ''">
          and icg_no_name = #{condition.icgNoName,jdbcType=VARCHAR}
      </if>
      <if test="condition.evaluateApproach != null and condition.evaluateApproach != ''">
          and evaluate_approach = #{condition.evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="condition.plJudgeRslt != null and condition.plJudgeRslt != ''">
          and pl_judge_rslt = #{condition.plJudgeRslt,jdbcType=VARCHAR}
      </if>
      <if test="condition.accYearMonth != null and condition.accYearMonth != ''">
          and acc_year_month = #{condition.accYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
          and policy_no = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.riskClassCode != null and condition.riskClassCode != ''">
          and risk_class_code = #{condition.riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.kindCode != null and condition.kindCode != ''">
          and kind_code = #{condition.kindCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessType != null and condition.businessType != ''">
          and business_type = #{condition.businessType,jdbcType=VARCHAR}
      </if>
      <if test="condition.epAmount != null ">
          and ep_amount = #{condition.epAmount,jdbcType=NUMERIC}
      </if>
      <if test="condition.epRatio != null ">
          and ep_ratio = #{condition.epRatio,jdbcType=NUMERIC}
      </if>
      <if test="condition.ibnrAmount != null ">
          and ibnr_amount = #{condition.ibnrAmount,jdbcType=NUMERIC}
      </if>
      <if test="condition.finDetailCode != null and condition.finDetailCode != ''">
          and fin_detail_code = #{condition.finDetailCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.finProductCode != null and condition.finProductCode != ''">
          and fin_product_code = #{condition.finProductCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.finSubProductCode != null and condition.finSubProductCode != ''">
          and fin_sub_product_code = #{condition.finSubProductCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.finAccChannel != null and condition.finAccChannel != ''">
          and fin_acc_channel = #{condition.finAccChannel,jdbcType=VARCHAR}
      </if>
      <if test="condition.centerCode != null and condition.centerCode != ''">
          and center_code = #{condition.centerCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.deptId != null and condition.deptId != ''">
          and dept_id = #{condition.deptId,jdbcType=VARCHAR}
      </if>
      <if test="condition.channelId != null and condition.channelId != ''">
          and channel_id = #{condition.channelId,jdbcType=VARCHAR}
      </if>
      <if test="condition.companyCode4 != null and condition.companyCode4 != ''">
          and company_code4 = #{condition.companyCode4,jdbcType=VARCHAR}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=BIGINT}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and year_month = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and icg_no = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNoName != null and icgNoName != ''">
          and icg_no_name = #{icgNoName,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and evaluate_approach = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="plJudgeRslt != null and plJudgeRslt != ''">
          and pl_judge_rslt = #{plJudgeRslt,jdbcType=VARCHAR}
      </if>
      <if test="accYearMonth != null and accYearMonth != ''">
          and acc_year_month = #{accYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="kindCode != null and kindCode != ''">
          and kind_code = #{kindCode,jdbcType=VARCHAR}
      </if>
      <if test="businessType != null and businessType != ''">
          and business_type = #{businessType,jdbcType=VARCHAR}
      </if>
      <if test="epAmount != null ">
          and ep_amount = #{epAmount,jdbcType=NUMERIC}
      </if>
      <if test="epRatio != null ">
          and ep_ratio = #{epRatio,jdbcType=NUMERIC}
      </if>
      <if test="ibnrAmount != null ">
          and ibnr_amount = #{ibnrAmount,jdbcType=NUMERIC}
      </if>
      <if test="finDetailCode != null and finDetailCode != ''">
          and fin_detail_code = #{finDetailCode,jdbcType=VARCHAR}
      </if>
      <if test="finProductCode != null and finProductCode != ''">
          and fin_product_code = #{finProductCode,jdbcType=VARCHAR}
      </if>
      <if test="finSubProductCode != null and finSubProductCode != ''">
          and fin_sub_product_code = #{finSubProductCode,jdbcType=VARCHAR}
      </if>
      <if test="finAccChannel != null and finAccChannel != ''">
          and fin_acc_channel = #{finAccChannel,jdbcType=VARCHAR}
      </if>
      <if test="centerCode != null and centerCode != ''">
          and center_code = #{centerCode,jdbcType=VARCHAR}
      </if>
      <if test="deptId != null and deptId != ''">
          and dept_id = #{deptId,jdbcType=VARCHAR}
      </if>
      <if test="channelId != null and channelId != ''">
          and channel_id = #{channelId,jdbcType=VARCHAR}
      </if>
      <if test="companyCode4 != null and companyCode4 != ''">
          and company_code4 = #{companyCode4,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=BIGINT}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnr_alloc_dd_result"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnr_alloc_dd_result"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnr_alloc_dd_result"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocDdResult">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnr_alloc_dd_result"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnr_alloc_dd_result"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocDdResult">
    <selectKey resultType="long" keyProperty="id" order="BEFORE">
      select nextval('atr_seq_buss_ibnr_alloc_dd_result')
    </selectKey>
    insert into "atr_buss_ibnr_alloc_dd_result"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actionNo != null">
        action_no,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="yearMonth != null">
        year_month,
      </if>
      <if test="portfolioNo != null">
        portfolio_no,
      </if>
      <if test="icgNo != null">
        icg_no,
      </if>
      <if test="icgNoName != null">
        icg_no_name,
      </if>
      <if test="evaluateApproach != null">
        evaluate_approach,
      </if>
      <if test="plJudgeRslt != null">
        pl_judge_rslt,
      </if>
      <if test="accYearMonth != null">
        acc_year_month,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="riskClassCode != null">
        risk_class_code,
      </if>
      <if test="kindCode != null">
        kind_code,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="epAmount != null">
        ep_amount,
      </if>
      <if test="epRatio != null">
        ep_ratio,
      </if>
      <if test="ibnrAmount != null">
        ibnr_amount,
      </if>
      <if test="finDetailCode != null">
        fin_detail_code,
      </if>
      <if test="finProductCode != null">
        fin_product_code,
      </if>
      <if test="finSubProductCode != null">
        fin_sub_product_code,
      </if>
      <if test="finAccChannel != null">
        fin_acc_channel,
      </if>
      <if test="centerCode != null">
        center_code,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="companyCode4 != null">
        company_code4,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actionNo != null">
        #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNoName != null">
        #{icgNoName,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="plJudgeRslt != null">
        #{plJudgeRslt,jdbcType=VARCHAR},
      </if>
      <if test="accYearMonth != null">
        #{accYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="riskClassCode != null">
        #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="kindCode != null">
        #{kindCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="epAmount != null">
        #{epAmount,jdbcType=NUMERIC},
      </if>
      <if test="epRatio != null">
        #{epRatio,jdbcType=NUMERIC},
      </if>
      <if test="ibnrAmount != null">
        #{ibnrAmount,jdbcType=NUMERIC},
      </if>
      <if test="finDetailCode != null">
        #{finDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="finProductCode != null">
        #{finProductCode,jdbcType=VARCHAR},
      </if>
      <if test="finSubProductCode != null">
        #{finSubProductCode,jdbcType=VARCHAR},
      </if>
      <if test="finAccChannel != null">
        #{finAccChannel,jdbcType=VARCHAR},
      </if>
      <if test="centerCode != null">
        #{centerCode,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode4 != null">
        #{companyCode4,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_buss_ibnr_alloc_dd_result"
     (id, action_no, entity_id, 
      year_month, portfolio_no, icg_no, 
      icg_no_name, evaluate_approach, 
      pl_judge_rslt, acc_year_month, policy_no, 
      risk_class_code, kind_code, business_type, 
      ep_amount, ep_ratio, ibnr_amount, 
      fin_detail_code, fin_product_code, 
      fin_sub_product_code, fin_acc_channel, 
      center_code, dept_id, channel_id, 
      company_code4, creator_id, create_time
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.actionNo,jdbcType=VARCHAR}, #{item.entityId,jdbcType=BIGINT}, 
        #{item.yearMonth,jdbcType=VARCHAR}, #{item.portfolioNo,jdbcType=VARCHAR}, #{item.icgNo,jdbcType=VARCHAR}, 
        #{item.icgNoName,jdbcType=VARCHAR}, #{item.evaluateApproach,jdbcType=VARCHAR}, 
        #{item.plJudgeRslt,jdbcType=VARCHAR}, #{item.accYearMonth,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.riskClassCode,jdbcType=VARCHAR}, #{item.kindCode,jdbcType=VARCHAR}, #{item.businessType,jdbcType=VARCHAR}, 
        #{item.epAmount,jdbcType=NUMERIC}, #{item.epRatio,jdbcType=NUMERIC}, #{item.ibnrAmount,jdbcType=NUMERIC}, 
        #{item.finDetailCode,jdbcType=VARCHAR}, #{item.finProductCode,jdbcType=VARCHAR}, 
        #{item.finSubProductCode,jdbcType=VARCHAR}, #{item.finAccChannel,jdbcType=VARCHAR}, 
        #{item.centerCode,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR}, #{item.channelId,jdbcType=VARCHAR}, 
        #{item.companyCode4,jdbcType=VARCHAR}, #{item.creatorId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocDdResult">
    update "atr_buss_ibnr_alloc_dd_result"
    <set>
      <if test="actionNo != null">
        action_no = #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="yearMonth != null">
        year_month = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        portfolio_no = #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        icg_no = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNoName != null">
        icg_no_name = #{icgNoName,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        evaluate_approach = #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="plJudgeRslt != null">
        pl_judge_rslt = #{plJudgeRslt,jdbcType=VARCHAR},
      </if>
      <if test="accYearMonth != null">
        acc_year_month = #{accYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="riskClassCode != null">
        risk_class_code = #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="kindCode != null">
        kind_code = #{kindCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="epAmount != null">
        ep_amount = #{epAmount,jdbcType=NUMERIC},
      </if>
      <if test="epRatio != null">
        ep_ratio = #{epRatio,jdbcType=NUMERIC},
      </if>
      <if test="ibnrAmount != null">
        ibnr_amount = #{ibnrAmount,jdbcType=NUMERIC},
      </if>
      <if test="finDetailCode != null">
        fin_detail_code = #{finDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="finProductCode != null">
        fin_product_code = #{finProductCode,jdbcType=VARCHAR},
      </if>
      <if test="finSubProductCode != null">
        fin_sub_product_code = #{finSubProductCode,jdbcType=VARCHAR},
      </if>
      <if test="finAccChannel != null">
        fin_acc_channel = #{finAccChannel,jdbcType=VARCHAR},
      </if>
      <if test="centerCode != null">
        center_code = #{centerCode,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode4 != null">
        company_code4 = #{companyCode4,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocDdResult">
    update "atr_buss_ibnr_alloc_dd_result"
    <set>
      <if test="record.actionNo != null">
        action_no = #{record.actionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=BIGINT},
      </if>
      <if test="record.yearMonth != null">
        year_month = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioNo != null">
        portfolio_no = #{record.portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        icg_no = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNoName != null">
        icg_no_name = #{record.icgNoName,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluateApproach != null">
        evaluate_approach = #{record.evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.plJudgeRslt != null">
        pl_judge_rslt = #{record.plJudgeRslt,jdbcType=VARCHAR},
      </if>
      <if test="record.accYearMonth != null">
        acc_year_month = #{record.accYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.riskClassCode != null">
        risk_class_code = #{record.riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="record.kindCode != null">
        kind_code = #{record.kindCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=VARCHAR},
      </if>
      <if test="record.epAmount != null">
        ep_amount = #{record.epAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.epRatio != null">
        ep_ratio = #{record.epRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.ibnrAmount != null">
        ibnr_amount = #{record.ibnrAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.finDetailCode != null">
        fin_detail_code = #{record.finDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="record.finProductCode != null">
        fin_product_code = #{record.finProductCode,jdbcType=VARCHAR},
      </if>
      <if test="record.finSubProductCode != null">
        fin_sub_product_code = #{record.finSubProductCode,jdbcType=VARCHAR},
      </if>
      <if test="record.finAccChannel != null">
        fin_acc_channel = #{record.finAccChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.centerCode != null">
        center_code = #{record.centerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deptId != null">
        dept_id = #{record.deptId,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode4 != null">
        company_code4 = #{record.companyCode4,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_buss_ibnr_alloc_dd_result"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_buss_ibnr_alloc_dd_result"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_buss_ibnr_alloc_dd_result"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocDdResult">
    select count(1) from "atr_buss_ibnr_alloc_dd_result"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>