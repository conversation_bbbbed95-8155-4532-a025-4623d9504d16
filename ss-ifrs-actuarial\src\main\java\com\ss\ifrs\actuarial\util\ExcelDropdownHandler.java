package com.ss.ifrs.actuarial.util;

import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel下拉选项处理工具类
 */
public class ExcelDropdownHandler {

    /**
     * 为指定的Sheet添加下拉选项
     * 
     * @param sheet Excel工作表
     * @param options 下拉选项列表
     * @param firstRow 起始行（从0开始）
     * @param lastRow 结束行
     * @param firstCol 起始列（从0开始）
     * @param lastCol 结束列
     */
    public static void addDropdownList(Sheet sheet, List<String> options, int firstRow, int lastRow, int firstCol, int lastCol) {
        if (sheet == null || options == null || options.isEmpty()) {
            return;
        }
        
        DataValidationHelper helper = sheet.getDataValidationHelper();
        
        // 由于Excel下拉列表有255个字符的限制，需要将选项分批处理
        // 计算每批可以包含的选项数量
        int optionsPerBatch = calculateOptionsPerBatch(options);
        
        // 如果选项太多，需要分批处理
        if (optionsPerBatch < options.size()) {
            int batchCount = (int) Math.ceil((double) options.size() / optionsPerBatch);
            int rowsPerBatch = (lastRow - firstRow + 1) / batchCount;
            
            for (int i = 0; i < batchCount; i++) {
                int startIdx = i * optionsPerBatch;
                int endIdx = Math.min(startIdx + optionsPerBatch, options.size());
                List<String> batchOptions = options.subList(startIdx, endIdx);
                
                int batchFirstRow = firstRow + (i * rowsPerBatch);
                int batchLastRow = (i == batchCount - 1) ? lastRow : batchFirstRow + rowsPerBatch - 1;
                
                addDropdownListBatch(sheet, helper, batchOptions, batchFirstRow, batchLastRow, firstCol, lastCol);
            }
        } else {
            // 如果选项数量较少，直接添加
            addDropdownListBatch(sheet, helper, options, firstRow, lastRow, firstCol, lastCol);
        }
    }
    
    /**
     * 添加一批下拉选项
     */
    private static void addDropdownListBatch(Sheet sheet, DataValidationHelper helper, List<String> options, 
                                           int firstRow, int lastRow, int firstCol, int lastCol) {
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        
        // 创建下拉框数据
        DataValidationConstraint constraint = helper.createExplicitListConstraint(
            options.toArray(new String[0])
        );
        
        // 创建数据验证对象
        DataValidation dataValidation = helper.createValidation(constraint, addressList);
        dataValidation.setShowErrorBox(true);
        
        // 添加验证到sheet
        sheet.addValidationData(dataValidation);
    }
    
    /**
     * 计算每批可以包含的最大选项数量，确保不超过255个字符的限制
     */
    private static int calculateOptionsPerBatch(List<String> options) {
        int maxChars = 255;
        int totalOptions = options.size();
        
        // 先尝试全部放入一个批次
        int totalLength = 0;
        for (int i = 0; i < totalOptions; i++) {
            // 每个选项之间需要一个逗号分隔符
            totalLength += options.get(i).length() + (i < totalOptions - 1 ? 1 : 0);
            
            // 如果超过限制，返回当前索引
            if (totalLength > maxChars) {
                return Math.max(1, i);
            }
        }
        
        // 如果所有选项的总长度都没有超过限制，返回全部选项数量
        return totalOptions;
    }
} 