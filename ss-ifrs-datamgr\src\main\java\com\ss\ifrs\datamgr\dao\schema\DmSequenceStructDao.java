package com.ss.ifrs.datamgr.dao.schema;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DmSequenceStructDao {
    /**
     * 创建序列
     *
     * @param sequenceName
     * @param schema
     */
    void createSequence(@Param("sequenceName") String sequenceName, @Param("schema") String schema);

    /**
     * 删除序列
     *
     * @param sequence
     * @param schema
     */
    void dropSequence(@Param("sequence") String sequence, @Param("sequence") String schema);

    /**
     * 查询schema下是否存在序列
     *
     * @param sequenceName
     * @param schema
     * @return
     */
    Integer querySequenceIsExist(@Param("sequenceName") String sequenceName, @Param("schema") String schema);
}
