package com.ss.ifrs.actuarial.util.abp;

import com.google.common.base.CaseFormat;
import com.ss.ifrs.actuarial.util.EcfUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * 异步批量插入处理器
 *
 * <AUTHOR>
 */
@Slf4j
public class AsyncBatchProcessor implements AutoCloseable {

    private final AtomicInteger threadCounter = new AtomicInteger(0);

    private final String actionNo;

    private final JdbcTemplate jdbcTemplate;

    private final Map<String, MainVo> voMap = new HashMap<>();

    private final ExecutorService pool;

    private final List<Future<?>> futures = new ArrayList<>();

    private final Map<String, LongAdder> costCounterMap = new HashMap<>();
    private final Map<String, LongAdder> linesCounterMap = new HashMap<>();

    public AsyncBatchProcessor(JdbcTemplate jdbcTemplate, int nThreads) {
        this(EcfUtil.createActionNo(), jdbcTemplate, nThreads);
    }

    public AsyncBatchProcessor(String actionNo, JdbcTemplate jdbcTemplate, int nThreads) {
        this.actionNo = actionNo;
        this.jdbcTemplate = jdbcTemplate;
        this.pool = Executors.newFixedThreadPool(nThreads);
    }

    public void addType(Class<?> clazz) {
        Tab tab = clazz.getAnnotation(Tab.class);
        if (tab == null || StringUtils.isBlank(tab.value())) {
            throw new RuntimeException("无法获取表名 " + clazz);
        }
        String table = tab.value();

        PropertyDescriptor[] ps = BeanUtils.getPropertyDescriptors(clazz);
        List<PropertyDescriptor> pdList = new ArrayList<>();
        List<String> columns = new ArrayList<>();
        for (PropertyDescriptor p : ps) {
            if (p.getReadMethod() != null && p.getWriteMethod() != null) {
                Field field = FieldUtils.getField(clazz, p.getName(), true);
                IgnoreCol ignoreCol = field.getAnnotation(IgnoreCol.class);
                if (ignoreCol != null) {
                    continue;
                }
                Col col = field.getAnnotation(Col.class);
                if (col != null && StringUtils.isNotBlank(col.value())) {
                    columns.add(col.value());
                } else {
                    columns.add(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, p.getName()));
                }
                pdList.add(p);
            }
        }

        String[] places = new String[columns.size()];
        Arrays.fill(places, "?");

        String sql = "insert into " + table + " (" +
                String.join(",", columns) +
                ") values (" +
                String.join(",", places) +
                ")";
        voMap.put(clazz.getName(), new MainVo(table, sql, pdList));
        costCounterMap.put(table, new LongAdder());
        linesCounterMap.put(table, new LongAdder());
    }

    public void insert(Object entity) {
        if (entity == null) {
            return;
        }
        String type = entity.getClass().getName();
        MainVo vo = voMap.get(type);
        if (vo == null) {
            throw new RuntimeException("No such type: " + type);
        }

        // wait
        while (threadCounter.get() > 80) {
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (Exception ignore) {
            }
        }

        vo.tryInsert(entity);
    }

    public void end() {
        for (MainVo vo : voMap.values()) {
            vo.forceInsert();
        }
        pool.shutdown();
        List<Exception> es = new ArrayList<>();
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("end error", e);
                es.add(e);
            }
        }
        if (!es.isEmpty()) {
            throw new RuntimeException(es.get(0));
        }
    }

    @Override
    public void close() throws Exception {
        try {
            if (pool != null) {
                pool.shutdownNow();
            }
        } catch (Exception e) {
            log.warn("abp close 异常", e);
        }
        logCost();
    }

    private void logCost() {
        try {
            String sql = "insert into atr_log_abp_cost (action_no," +
                    "cost_type," +
                    "cost_time," +
                    "cost_lines) values (?, ?, ?, ?)";
            jdbcTemplate.execute(sql, (PreparedStatementCallback<?>) ps -> {
                costCounterMap.forEach((type, ldCost) -> {
                    try {
                        ps.setObject(1, actionNo);
                        ps.setObject(2, type);
                        ps.setObject(3, ldCost.sum() / 1000_000_000D);
                        ps.setObject(4, linesCounterMap.get(type).sum());
                        ps.addBatch();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
                ps.executeBatch();
                ps.clearBatch();
                return null;
            });
        } catch (Exception e) {
            log.warn("abp log cost 异常", e);
        }
    }

    private class MainVo {

        private final String table;

        private final String sql;

        private final PropertyDescriptor[] ps;

        private final Executor[] executors = new Executor[20];

        public MainVo(String table, String sql, List<PropertyDescriptor> ps) {
            this.table = table;
            this.sql = sql;
            this.ps = ps == null ? null : ps.toArray(new PropertyDescriptor[0]);
            for (int i = 0; i < executors.length; i++) {
                int threshold = (int) (1000 * (0.7 + 0.3 * (i / (executors.length - 1f))));
                executors[i] = new Executor(threshold);
            }
        }

        public void tryInsert(Object[] values) {
            int index = ThreadLocalRandom.current().nextInt(executors.length);
            Executor executor = executors[index];
            executor.addArgs(values);
            executor.tryInsert(table, sql);
        }

        public void tryInsert(Object entity) {
            if (entity == null) {
                return;
            }
            Object[] values = new Object[ps.length];
            try {
                for (int i = 0; i < ps.length; i++) {
                    values[i] = ps[i].getReadMethod().invoke(entity);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            tryInsert(values);
        }

        public void forceInsert() {
            for (Executor executor : executors) {
                executor.forceInsert(table, sql);
            }
        }

    }

    private class Executor {

        private final int threshold;

        private final Object lock = new Object();

        private List<Object[]> argsList = new ArrayList<>();

        public Executor(int threshold) {
            this.threshold = threshold;
        }

        public void addArgs(Object[] values) {
            synchronized (lock) {
                argsList.add(values);
            }
        }

        public void tryInsert(String table, String sql) {
            if (argsList.size() >= threshold) {
                synchronized (lock) {
                    if (argsList.size() >= threshold) {
                        List<Object[]> argsListTemp = argsList;
                        argsList = new ArrayList<>();
                        synchronized (futures) {
                            threadCounter.incrementAndGet();
                            futures.add(pool.submit(() -> {
                                try {
                                    batchUpdate(table, sql, argsListTemp);
                                } finally {
                                    argsListTemp.clear();
                                    threadCounter.decrementAndGet();
                                }
                            }));
                        }
                    }
                }
            }
        }

        public void forceInsert(String table, String sql) {
            if (!argsList.isEmpty()) {
                futures.add(pool.submit(() -> {
                    batchUpdate(table, sql, argsList);
                    argsList.clear();
                }));
            }
        }

        private void batchUpdate(String table, String sql, List<Object[]> argsList) {
            if (!argsList.isEmpty()) {
                try {
                    long start = System.nanoTime();

                    jdbcTemplate.execute(sql, (PreparedStatementCallback<?>) ps -> {
                        for (Object[] values : argsList) {
                            for (int i = 0; i < values.length; i++) {
                                Object v = values[i];
                                if (v == null) {
                                    ps.setNull(i + 1, Types.NULL);
                                } else if (v instanceof Date) {
                                    ps.setTimestamp(i + 1, new Timestamp(((Date) v).getTime()));
                                } else {
                                    ps.setObject(i + 1, v);
                                }
                            }
                            ps.addBatch();
                        }
                        ps.executeBatch();
                        ps.clearBatch();
                        return null;
                    });

                    costCounterMap.get(table).add(System.nanoTime() - start);
                    linesCounterMap.get(table).add(argsList.size());
                } catch (Exception e) {
                    String argStr = argsList.stream().map(Arrays::toString).collect(Collectors.joining(", "));
                    throw new RuntimeException("batch update error, sql = " + sql + ", arg = " + argStr, e);
                }
            }
        }
    }

}
