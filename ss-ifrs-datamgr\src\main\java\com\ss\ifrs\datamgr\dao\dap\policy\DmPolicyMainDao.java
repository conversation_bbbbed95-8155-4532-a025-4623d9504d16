
package com.ss.ifrs.datamgr.dao.dap.policy;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmUnitNoAdapterVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCreateCmUnitNoVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.ifrs.datamgr.pojo.dap.po.policy.DmPolicyMain;
import com.ss.ifrs.datamgr.pojo.dap.vo.policy.DmPolicyMainVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.io.Serializable;
import java.util.List;

/**
 * Create Date: 2020-12-18 18:21:55<br/>
 * Description: I17保单信息表 Dao类<br/>
 * Related Table Name: DM_POLICY_MAIN<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmPolicyMainDao extends IDao<DmPolicyMain, Serializable> {
    Page<DmPolicyMainVo> findDataList(DmDataSearchVo searchVo, Pageable pageParam);
    List<String> findListByEndorseNo(DmDataSearchVo dmDataSearchVo);
    List<String> findTreatyNoByEndorseNo(DmDataSearchVo dmDataSearchVo);
    Page<DmPolicyMainVo> findDataListProt(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmPolicyMainVo> findDataListOutwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmPolicyMainVo> findDataListProtOutwards(DmDataSearchVo searchVo, Pageable pageParam);
    List<DmPolicyMainVo> findPolicyByEntityId(DmCreateCmUnitNoVo cmUnitNoAdapterVo);
    Page<DmPolicyMainVo> findDataListMain(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmPolicyMainVo> queryPolicy(DmPolicyMainVo dmPolicyMainVo, Pageable pageParam);
}