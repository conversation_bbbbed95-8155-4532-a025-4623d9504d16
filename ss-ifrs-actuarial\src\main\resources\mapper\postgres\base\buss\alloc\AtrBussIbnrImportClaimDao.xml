<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrImportClaimDao">
  <!-- 本文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim">
    <id column="ibnr_claim_id" property="ibnrClaimId" jdbcType="BIGINT" />
    <result column="claim_main_id" property="claimMainId" jdbcType="BIGINT" />
    <result column="treaty_no" property="treatyNo" jdbcType="VARCHAR" />
    <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
    <result column="kind_code" property="kindCode" jdbcType="VARCHAR" />
    <result column="claim_no" property="claimNo" jdbcType="VARCHAR" />
    <result column="treaty_name" property="treatyName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ibnr_claim_id, claim_main_id, treaty_no, treaty_name, policy_no, kind_code, claim_no
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="ibnrClaimId != null ">
          and ibnr_claim_id = #{ibnrClaimId,jdbcType=BIGINT}
      </if>
      <if test="claimMainId != null ">
          and claim_main_id = #{claimMainId,jdbcType=BIGINT}
      </if>
      <if test="treatyNo != null and treatyNo != ''">
          and treaty_no = #{treatyNo,jdbcType=VARCHAR}
      </if>
      <if test="treatyName != null and treatyName != ''">
        and treaty_name = #{treatyName,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="kindCode != null and kindCode != ''">
          and kind_code = #{kindCode,jdbcType=VARCHAR}
      </if>
      <if test="claimNo != null and claimNo != ''">
          and claim_no = #{claimNo,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.ibnrClaimId != null ">
          and ibnr_claim_id = #{condition.ibnrClaimId,jdbcType=BIGINT}
      </if>
      <if test="condition.claimMainId != null ">
          and claim_main_id = #{condition.claimMainId,jdbcType=BIGINT}
      </if>
      <if test="condition.treatyNo != null and condition.treatyNo != ''">
          and treaty_no = #{condition.treatyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.treatyName != null and condition.treatyName != ''">
        and treaty_name = #{condition.treatyName,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
          and policy_no = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.kindCode != null and condition.kindCode != ''">
          and kind_code = #{condition.kindCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.claimNo != null and condition.claimNo != ''">
          and claim_no = #{condition.claimNo,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="ibnrClaimId != null ">
          and ibnr_claim_id = #{ibnrClaimId,jdbcType=BIGINT}
      </if>
      <if test="claimMainId != null ">
          and claim_main_id = #{claimMainId,jdbcType=BIGINT}
      </if>
      <if test="treatyNo != null and treatyNo != ''">
          and treaty_no = #{treatyNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="kindCode != null and kindCode != ''">
          and kind_code = #{kindCode,jdbcType=VARCHAR}
      </if>
      <if test="claimNo != null and claimNo != ''">
          and claim_no = #{claimNo,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_import_claim
    where ibnr_claim_id = #{ibnrClaimId,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_import_claim
    where ibnr_claim_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_import_claim
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_import_claim
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_ibnr_import_claim
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ibnr_claim_id" keyProperty="ibnrClaimId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim">
    <selectKey resultType="long" keyProperty="ibnrClaimId" order="BEFORE">
      select nextval('atr_seq_buss_ibnr_import_calim')
    </selectKey>
    insert into atr_buss_ibnr_import_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ibnrClaimId != null">
        ibnr_claim_id,
      </if>
      <if test="claimMainId != null">
        claim_main_id,
      </if>
      <if test="treatyNo != null">
        treaty_no,
      </if>
      <if test="treatyName != null">
        treaty_name,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="kindCode != null">
        kind_code,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ibnrClaimId != null">
        #{ibnrClaimId,jdbcType=BIGINT},
      </if>
      <if test="claimMainId != null">
        #{claimMainId,jdbcType=BIGINT},
      </if>
      <if test="treatyNo != null">
        #{treatyNo,jdbcType=VARCHAR},
      </if>
      <if test="treatyName != null">
        #{treatyName,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="kindCode != null">
        #{kindCode,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_ibnr_import_claim
     (ibnr_claim_id, claim_main_id, treaty_no, treaty_name,
      policy_no, kind_code, claim_no
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.ibnrClaimId,jdbcType=BIGINT}, #{item.claimMainId,jdbcType=BIGINT}, #{item.treatyNo,jdbcType=VARCHAR},  #{item.treatyName,jdbcType=VARCHAR},
        #{item.policyNo,jdbcType=VARCHAR}, #{item.kindCode,jdbcType=VARCHAR}, #{item.claimNo,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim">
    update atr_buss_ibnr_import_claim
    <set>
      <if test="claimMainId != null">
        claim_main_id = #{claimMainId,jdbcType=BIGINT},
      </if>
      <if test="treatyNo != null">
        treaty_no = #{treatyNo,jdbcType=VARCHAR},
      </if>
      <if test="treatyName != null">
        treaty_name = #{treatyName,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="kindCode != null">
        kind_code = #{kindCode,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
    </set>
    where ibnr_claim_id = #{ibnrClaimId,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim">
    update atr_buss_ibnr_import_claim
    <set>
      <if test="record.claimMainId != null">
        claim_main_id = #{record.claimMainId,jdbcType=BIGINT},
      </if>
      <if test="record.treatyNo != null">
        treaty_no = #{record.treatyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.treatyName != null">
        treaty_name = #{record.treatyName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.kindCode != null">
        kind_code = #{record.kindCode,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_buss_ibnr_import_claim
    where ibnr_claim_id = #{ibnrClaimId,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_buss_ibnr_import_claim
    where ibnr_claim_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_buss_ibnr_import_claim
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim">
    select count(1) from atr_buss_ibnr_import_claim
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>