package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.profitable.DmDomainProfitableTiPaaDao;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitablePaaService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_PAA")
public class DmDomainProfitableTiPaaServiceImpl implements DmDomainProfitablePaaService {

    @Autowired
    private DmDomainProfitableTiPaaDao dmDomainProfitableTiPaaDao;

    @Qualifier(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_COMMON")
    @Autowired
    private DmDomainProfitableCommonService dmDomainProfitableCommonService;

    @Override
    public void calCmUnitDevelopAmount(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmSetAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiPaaDao.deleteDuctProfitableSetAmount(dmIcgProcVo);
        dmDomainProfitableTiPaaDao.calCmSetAmount(dmIcgProcVo);
    }

    @Override
    public void calIacfRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiPaaDao.calIacfRate(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiPaaDao.calCmUnitExpectFutureAmount(dmIcgProcVo);
    }

    @Override
    public void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiPaaDao.calCmUnitExpectLossAmount(dmIcgProcVo);
    }

    @Override
    public void calCmSetCostRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiPaaDao.calCmSetCostRate(dmIcgProcVo);
    }

    @Override
    public void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiPaaDao.calCmUnitCostRate(dmIcgProcVo);
    }

    @Override
    public void Profitable(DmIcgProcVo dmIcgProcVo) {
        //初始化盈亏判断计量单元
        dmDomainProfitableTiPaaDao.deleteDuctCmUnitProfitable(dmIcgProcVo);
        dmDomainProfitableTiPaaDao.insertDuctCmUnitProfitable(dmIcgProcVo);

        //满足条件的数据为空, 结束判定
        Integer count = dmDomainProfitableTiPaaDao.getDuctCmUnitProfitableCount(dmIcgProcVo);
        if(count<=0){
            return;
        }

        //初始化计算参数数据
        dmDomainProfitableCommonService.prepareQuota(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareInterestRate(dmIcgProcVo);
        dmDomainProfitableCommonService.prepareDevelopQuota(dmIcgProcVo);
        //集合现金流
        calCmSetAmount(dmIcgProcVo);

        //校验数据:数据完整性 及 配置表数据是否缺失,  NODE_STATE - 1 校验通过;  NODE_STATE -2 校验不通过
        dmDomainProfitableCommonService.checkFactor(dmIcgProcVo);

        //存在数据不通过校验，更新数据状态，结束处理
        Integer failCount = dmDomainProfitableTiPaaDao.getDuctCmUnitProfitableFailCount(dmIcgProcVo);
        if(failCount>0){
            dmDomainProfitableTiPaaDao.updateProfitableEstimateCheckFail(dmIcgProcVo);
            return;
        }

        //跟單IACF率
        calIacfRate(dmIcgProcVo);
        //集合成本率
        calCmSetCostRate(dmIcgProcVo);
        //预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
        calCmUnitExpectFutureAmount(dmIcgProcVo);
        //预期赔付现金流现值
        calCmUnitExpectLossAmount(dmIcgProcVo);
        //集合成本率(非金融风险调整)
        calCmUnitCostRate(dmIcgProcVo);
        //更新盈亏标识
        dmDomainProfitableTiPaaDao.updateProfitableEstimateDuctData(dmIcgProcVo);

        //更新判定结果及流程状态
        dmDomainProfitableTiPaaDao.updateProfitableEstimateResult(dmIcgProcVo);


    }

}
