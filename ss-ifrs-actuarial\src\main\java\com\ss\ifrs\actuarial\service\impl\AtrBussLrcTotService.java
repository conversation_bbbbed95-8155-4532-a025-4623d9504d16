package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.*;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import com.ss.library.utils.ClassUtil;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 预期保费现金流 service （合约分出业务）
 *
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussLrcTotService extends AbstractAtrBussLrcService {

    private AtomicLong icpMainIdGen;
    private AtomicLong icgMainIdGen;
    // 新增：R表ID生成器
    private AtomicLong rTableIdGen;

    private final IndexFactory<AtrBussToLrcTUl> preToLrcUls = new IndexFactory<>();

    private final Map<String, AtrBussLrcToIcg> atrBussLrcToIcgHashMap = new HashMap<>();

    /*  part index start */
    // 需要参与计算的底单信息
    private final List<AtrBussToLrcTUlR> treatyPolicyList = new ArrayList<>();
    private final IndexFactory<AtrDapTreaty> treatyConfIdx = new IndexFactory<>();

    private final IndexFactory<BigDecimal> preIcgPremiumIndex = new IndexFactory<>();

    // 统计新旧保费，index 0 为旧保费,index 1 为新保费
    private final Map<List<String>, BigDecimal[]> icgPremiumIndex = new HashMap<>();

    private final IndexFactory<AtrBussToLrcTUl> atrToLrcUls = new IndexFactory<>();

    // 新增：存储汇总后的发展期数据
    private final IndexFactory<AtrBussToLrcTUlDev> atrToLrcUlDevs = new IndexFactory<>();

    /*  part index end */

    public void entry(String actionNo, Long entityId, String yearMonth) {
        initEnvParams(actionNo, entityId, yearMonth, "TO");
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            initAbp(abp);
            logDebug("collectData");
            collectData();
            logDebug("calcIcp");
            calcIcp();
            logDebug("insertAggregatedData");
            insertAggregatedData();
            logDebug("calcIcg");
            atrBussLrcToIcgHashMap.values().forEach(this::calcIcg);
            logDebug("saveIcgPremium");
            saveIcgPremium();
            logDebug("abp-end");
            abp.end();
            logDebug("end");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException(e);
        }
    }

    private void saveIcgPremium() {
        icgPremiumIndex.forEach((key, value) -> {
            AtrBussToLrcIcgPremium atrBussTOLicIcgPremium = new AtrBussToLrcIcgPremium();
            atrBussTOLicIcgPremium.setActionNo(actionNo);
            atrBussTOLicIcgPremium.setYearMonth(yearMonth);
            atrBussTOLicIcgPremium.setPortfolioNo(key.get(0));
            atrBussTOLicIcgPremium.setIcgNo(key.get(1));
            atrBussTOLicIcgPremium.setRiskClassCode(key.get(2));
            atrBussTOLicIcgPremium.setOldPremium(value[0]);
            atrBussTOLicIcgPremium.setNewPremium(value[1]);
            atrBussTOLicIcgPremium.setMainTreatyType("T");
            abp.insert(atrBussTOLicIcgPremium);
        });
    }

    private void calcIcgPremium(String portfolioNo, String icgNo, String riskClassCode, Date contractDate, BigDecimal premium) {
        List<String> key = Arrays.asList(portfolioNo, icgNo, riskClassCode);
        // 获取上期的旧保费
        BigDecimal oldPremium = nvl(preIcgPremiumIndex.one(key));
        
        // 只有当合同日期为当期时，才将premium计入新保费
        if (Dates.toChar(contractDate, "yyyyMM").equals(yearMonth)) {
            if (icgPremiumIndex.containsKey(key)) {
                BigDecimal[] premiums = icgPremiumIndex.get(key);
                premiums[1] = round(premiums[1].add(premium));
                icgPremiumIndex.put(key, premiums);
            } else {
                icgPremiumIndex.put(key, new BigDecimal[]{oldPremium, premium});
            }
        }else{
            if ( !icgPremiumIndex.containsKey(key) ) {
                icgPremiumIndex.put(key, new BigDecimal[]{oldPremium, BigDecimal.ZERO});
            }
        }
    }


    // 计算底单
    private void calcIcp() {
        treatyPolicyList.forEach(this::calcIcp);
    }

    /**
     * @param icu 合约标的保单对应的现金流
     */
    private void calcIcp(AtrBussToLrcTUlR icu) throws RuntimeException {
        List<String> treatyKey = Arrays.asList(icu.getTreatyNo(), icu.getSectionoCode(), icu.getReinsurerCode());
        // 合约信息
        AtrDapTreaty treatyInfo = treatyConfIdx.one(treatyKey);

        if (treatyInfo == null) {
            treatyInfo = treatyConfIdx.one(Collections.singletonList(icu.getTreatyNo()));
            if (treatyInfo == null) {
                treatyInfo = new AtrDapTreaty();
            }
            // throw new RuntimeException(String.format("找不到对应的合约号: %s,分项为: %s,再保人为: %s",icu.getTreatyNo(),icu.getSectionoCode(),icu.getReinsurerCode()));
        }
        
        // 将treatyInfo数据设置到icu对象中
        if (treatyInfo.getFixedFeeRate() != null) {
            icu.setFixedFeeRate(treatyInfo.getFixedFeeRate());
        }
        if (treatyInfo.getPrepaidFeeRate() != null) {
            icu.setPrepaidFeeRate(treatyInfo.getPrepaidFeeRate());
        }
        icu.setFloatingHandlingFeeCap(treatyInfo.getFloatingHandlingFeeCap());

        BigDecimal netFeeRate;
        BigDecimal invRate = BigDecimal.ZERO;

        if (treatyInfo.getPrepaidFeeRate() == null || BigDecimal.ZERO.compareTo(treatyInfo.getPrepaidFeeRate()) == 0) {
            netFeeRate = treatyInfo.getFixedFeeRate() == null ? BigDecimal.ZERO : treatyInfo.getFixedFeeRate();
        } else {
            netFeeRate = treatyInfo.getPrepaidFeeRate();
        }

        if (treatyInfo.getFloatingHandlingFeeCap() != null && treatyInfo.getPrepaidFeeRate() != null) {
            invRate = round(treatyInfo.getFloatingHandlingFeeCap().subtract(treatyInfo.getPrepaidFeeRate()));
        }

        // 创建R表结构并保存原始维度数据
        AtrBussToLrcTUlR atrBussToLrcTUlR = ClassUtil.convert(icu, AtrBussToLrcTUlR.class);
        atrBussToLrcTUlR.setActionNo(actionNo);
        
        // 底单起终保日期
        Date expiryDate = icu.getExpiryDate();
        Date effectiveDate = icu.getEffectiveDate();
        Date maxYearMonth = Dates.max(Dates.toDate(icu.getBussYearMonth()), effectiveDate);
        // 合约的起终保日期
        Date riEffectiveDate = icu.getRiEffectiveDate();
        Date riExpiryDate = icu.getRiExpiryDate();
        // 存在利率的月份
        int effectiveMonth = Dates.monthsBetween(maxYearMonth, expiryDate) + 1;
        int totalDays = Dates.daysBetween(effectiveDate, expiryDate) + 1;
        // 合约失效时间
        String riExpiryDateStr = Dates.toChar(riExpiryDate, "yyyyMM");
        BigDecimal riCedingRate = icu.getRiCedingRate();
        // 手续费率
        atrBussToLrcTUlR.setFeeRate(netFeeRate);
        // 存在EPI的月份
        int treatyMonth = Dates.monthsBetween(riEffectiveDate, riExpiryDate) + 1;
        int treatyEffectiveMonths = Dates.monthsBetween(yearMonth, riExpiryDateStr) + 1;
        int maxPatterDev = Math.max(1, effectiveMonth);
        int maxDev = Math.max(maxPatterDev, maxPatterDev + treatyEffectiveMonths - 1);
        // 存储计算过程中的利率
        Map<Integer, BigDecimal> edRateMap = new HashMap<>();
        int offsetPatter = 0;
        
        // 计算已赚比例
        for (int i = 0; i < maxPatterDev; i++) {
            String edYearMonth = Dates.getYearMonth(icu.getBussYearMonth(), i);
            if (edYearMonth.compareTo(yearMonth) < 0) {
                offsetPatter++;
            }
            // 第0期计算起保日期到最晚实收年月的日期的利率作为第0期
            int daysed = 0;
            if (i == 0) {
                // 最晚实收月份最后一天、终保日期取较小者
                Date minDate = Dates.min(Dates.lastDay(icu.getBussYearMonth()), icu.getExpiryDate());
                daysed = Dates.daysBetween(effectiveDate, minDate) + 1;
            } else {
                // 对于非第0期，计算该月的天数
                String tempYearMonth = Dates.toChar(maxYearMonth, "yyyyMM");
                String monthYearMonth = Dates.getYearMonth(tempYearMonth, i);

                // 获取当月第一天和最后一天
                Date monthFirstDay = Dates.toDate(monthYearMonth + "01");
                Date monthLastDay = Dates.lastDay(monthYearMonth);

                // 如果当月第一天早于保单生效日期，则使用保单生效日期作为起始日
                Date startDate = Dates.max(effectiveDate, monthFirstDay);

                // 如果当月最后一天晚于保单终保日期，则使用保单终保日期作为结束日
                Date endDate = Dates.min(monthLastDay, expiryDate);

                // 计算当月已赚天数（如果开始日期晚于结束日期，则天数为0）
                if (startDate != null && !startDate.after(endDate)) {
                    daysed = Dates.daysBetween(startDate, endDate) + 1;
                }
            }
            BigDecimal daysDecimal = BigDecimal.valueOf(daysed);
            BigDecimal totalDaysDecimal = BigDecimal.valueOf(totalDays);
            BigDecimal edRate = totalDaysDecimal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    roundR(daysDecimal.divide(totalDaysDecimal, 15, RoundingMode.HALF_UP));
            edRateMap.put(i, edRate);
        }

        BigDecimal estPremium = BigDecimal.ZERO;
        if (treatyEffectiveMonths > 0) {
            estPremium = treatyInfo.getEstPremium().multiply(riCedingRate)
                    .divide(BigDecimal.valueOf(treatyMonth), 8, RoundingMode.HALF_UP);
        }
        
        BigDecimal recvPremiumSum = BigDecimal.ZERO;
        List<AtrBussToLrcTUlRDev> devRList = new ArrayList<>();   // 原始维度表发展期
        
        for (int i = 0; i < maxDev; i++) {
            AtrBussToLrcTUlRDev devR = new AtrBussToLrcTUlRDev(); // 创建R表的发展期对象
            devR.setDevNo(i);
            
            if (i > 0 && i < treatyEffectiveMonths) {
                devR.setRecvPremium(estPremium);
                devR.setNetFee(round(estPremium.multiply(netFeeRate)));
            }
            
            if (i == 0) {
                devR.setRecvPremium(icu.getCurPaidPremium());
                devR.setNetFee(icu.getCurNetFee());
            }

            if (i < maxPatterDev) {
                BigDecimal edRate = edRateMap.getOrDefault(i + offsetPatter, BigDecimal.ZERO);
                
                devR.setEdRate(edRate);
                devR.setPaidEdPremium(round(icu.getPremium().multiply(edRate)));
                devR.setPaidNetFee(round(icu.getNetFee().multiply(edRate)));
                devR.setEdPremium(round(icu.getPremium().multiply(edRate)));
                devR.setEdNetFee(round(icu.getNetFee().multiply(edRate)));
            }
            
            // 第0期计算投资成分
            if (i == 0) {
                atrBussToLrcTUlR.setInvAmount(round(nvl(devR.getPaidEdPremium()).multiply(invRate)));
            }
            
            recvPremiumSum = recvPremiumSum.add(nvl(devR.getRecvPremium()));
            devRList.add(devR);
        }
        
        // EPI维度
        for (int i = 1; i < treatyEffectiveMonths; i++) {
            // 利率维度
            for (int j = 0; j < maxPatterDev; j++) {
                // 处理R表数据
                BigDecimal recvPremiumR = nvl(devRList.get(i + j).getEdPremium());
                BigDecimal netFeeR = nvl(devRList.get(i + j).getEdNetFee());
                devRList.get(i + j).setEdPremium(devRList.get(i).getRecvPremium().multiply(edRateMap.get(j)).add(recvPremiumR));
                devRList.get(i + j).setEdNetFee(devRList.get(i).getNetFee().multiply(edRateMap.get(j)).add(netFeeR));
            }
        }

        // 统计当期实收+EPI
        calcIcgPremium(icu.getPortfolioNo(), icu.getIcgNo(), icu.getRiskClassCode(), icu.getContractDate(), recvPremiumSum);
        
        // 当期已赚实收 - R表
        atrBussToLrcTUlR.setCurEdPremium(devRList.isEmpty() ? BigDecimal.ZERO : devRList.get(0).getEdPremium());
        atrBussToLrcTUlR.setCurEdNetFee(devRList.isEmpty() ? BigDecimal.ZERO : devRList.get(0).getEdNetFee());

        // 计算总保费 - R表
        BigDecimal premiumR = devRList.stream()
                .map(dev -> nvl(dev.getRecvPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        atrBussToLrcTUlR.setPremium(premiumR);
        
        // 计算总净额结算 - R表
        BigDecimal netFeeR = devRList.stream()
                .map(dev -> nvl(dev.getNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        atrBussToLrcTUlR.setNetFee(netFeeR);
        
        // 存储详细维度的数据
        storeRawData(atrBussToLrcTUlR, devRList);
        
        // 汇总到不带reinsurerCode和sectionoCode维度
        collectIcu(atrBussToLrcTUlR, devRList);
    }

    // 修改：直接插入详细维度的数据，而不是存储到索引中
    private void storeRawData(AtrBussToLrcTUlR icu, List<AtrBussToLrcTUlRDev> devList) {
        // 为R表生成ID，使用专用的R表ID生成器
        long newId = rTableIdGen.incrementAndGet();
        icu.setId(newId);
        
        // 直接插入R表数据
        abp.insert(icu);
        
        // 处理发展期数据
        for (AtrBussToLrcTUlRDev dev : devList) {
            dev.setMainId(newId);
            // 直接插入发展期数据
            abp.insert(dev);
        }
    }



    // 修改collectIcu方法，移除collectIcg调用
    private void collectIcu(AtrBussToLrcTUlR icuR, List<AtrBussToLrcTUlRDev> devRList) {
        // 不带 reinsurerCode 的维度键
        List<Object> unitKey = createUnitKey(icuR);
        
        // 已存在的汇总记录
        AtrBussToLrcTUl existingIcu = atrToLrcUls.one(unitKey);
        
        // 只获取一次上期数据，因为是按treatyNo、policyNo、endorseSeqNo和kindCode维度获取的
        AtrBussToLrcTUl preToLrcUl;
        BigDecimal preCumlEdPremium = BigDecimal.ZERO;
        BigDecimal preCumlEdNetFee = BigDecimal.ZERO;
        BigDecimal preCumlPaidPremium = BigDecimal.ZERO;
        BigDecimal preCumlPaidNetFee = BigDecimal.ZERO;
        
        if (existingIcu != null) {
            // 已存在相同维度的记录，只汇总当期的值，不汇总上期的值
            
            // 汇总当期实收保费
            existingIcu.setCurPremium(nvl(existingIcu.getCurPremium()).add(nvl(icuR.getCurPaidPremium())));
            // 汇总当期净额结算手续费
            existingIcu.setCurNetFee(nvl(existingIcu.getCurNetFee()).add(nvl(icuR.getCurNetFee())));
            
            // 汇总当期已赚保费
            existingIcu.setCurEdPremium(nvl(existingIcu.getCurEdPremium()).add(nvl(icuR.getCurEdPremium())));
            // 汇总当期已赚手续费
            existingIcu.setCurEdNetFee(nvl(existingIcu.getCurEdNetFee()).add(nvl(icuR.getCurEdNetFee())));

            // 汇总投资成分
            existingIcu.setInvAmount(nvl(existingIcu.getInvAmount()).add(nvl(icuR.getInvAmount())));
            // 汇总总保费
            existingIcu.setPremium(nvl(existingIcu.getPremium()).add(nvl(icuR.getPremium())));
            // 汇总总净额结算
            existingIcu.setNetFee(nvl(existingIcu.getNetFee()).add(nvl(icuR.getNetFee())));
            
            // 对 devRList 中的发展期数据进行汇总
            for (AtrBussToLrcTUlRDev devR : devRList) {
                // 创建发展期的维度键：treatyNo、policyNo、endorseSeqNo、kindCode、devNo
                List<Object> devKey = new ArrayList<>(unitKey);
                devKey.add(devR.getDevNo());
                
                // 查找已存在的发展期数据
                AtrBussToLrcTUlDev existingDev = atrToLrcUlDevs.one(devKey);
                
                if (existingDev != null) {
                    // 汇总发展期数据
                    existingDev.setEdPremium(round(nvl(existingDev.getEdPremium()).add(nvl(devR.getEdPremium()))));
                    existingDev.setEdNetFee(round(nvl(existingDev.getEdNetFee()).add(nvl(devR.getEdNetFee()))));
                    existingDev.setRecvPremium(round(nvl(existingDev.getRecvPremium()).add(nvl(devR.getRecvPremium()))));
                    existingDev.setNetFee(round(nvl(existingDev.getNetFee()).add(nvl(devR.getNetFee()))));
                    existingDev.setPaidEdPremium(round(nvl(existingDev.getPaidEdPremium()).add(nvl(devR.getPaidEdPremium()))));
                    existingDev.setPaidNetFee(round(nvl(existingDev.getPaidNetFee()).add(nvl(devR.getPaidNetFee()))));
                } else {
                    // 创建新的发展期数据
                    AtrBussToLrcTUlDev newDev = new AtrBussToLrcTUlDev();
                    newDev.setMainId(existingIcu.getId());
                    newDev.setDevNo(devR.getDevNo());
                    newDev.setEdPremium(nvl(devR.getEdPremium()));
                    newDev.setEdNetFee(nvl(devR.getEdNetFee()));
                    newDev.setRecvPremium(nvl(devR.getRecvPremium()));
                    newDev.setNetFee(nvl(devR.getNetFee()));
                    newDev.setPaidEdPremium(nvl(devR.getPaidEdPremium()));
                    newDev.setPaidNetFee(nvl(devR.getPaidNetFee()));
                    newDev.setEdRate(devR.getEdRate());
                    
                    // 添加到索引中
                    atrToLrcUlDevs.add(devKey, newDev);
                }
            }
        } else {
            long newId = icpMainIdGen.incrementAndGet();
            AtrBussToLrcTUl newIcu = new AtrBussToLrcTUl();

            ClassUtil.copyProperties(icuR, newIcu);
            newIcu.setId(newId);
            newIcu.setActionNo(actionNo);
            preToLrcUl = preToLrcUls.one(unitKey);
            if (preToLrcUl != null) {
                preCumlEdPremium = nvl(preToLrcUl.getPreCumlEdPremium())
                        .add(nvl(preToLrcUl.getCurEdPremium()));
                preCumlEdNetFee = nvl(preToLrcUl.getPreCumlEdNetFee())
                        .add(nvl(preToLrcUl.getCurEdNetFee()));
                preCumlPaidPremium = nvl(preToLrcUl.getPreCumlPaidPremium())
                        .add(nvl(preToLrcUl.getCurPremium()));
                preCumlPaidNetFee = nvl(preToLrcUl.getPreCumlPaidNetFee())
                        .add(nvl(preToLrcUl.getCurNetFee()));
            }
            newIcu.setCurPremium(icuR.getCurPaidPremium());
            newIcu.setPreCumlEdPremium(preCumlEdPremium);
            newIcu.setPreCumlEdNetFee(preCumlEdNetFee);
            newIcu.setPreCumlPaidNetFee(preCumlPaidNetFee);
            newIcu.setPreCumlPaidPremium(preCumlPaidPremium);
            newIcu.setPremium(preCumlPaidPremium.add(icuR.getPremium()));
            newIcu.setNetFee(preCumlPaidNetFee.add(icuR.getNetFee()));

            // 添加到索引中
            atrToLrcUls.add(unitKey, newIcu);

            for (AtrBussToLrcTUlRDev devR : devRList) {
                List<Object> devKey = new ArrayList<>(unitKey);
                devKey.add(devR.getDevNo());

                AtrBussToLrcTUlDev newDev = new AtrBussToLrcTUlDev();
                newDev.setMainId(newId);
                newDev.setDevNo(devR.getDevNo());
                newDev.setEdPremium(nvl(devR.getEdPremium()));
                newDev.setEdNetFee(nvl(devR.getEdNetFee()));
                newDev.setRecvPremium(nvl(devR.getRecvPremium()));
                newDev.setNetFee(nvl(devR.getNetFee()));
                newDev.setPaidEdPremium(nvl(devR.getPaidEdPremium()));
                newDev.setPaidNetFee(nvl(devR.getPaidNetFee()));
                newDev.setEdRate(devR.getEdRate());

                atrToLrcUlDevs.add(devKey, newDev);
            }
        }
    }


    private void collectIcg(AtrBussToLrcTUl atrBussToLrcTUl, List<AtrBussToLrcTUlDev> atrBussToLrcTUlDev) {
        AtrBussLrcToIcg atrBussLrcToIcg = atrBussLrcToIcgHashMap.get(atrBussToLrcTUl.getIcgNo());
        if (atrBussLrcToIcg != null) {
            atrBussLrcToIcg.setTotalPremium(round(atrBussLrcToIcg.getTotalPremium().add(atrBussToLrcTUl.getPremium())));
            atrBussLrcToIcg.setTotalNetFee(round(atrBussLrcToIcg.getTotalNetFee().add(atrBussToLrcTUl.getNetFee())));
            // 直接累加已计算好的投资成分
            atrBussLrcToIcg.setInvAmount(round(nvl(atrBussLrcToIcg.getInvAmount()).add(nvl(atrBussToLrcTUl.getInvAmount()))));
            
            Map<Integer, BigDecimal> devEdPremiumMap = atrBussLrcToIcg.getDevEdPremiumMap();
            Map<Integer, BigDecimal> devNetFeeMap = atrBussLrcToIcg.getDevEdNetFeeMap();
            Map<Integer, BigDecimal> devRecvPremiumMap = atrBussLrcToIcg.getDevRecvPremiumMap();
            Map<Integer, BigDecimal> devNetFeeCfMap = atrBussLrcToIcg.getDevNetFeeCfMap();
            atrBussToLrcTUlDev.forEach(item -> {
                devEdPremiumMap.merge(item.getDevNo(), nvl(item.getEdPremium()), (a, b) -> round(a.add(b)));
                devNetFeeMap.merge(item.getDevNo(), nvl(item.getEdNetFee()), (a, b) -> round(a.add(b)));
                devRecvPremiumMap.merge(item.getDevNo(), nvl(item.getRecvPremium()), (a, b) -> round(a.add(b)));
                devNetFeeCfMap.merge(item.getDevNo(), nvl(item.getNetFee()), (a, b) -> round(a.add(b)));
            });
        } else {
            atrBussLrcToIcg = ClassUtil.convert(atrBussToLrcTUl, AtrBussLrcToIcg.class);
            Map<Integer, BigDecimal> devEdPremiumMap = new HashMap<>();
            Map<Integer, BigDecimal> devNetFeeMap = new HashMap<>();
            Map<Integer, BigDecimal> devRecvPremiumMap = new HashMap<>();
            Map<Integer, BigDecimal> devNetFeeCfMap = new HashMap<>();
            atrBussToLrcTUlDev.forEach(item -> {
                devEdPremiumMap.merge(item.getDevNo(), nvl(item.getEdPremium()), (a, b) -> round(a.add(b)));
                devNetFeeMap.merge(item.getDevNo(), nvl(item.getEdNetFee()), (a, b) -> round(a.add(b)));
                devRecvPremiumMap.merge(item.getDevNo(), nvl(item.getRecvPremium()), (a, b) -> round(a.add(b)));
                devNetFeeCfMap.merge(item.getDevNo(), nvl(item.getNetFee()), (a, b) -> round(a.add(b)));
            });
            atrBussLrcToIcg.setTotalPremium(round(atrBussToLrcTUl.getPremium()));
            atrBussLrcToIcg.setTotalNetFee(round(atrBussToLrcTUl.getNetFee()));
            // 直接使用已计算好的投资成分
            atrBussLrcToIcg.setInvAmount(round(atrBussToLrcTUl.getInvAmount()));
            
            atrBussLrcToIcg.setDevEdPremiumMap(devEdPremiumMap);
            atrBussLrcToIcg.setDevEdNetFeeMap(devNetFeeMap);
            atrBussLrcToIcg.setDevRecvPremiumMap(devRecvPremiumMap);
            atrBussLrcToIcg.setDevNetFeeCfMap(devNetFeeCfMap);
            atrBussLrcToIcgHashMap.put(atrBussLrcToIcg.getIcgNo(), atrBussLrcToIcg);
        }
    }


    private void calcIcg(AtrBussLrcToIcg vo) {
        long mainId = icgMainIdGen.incrementAndGet();

        Map<Integer, BigDecimal> devEdPremiumMap = vo.getDevEdPremiumMap();
        Map<Integer, BigDecimal> devRecvPremiumMap = vo.getDevRecvPremiumMap();
        Map<Integer, BigDecimal> devEdNetFeeMap = vo.getDevEdNetFeeMap();
        Map<Integer, BigDecimal> devNetFeeCfMap = vo.getDevNetFeeCfMap();
        // 初始化发展期
        List<AtrBussLrcToIcgDev> devVos = new ArrayList<>();
        /*vo.setTotalPremium(devRecvPremiumMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setTotalNetFee(devNetFeeCfMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add));*/

        // 已赚汇总
        devEdPremiumMap.forEach((k, v) -> {
            AtrBussLrcToIcgDev devVo = new AtrBussLrcToIcgDev();
            devVos.add(devVo);
            devVo.setMainId(mainId);
            devVo.setDevNo(k);
            // 已赚保费
            devVo.setEdPremium(v);
            // 设置已赚净额结算
            devVo.setEdNetFee(devEdNetFeeMap.getOrDefault(k, BigDecimal.ZERO));
        });
        BigDecimal sumEdPremium = round(devVos.stream()
                .map(AtrBussLrcToIcgDev::getEdPremium)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        // 已赚比例
        devVos.forEach(item -> {
            if (sumEdPremium.compareTo(BigDecimal.ZERO) > 0) {
                item.setEdRatio(round(item.getEdPremium().divide(sumEdPremium, 8, RoundingMode.HALF_UP)));
            } else {
                item.setEdRatio(BigDecimal.ZERO);
            }
        });

        // recv / net_fee
        for (AtrBussLrcToIcgDev devVo : devVos) {
            Integer devNo = devVo.getDevNo();
            devVo.setRecvPremium(round(devRecvPremiumMap.get(devNo)));
            devVo.setNetFee(round(devNetFeeCfMap.get(devNo)));
        }
        vo.setBusinessType("TO");
        vo.setId(mainId);
        abp.insert(vo);

        devVos.forEach(abp::insert);
    }

    private void collectData() {
        logDebug("initMainIdGen");
        initMainIdGen();
        logDebug("collectionTreatyConf");
        // 采集合约相关的信息，如合约的EPI数据
        collectionTreatyConf();
        // 采集当前评估期需要计算的底单数据
        partitionBaseData();
        logDebug("collectionBase");
        // 采集保单信息
        collectionPolicyInfo();
        logDebug("collectionPrePolicyInfo");
        collectionPrePolicyInfo();
        // 获取上一期的旧保费
        logDebug("preIcgPremiumIndex");
        collectionPreIcgPremiumIndex();

    }

    private void collectionPreIcgPremiumIndex() {
        List<AtrBussToLrcIcgPremium> atrBussToLrcIcgPremiums = atrBussLrcToDao.listPreBussToLrcIcgPremium(commonParamMap);
        atrBussToLrcIcgPremiums.forEach(item -> preIcgPremiumIndex.add(Arrays.asList(item.getPortfolioNo(),item.getIcgNo(), item.getRiskClassCode()), item.getOldPremium()));
    }

    // 获取合约的配置
    private void collectionTreatyConf() {
        List<AtrDapTreaty> atrDapTreaties = atrBussLrcToDao.listDapTreaty(yearMonth);
        atrDapTreaties.forEach(item -> {
            if ( item.getSectionoCode() == null || item.getReinsurerCode() == null ) {
                treatyConfIdx.add(Collections.singletonList(item.getTreatyNo()),item);
            }else{
                treatyConfIdx.add(Arrays.asList(item.getTreatyNo(),item.getSectionoCode(), item.getReinsurerCode()), item);
            }
        });
    }


    private void collectionPrePolicyInfo() {
        List<AtrBussToLrcTUl> atrBussToLrcTUls = atrBussLrcToDao.listPreBussToLrcTUl(commonParamMap);
        atrBussToLrcTUls.forEach(item -> {
            List<?> unitKey = createUnitKey(item);
            preToLrcUls.add(unitKey, item);
        });
    }

    private void collectionPolicyInfo() {
        List<AtrBussToLrcTUlR> atrBussToLrcUSES = atrBussLrcToDao.listBasePolicyT();
        // 根据合约来汇总实收保费
        Map<String, BigDecimal> premiumSumByTreatyNo = atrBussToLrcUSES.stream()
                .collect(Collectors.groupingBy(
                        AtrBussToLrcTUlR::getTreatyNo,
                        Collectors.mapping(
                                AtrBussToLrcTUlR::getPremium,
                                Collectors.reducing(BigDecimal.ZERO, (a, b) -> round(a.add(b)))
                        )
                ));
        atrBussToLrcUSES.forEach(item -> {
            item.setRiCedingRate(item.getPremium().divide(premiumSumByTreatyNo.get(item.getTreatyNo()), 15, RoundingMode.HALF_UP));
            treatyPolicyList.add(item);
        });
    }

    private void partitionBaseData() {
        atrBussLrcToDao.truncateBaseDataT();
        // 当前评估期需要计算的数据，范围 评估期大于保单生效时间 且 小于 合约失效时间和底单失效时间中最大的部分
        atrBussLrcToDao.partitionBaseDataT(commonParamMap);
    }

    private void initMainIdGen() {
        icpMainIdGen = new AtomicLong(atrBussLrcToDao.getUltMaxMainId());
        icgMainIdGen = new AtomicLong(atrBussLrcToDao.getIcgMaxMainId());
        rTableIdGen = new AtomicLong(atrBussLrcToDao.getRTableMaxMainId());
    }

    @Override
    protected void initAbp(AsyncBatchProcessor abp) {
        super.initAbp(abp);
        abp.addType(AtrBussToLrcTUl.class);
        abp.addType(AtrBussToLrcTUlDev.class);
        abp.addType(AtrBussToLrcTUlR.class);  // 保留，因为我们直接插入这些数据
        abp.addType(AtrBussToLrcTUlRDev.class); // 保留，因为我们直接插入这些数据
        abp.addType(AtrBussLrcToIcg.class);
        abp.addType(AtrBussLrcToIcgDev.class);
        abp.addType(AtrBussToLrcIcgPremium.class);
        abp.addType(AtrBussToTransitionEdPremium.class);
    }


    private List<Object> createUnitKey(Object vo) {
        List<Object> key = new ArrayList<>();
        key.add(EcfUtil.readField(vo, "treatyNo"));
        key.add(EcfUtil.readField(vo, "policyNo"));
        key.add(EcfUtil.readField(vo, "endorseSeqNo"));
        key.add(EcfUtil.readField(vo, "kindCode"));
        return key;
    }

    /**
     * 计算过渡期已赚保费
     *
     * @param yearMonth 评估期（格式：yyyyMM）
     * @param entityId  机构ID
     */
    public void calculateTransitionEdPremium(String yearMonth, Long entityId) {
        // 初始化环境参数
        String actionNo = EcfUtil.createActionNo();
        initEnvParams(actionNo, entityId, yearMonth, "TO");

        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            // 初始化批处理器
            abp.addType(AtrBussToTransitionEdPremium.class);

            // 设置分区数量
            final int parts = 20; // 分区数量

            // 获取评估期开始和结束日期
            DateTime evDateBom = new DateTime(Dates.toDate(yearMonth + "01"));
            DateTime evDateEom = evDateBom.dayOfMonth().withMaximumValue();
            Date evStartDate = evDateBom.toDate();
            Date evEndDate = evDateEom.toDate();
            int daysInMonth = evDateEom.getDayOfMonth(); // 当月天数

            // 清空临时表
            atrBussLrcToDao.truncateTransitionTempTable();

            // 构建插入临时表所需参数
            Map<String, Object> tempDataParams = new HashMap<>(commonParamMap);
            tempDataParams.put("parts", parts);
            tempDataParams.put("evStartDate", evStartDate);

            // 插入临时表数据
            atrBussLrcToDao.insertTransitionTempData(tempDataParams);

            // 分区计算
            for (int partNo = 0; partNo < parts; partNo++) {
                Map<String, Object> partParams = new HashMap<>(commonParamMap);
                partParams.put("pn", partNo);

                // 获取当前分区的保单数据
                List<AtrBussToLrcTUlR> baseVos = atrBussLrcToDao.getTransitionPartBaseVos(partParams);
                if (baseVos.isEmpty()) {
                    continue; // 跳过空分区
                }

                // 创建线程安全的结果集合
                final List<AtrBussToTransitionEdPremium> resultList = Collections.synchronizedList(new ArrayList<>());

                // 使用并发计算
                ThreadUtil.runThreadsThrow(baseVos, vo -> {
                    // 计算已赚保费
                    AtrBussToTransitionEdPremium edPremium = calculateSingleEdPremium(
                            vo, entityId, yearMonth, evStartDate, evEndDate, daysInMonth);

                    // 添加到线程安全的结果列表
                    if (edPremium != null) {
                        resultList.add(edPremium);
                    }
                }, () -> EcfUtil.THREADS_CALC); // 使用EcfUtil中定义的计算线程数

                // 批量插入当前分区的已赚保费结果
                if (!resultList.isEmpty()) {
                    // 使用AsyncBatchProcessor插入数据
                    resultList.forEach(abp::insert);
                }
            }
            abp.end();
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("计算TO业务过渡期已赚保费异常: " + e.getMessage(), e);
        }
    }

    /**
     * 计算单个保单的已赚保费
     *
     * @param vo          保单数据
     * @param entityId    实体ID
     * @param yearMonth   评估期
     * @param evStartDate 评估期开始日期
     * @param evEndDate   评估期结束日期
     * @param daysInMonth 当月天数
     * @return 已赚保费对象
     */
    private AtrBussToTransitionEdPremium calculateSingleEdPremium(
            AtrBussToLrcTUlR vo, Long entityId, String yearMonth,
            Date evStartDate, Date evEndDate, int daysInMonth) {

        // 获取基本信息
        String treatyNo = vo.getTreatyNo();
        String policyNo = vo.getPolicyNo();
        String endorseSeqNo = vo.getEndorseSeqNo();
        String kindCode = vo.getKindCode();
        BigDecimal premium = nvl(vo.getPremium());
        Date effectiveDate = vo.getEffectiveDate();  // 标的保单起保日期
        Date expiryDate = vo.getExpiryDate();        // 标的保单终保日期

        // 承保总天数
        int totalDays = Dates.daysBetween(effectiveDate, expiryDate) + 1;
        if (totalDays <= 0) {
            return null; // 无效的保单日期，跳过
        }

        // 计算当期已赚天数
        double earnedDays;

        // 情况1: 保险期间完全在评估期内（起保日期和终保日期都在评估期内）
        if (!effectiveDate.before(evStartDate) && !effectiveDate.after(evEndDate) &&
                !expiryDate.before(evStartDate) && !expiryDate.after(evEndDate)) {
            // 计算公式：保险期间总天数 / 保险期间总天数 * premium = premium
            earnedDays = totalDays;
        }
        // 情况2: 起保日期在当前评估期内，终保日期大于当前评估期
        else if (!effectiveDate.before(evStartDate) && !effectiveDate.after(evEndDate) &&
                expiryDate.after(evEndDate)) {
            // 计算公式：（当前评估期的最后一天-起保日期）的天数 / (终保日期-起保日期+1) * premium
            earnedDays = Dates.daysBetween(effectiveDate, evEndDate) + 1;
        }
        // 情况3: 起保日期小于当前评估期，终保日期在当前评估期内
        else if (effectiveDate.before(evStartDate) &&
                !expiryDate.before(evStartDate) && !expiryDate.after(evEndDate)) {
            // 计算公式：（终保日期-当前评估期的第一天+1）/ (终保日期-起保日期+1) * premium
            earnedDays = Dates.daysBetween(evStartDate, expiryDate) + 1;
        }
        // 情况4: 起保日期小于当前评估期且终保日期大于当前评估期（跨期保单）
        else if (effectiveDate.before(evStartDate) && expiryDate.after(evEndDate)) {
            // 计算公式：（当前评估期的最后一天-当前评估期的第一天+1）/ (终保日期-起保日期+1) * premium
            earnedDays = daysInMonth;
        }
        // 情况5: 保险期间完全在评估期外
        else {
            // 没有已赚部分
            return null;
        }

        // 计算已赚保费比例
        BigDecimal edRate = earnedDays <= 0 ? BigDecimal.ZERO :
                round(BigDecimal.valueOf(earnedDays).divide(BigDecimal.valueOf(totalDays), 15, RoundingMode.HALF_UP));

        // 计算已赚保费
        BigDecimal edPremium = round(premium.multiply(edRate));

        // 创建已赚保费对象
        AtrBussToTransitionEdPremium result = new AtrBussToTransitionEdPremium();
        result.setEntityId(entityId);
        result.setTreatyNo(treatyNo);
        result.setPolicyNo(policyNo);
        result.setEndorseSeqNo(endorseSeqNo);
        result.setKindCode(kindCode);
        result.setYearMonth(yearMonth);
        result.setEdPremium(edPremium);

        return result;
    }

    /**
     * 备份并清理历史保单数据
     *
     * <p>会备份并清理atr_dap_to_paid_t表中符合条件的数据。
     * 只有当一个treaty_no下的所有policy_no都在清理列表中时，
     * 才会删除该treaty_no下的所有数据。
     *
     * @param entityId 实体ID
     */
    public void backupAndCleanHistoricalData(Long entityId) {
        try {
            log.info("开始备份和清理TO业务历史数据，机构ID: {}", entityId);

            // 创建备份表
            atrBussLrcToDao.createPaidBackupTable();

            // 删除数据
            atrBussLrcToDao.deletePaidData();

            log.info("TO业务历史数据备份和清理完成");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("备份和清理TO业务历史数据异常: " + e.getMessage(), e);
        }
    }

    /**
     * 判断过渡期已赚保费表中是否存在指定机构的数据
     *
     * @param entityId 机构ID
     * @return 存在返回true，不存在返回false
     */
    public boolean hasTransitionData(Long entityId) {
        return atrBussLrcToDao.hasTransitionData(entityId);
    }

    /**
     * 处理各种金额的尾差
     *
     * @param ulDevs       发展期列表
     * @param totalPremium 总保费
     * @param totalNetFee  总净额结算手续费
     * @param preEdPremium 上期累计已赚保费
     * @param preEdNetFee  上期累计已赚净额结算手续费
     */
    private void handleAmountTailDifference(List<AtrBussToLrcTUlDev> ulDevs,
                                            BigDecimal totalPremium, BigDecimal totalNetFee,
                                            BigDecimal preEdPremium, BigDecimal preEdNetFee) {
        // 已赚保费尾差处理
        AtrBussToLrcTUlDev targetDevVo = null;
        BigDecimal maxEdPremium = BigDecimal.ZERO;


        for (AtrBussToLrcTUlDev dev : ulDevs) {
            if (dev.getEdPremium() != null && dev.getEdPremium().compareTo(BigDecimal.ZERO) != 0) {
                if (targetDevVo == null || dev.getEdPremium().compareTo(maxEdPremium) > 0) {
                    targetDevVo = dev;
                    maxEdPremium = dev.getEdPremium();
                }
            }
        }
        BigDecimal edPremiumSum = round(ulDevs.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        if (targetDevVo != null && totalPremium != null) {
            BigDecimal gap = round(totalPremium.subtract(preEdPremium).subtract(edPremiumSum));
            targetDevVo.setEdPremium(round(targetDevVo.getEdPremium().add(gap)));
        }

        // 净额结算手续费尾差处理
        AtrBussToLrcTUlDev targetNetFeeDevVo = null;
        BigDecimal maxEdNetFee = BigDecimal.ZERO;
        for (AtrBussToLrcTUlDev dev : ulDevs) {
            if (dev.getEdNetFee() != null && dev.getEdNetFee().compareTo(BigDecimal.ZERO) != 0) {
                if (targetNetFeeDevVo == null || dev.getEdNetFee().compareTo(maxEdNetFee) > 0) {
                    targetNetFeeDevVo = dev;
                    maxEdNetFee = dev.getEdNetFee();
                }
            }
        }
        BigDecimal edNetFeeSum = round(ulDevs.stream()
                .map(dev -> nvl(dev.getEdNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        if (targetNetFeeDevVo != null && totalNetFee != null) {
            BigDecimal netFeeGap = round(totalNetFee.subtract(preEdNetFee).subtract(edNetFeeSum));
            targetNetFeeDevVo.setEdNetFee(round(targetNetFeeDevVo.getEdNetFee().add(netFeeGap)));
        }
    }

    /**
     * 在所有数据汇总完成后，处理汇总数据的尾差并插入数据库
     */
    private void insertAggregatedData() {
        // 遍历所有汇总后的数据
        for (List<AtrBussToLrcTUl> icuList : atrToLrcUls.getMap().values()) {
            for (AtrBussToLrcTUl icu : icuList) {
                // 获取该 ICU 对应的所有发展期数据
                List<Object> unitKey = createUnitKey(icu);
                List<AtrBussToLrcTUlDev> devList = new ArrayList<>();
                
                // 从发展期索引中收集对应的发展期数据
                for (Map.Entry<List<?>, List<AtrBussToLrcTUlDev>> entry : atrToLrcUlDevs.getMap().entrySet()) {
                    List<?> key = entry.getKey();
                    // 检查前缀是否匹配（忽略最后一个元素，即devNo）
                    if (key.size() > unitKey.size() && key.subList(0, unitKey.size()).equals(unitKey)) {
                        devList.addAll(entry.getValue());
                    }
                }
                
                if (!devList.isEmpty()) {
                    collectIcg(icu, devList);
                    
                    BigDecimal totalPremium = icu.getPremium();
                    BigDecimal totalNetFee = icu.getNetFee();
                    // 处理尾差（针对汇总后的数据）
                    handleAmountTailDifference(devList, totalPremium, totalNetFee,
                            nvl(icu.getPreCumlEdPremium()),
                            nvl(icu.getPreCumlEdNetFee()));

                    validateAggregatedData(icu, devList);
                    
                    // 通过校验，插入数据库
                    abp.insert(icu);
                    devList.forEach(abp::insert);
                }
            }
        }
    }

    /**
     * 校验汇总数据的一致性
     * 使用相同维度的数据进行校验
     */
    private void validateAggregatedData(AtrBussToLrcTUl icu, List<AtrBussToLrcTUlDev> devList) {
        // 构建记录标识信息
        String recordInfo = String.format("合约号=%s, 保单号=%s, 批单序号=%s, 险别代码=%s",
                icu.getTreatyNo(), icu.getPolicyNo(), icu.getEndorseSeqNo(), icu.getKindCode());

        // 1. 校验已赚保费
        // 计算发展期已赚保费总和
        BigDecimal edPremiumSum = round(devList.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        // 上期已赚保费
        BigDecimal preEdPremium = nvl(icu.getPreCumlEdPremium());

        // 验证保费一致性
        if (icu.getPremium()
                .compareTo(round(preEdPremium.add(edPremiumSum))) != 0) {
            throw new RuntimeException(String.format("保费不一致，总保费=%s, 累计已赚保费=%s, 记录信息: %s",
                    icu.getPremium(), round(preEdPremium.add(edPremiumSum)), recordInfo));
        }

        // 2. 校验净额结算
        // 计算已赚净额结算总和
        BigDecimal edNetFeeSum = round(devList.stream()
                .map(dev -> nvl(dev.getEdNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        // 上期已赚净额结算
        BigDecimal preEdNetFee = nvl(icu.getPreCumlEdNetFee());

        // 验证净额结算一致性
        if (icu.getNetFee()
                .compareTo(round(preEdNetFee.add(edNetFeeSum))) != 0) {
            throw new RuntimeException(String.format("净额结算不一致，总净额结算=%s, 累计已赚净额结算=%s, 记录信息: %s",
                    icu.getNetFee(), round(preEdNetFee.add(edNetFeeSum)), recordInfo));
        }
    }
}
