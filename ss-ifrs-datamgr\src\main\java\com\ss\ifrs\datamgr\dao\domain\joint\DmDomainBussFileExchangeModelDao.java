package com.ss.ifrs.datamgr.dao.domain.joint;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelConditionVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelStatResultVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DmDomainBussFileExchangeModelDao {

    Integer getRowCount(String bizCode, String taskCode);

    Integer updateTaskStatus(String bizCode, String taskCode, String taskStatus, String dealMsg);

    Integer updateVerifyResult(DapModelStatResultVo dapModelStatResultVo, DmBussDataVerifyVo dmBussDataVerifyVo);

    List<String> getValidTaskCode(String bizCode,String yearMonth);


}
