package com.ss.ifrs.datamgr.domain.service.impl.bussperiod;

import com.ss.ifrs.datamgr.dao.domain.buss.DmDomainBussPeriodDetailDao;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodDetailService;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodDetailVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodDetailService;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.exception.BusinessException;
import com.ss.platform.core.exception.ResponseEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class DmDomainPeriodDetailServiceImpl implements DmDomainPeriodDetailService {
    @Autowired
    public DmDomainBussPeriodService domainBussPeriodService;
    @Autowired
    public DmDomainBussPeriodDetailDao domainBussPeriodDetailDao;
    Long userId = 1L;
    @Autowired
    private DmConfBussPeriodDetailService confBussPeriodDetailService;

    @Autowired
    private DmConfBussPeriodService confBussPeriodService;

    @Override
    public void addByConfTable(Long entityId, String yearMonth) {
        domainBussPeriodDetailDao.addByConfTable(entityId, yearMonth);
    }

    @Override
    public void update(BussPeriodReqVo bussPeriodReqVo) {
        DmConfBussPeriodVo bussPeriodVo = new DmConfBussPeriodVo();
        bussPeriodVo.setEntityId(bussPeriodReqVo.getEntityId());
        bussPeriodVo.setYearMonth(bussPeriodReqVo.getYearMonth());
        bussPeriodVo.setPeriodState(bussPeriodReqVo.getPeriodState());
        bussPeriodVo = confBussPeriodService.findByVo(bussPeriodVo);

        if (ObjectUtils.isEmpty(bussPeriodVo)) {
            throw new BusinessException(ResponseEnum.ERROR_PERIOD_NOT_EXIST);
        }

        DmConfBussPeriodDetailVo detailVo = new DmConfBussPeriodDetailVo();
        detailVo.setBussPeriodId(bussPeriodVo.getBussPeriodId());
        detailVo.setBizCode(bussPeriodReqVo.getBizCode());
        detailVo.setPeriodState(bussPeriodReqVo.getPeriodState());
        detailVo.setReadyState(bussPeriodReqVo.getReadyState());
        detailVo.setDirection(bussPeriodReqVo.getDirection());
        detailVo.setExecResult("success");
        detailVo.setUpdatorId(this.userId);
        detailVo.setUpdateTime(new Date());
        confBussPeriodDetailService.updateDetail(detailVo);
    }

    @Override
    public Boolean checkExistsPreparingInput(Long entityId, String yearMonth) {
        return checkExistsPreparing(entityId, yearMonth, CommonConstant.BussPeriod.DetailBizDirection.INPUT);
    }

    @Override
    public Boolean checkExistsPreparingOutput(Long entityId, String yearMonth) {
        return checkExistsPreparing(entityId, yearMonth, CommonConstant.BussPeriod.DetailBizDirection.OUTPUT);
    }

    @Override
    public List<String> listPreparingInputModel(Long entityId,String yearMonth) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.INPUT);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.PREPARING);
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setYearMonth(yearMonth);
        return domainBussPeriodDetailDao.listBussPeriodDetailInfo(bussPeriodReqVo);
    }

    private Boolean checkExistsPreparing(Long entityId, String yearMonth, String direction) {
        BussPeriodReqVo confBussPeriodDetailVo = new BussPeriodReqVo();
        confBussPeriodDetailVo.setEntityId(entityId);
        confBussPeriodDetailVo.setYearMonth(yearMonth);
        confBussPeriodDetailVo.setDirection(direction);
        confBussPeriodDetailVo.setReadyState(CommonConstant.BussPeriod.PeriodStatus.PREPARING);
        // 业务期间己准备，所有输入数据己准备
        List<String> confPeriodDetailIdCount = domainBussPeriodDetailDao.listBussPeriodDetailInfo(confBussPeriodDetailVo);
        //有待准备数据
        return confPeriodDetailIdCount.size() > 0;
    }

}
