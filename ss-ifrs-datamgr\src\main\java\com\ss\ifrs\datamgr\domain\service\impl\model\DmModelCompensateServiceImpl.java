package com.ss.ifrs.datamgr.domain.service.impl.model;

import com.ss.ifrs.datamgr.dao.domain.model.DmModelCompensateDao;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainDataVerifyService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.model.vo.DmModelCompensateDataVo;
import com.ss.ifrs.datamgr.domain.service.model.DmModelCompensateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DmModelCompensateServiceImpl implements DmModelCompensateService {

    @Autowired
    DmModelCompensateDao dmModelCompensateDao;


    private DmDomainDataVerifyService dmDomainDataVerifyService;

    @Autowired
    public void setDmDomainDataVerifyService(DmDomainDataVerifyService dmDomainDataVerifyService) {
        this.dmDomainDataVerifyService = dmDomainDataVerifyService;
    }

    @Override
    public String getMappingCondition(Long bizTypeId) {
        Long modelCompensateData = dmModelCompensateDao.getModelCompensateData();
        if (modelCompensateData > 0) {
             return String.format(" and exists (select 1 from dm_model_compensate_data where t.policy_no = policy_no and t.task_code = task_code and biz_type_id = %d"
                    , bizTypeId);
        }else {
            return "";
        }
    }

    @Override
    public void insertNeedCompensatePolicy(DmModelCompensateDataVo dmBussDataCompensateVo) {
        // 查找需要补偿的模型
        List<DmModelCompensateDataVo> initCompensateVerify = dmModelCompensateDao.getInitCompensateVerify();
        initCompensateVerify.forEach(item -> {
            dmBussDataCompensateVo.setBizTypeId(item.getBizTypeId());
            dmBussDataCompensateVo.setBizCode(item.getBizCode());
            dmModelCompensateDao.insertNeedCompensatePolicy(dmBussDataCompensateVo);
        });
    }

    @Override
    public Long getNeedCompensatePolicySize() {
        return dmModelCompensateDao.getNeedCompensatePolicySize();
    }

    @Override
    public void insertLogCompensatePolicy(String taskCode) {
        dmModelCompensateDao.insertLogCompensatePolicy(taskCode);
        dmModelCompensateDao.deleteAllNeedCompensatePolicy();
    }

    @Override
    public void getModelCompensateData(DmModelCompensateDataVo dmBussDataCompensateVo) {
        // 查找需要补偿的模型
        List<DmModelCompensateDataVo> initCompensateVerify = dmModelCompensateDao.getInitCompensateVerify();
        initCompensateVerify.forEach(item -> {
            dmBussDataCompensateVo.setBizTypeId(item.getBizTypeId());
            dmBussDataCompensateVo.setBizCode(item.getBizCode());
            dmModelCompensateDao.insertModelCompensateData(dmBussDataCompensateVo);
        });
    }

    @Override
    public void compensateDataVerify(DmBussDataVerifyVo dmBussDataVerifyVo) {
        List<DmModelCompensateDataVo> compensateDataVos = dmModelCompensateDao.listCompensateData();
        compensateDataVos.forEach(item -> {
            String yearMonth = item.getTaskCode().substring(8, 15);
            // 满足条件才需要重新校验
            if (dmBussDataVerifyVo.getYearMonth().compareTo(yearMonth) > 0) {
                dmBussDataVerifyVo.setBizTypeId(item.getBizTypeId());
                dmBussDataVerifyVo.setBizCode(item.getBizCode());
                dmBussDataVerifyVo.setPushModel(item.getBizCode());
                dmDomainDataVerifyService.dataVerifyDeal(dmBussDataVerifyVo);
            }
        });
        // 删除需要补偿的数据
        dmModelCompensateDao.deleteAllModelCompensateData();

    }


}
