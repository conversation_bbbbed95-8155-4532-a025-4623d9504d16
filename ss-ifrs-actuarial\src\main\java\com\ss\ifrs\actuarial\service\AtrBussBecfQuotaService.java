package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussQuotaValueVo;

import java.util.List;
import java.util.Map;

public interface AtrBussBecfQuotaService {

    //根据评估id查询计量业务假设数据
    Map<String, Object> findBussQuotaData(AtrBussQuotaValueVo atrBussQuotaValueVo);

    Map<String, Object> findBussQuotaDataDetail(AtrBussQuotaValueVo atrBussQuotaValueVo);

    List<AtrConfBecfOutPutVo> findBecfOutPut(AtrBussQuotaValueVo atrBussQuotaValueVo);

    List<AtrConfBecfOutPutVo> findBecfListByIds(AtrBussBecfViewVo atrBussBecfViewVo);
}
