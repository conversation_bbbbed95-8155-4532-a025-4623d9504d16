package com.ss.ifrs.datamgr.dao.domain.conf;

import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfCheckRuleVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfigValidPolicyRuleVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelConditionVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface DmDomainConfCheckRuleDao {

    List<DmConfCheckRuleVo> getValidCheckRules(DmBussDataVerifyVo dmBussDataVerifyVo,List<String> taskCodes);

    List<String> getValidRuleTypes(Long bizTypeId);

    // 查询有效单规则
    List<DmConfigValidPolicyRuleVo> listValidPolicyRule(String taskCode);


}
