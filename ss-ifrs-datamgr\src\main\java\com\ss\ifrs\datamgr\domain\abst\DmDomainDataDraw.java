package com.ss.ifrs.datamgr.domain.abst;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.joint.DmDomainDataDrawDao;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainDataVerifyService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodDetailService;
import com.ss.ifrs.datamgr.domain.service.joint.DmDomainDataPushSignalService;
import com.ss.ifrs.datamgr.domain.service.push.DmDomainDataPushService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmTaskCodeAdapterVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDrawColumnParseVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmOdsDataPushSignalVo;
import com.ss.ifrs.datamgr.pojo.push.DataPushAccVo;
import com.ss.ifrs.datamgr.service.push.DmBussDataPushAccService;
import com.ss.library.utils.StringUtil;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
public abstract class DmDomainDataDraw {

    @Autowired
    DmDomainDataDrawDao dmDomainDataDrawDao;
    @Autowired
    DmDomainDataPushSignalService dmDomainDataPushSignalService;
    @Autowired
    DmDomainDataVerifyService dmDomainDataVerifyService;
    @Autowired
    BbsConfEntityFeignClient bbsConfEntityFeignClient;
    @Autowired
    DmDomainDataPushService dmDomainDataPushService;

    @Autowired
    private DmDomainPeriodDetailService dmDomainPeriodDetailService;

    public String getTaskCode(DmTaskCodeAdapterVo dmTaskCodeAdapterVo) {
        StringBuffer taskCode = new StringBuffer("DM");
        taskCode.append(dmTaskCodeAdapterVo.getDrawType());
        BbsConfEntityVo entityVo = bbsConfEntityFeignClient.findByEntityId(dmTaskCodeAdapterVo.getEntityId());
        taskCode.append(StringUtil.lpad(entityVo.getEntityCode(), 4, "0"));
        taskCode.append(dmTaskCodeAdapterVo.getYearMonth());
        if (StringUtil.isNotEmpty(dmTaskCodeAdapterVo.getDay())) {
            taskCode.append(StringUtil.lpad(dmTaskCodeAdapterVo.getDay(), 2, "0"));
        } else {
            taskCode.append("00");
        }
        String maxTaskCode = dmDomainDataPushSignalService.getMaxTaskCode(taskCode.toString());
        if (StringUtil.isEmpty(maxTaskCode)) {
            taskCode.append("001");
        } else {
            String seqNo = StringUtil.substring(maxTaskCode, maxTaskCode.length() - 3);
            int seq = Integer.valueOf(seqNo).intValue();
            taskCode.append(StringUtil.lpad(String.valueOf(seq + 1), 3, "0"));
        }
        return taskCode.toString();
    }

    public void saveDrawData(List<List<DmDrawColumnParseVo>> lists, String tableName) {
        dmDomainDataDrawDao.saveDrawData(lists, tableName);
    }

    public List<DmDrawColumnParseVo> createDefaultValue(String taskCode, Long userId, String seqName) throws ParseException {
        List<DmDrawColumnParseVo> createList = new ArrayList<>();
        //createList.add(new DmDrawColumnParseVo("ID", "NUMERIC", "11", "1", seqName + ".nextval"));
        createList.add(new DmDrawColumnParseVo("DRAW_TIME", "DATE", "6", "1", new Date()));
        createList.add(new DmDrawColumnParseVo("DRAW_TYPE", "VARCHAR2", "6", "1", "3"));
        createList.add(new DmDrawColumnParseVo("TASK_STATUS", "VARCHAR2", "11", "1", "0"));
        createList.add(new DmDrawColumnParseVo("TASK_CODE", "VARCHAR2", "20", "1", taskCode));
        createList.add(new DmDrawColumnParseVo("DRAW_USER", "NUMERIC", "11", "1", userId));
        return createList;
    }

    /**
     * 单线程数据校验
     * 业务数据校验
     */
    protected void queueDataVerify(DmDataDrawVo dmDataDrawVo) {
        List<DmOdsDataPushSignalVo> dataPushSignalVo = dmDomainDataPushSignalService.findTodoList(dmDataDrawVo);
        List<String> bizCodes = dmDomainPeriodDetailService.listPreparingInputModel(dmDataDrawVo.getEntityId()
                , dmDataDrawVo.getYearMonth());

        dataPushSignalVo.stream()
                .sorted(Comparator.comparing(DmOdsDataPushSignalVo::getTypeGroup)
                        .thenComparing(DmOdsDataPushSignalVo::getDisplayNo)
                        .thenComparing(DmOdsDataPushSignalVo::getTaskCode))
                .collect(Collectors.toList())
                .forEach(vo -> {
                    if (bizCodes.contains(vo.getPushModel())) {
                        DmBussDataVerifyVo dmBussDataVerifyVo = new DmBussDataVerifyVo();
                        dmBussDataVerifyVo.setEntityId(dmDataDrawVo.getEntityId());
                        dmBussDataVerifyVo.setTaskCode(vo.getTaskCode());
                        dmBussDataVerifyVo.setPushModel(vo.getPushModel());
                        dmBussDataVerifyVo.setUserId(dmDataDrawVo.getCreatorId());
                        dmBussDataVerifyVo.setDrawType(dmDataDrawVo.getDrawType());
                        dmBussDataVerifyVo.setBizTypeId(vo.getBizTypeId());
                        dmBussDataVerifyVo.setBizCode(vo.getPushModel());
                        dmDataDrawVo.setTaskCode(vo.getTaskCode());
                        if (StringUtil.isNotEmpty(vo.getTaskCode())) {
                            dmBussDataVerifyVo.setYearMonth(vo.getTaskCode().substring(7, 13));
                        }
                        try {
                            dmDomainDataVerifyService.dataVerify(dmBussDataVerifyVo);
                        } catch (Exception ex) {
                            log.error("{}:{}", vo.getTaskCode() + "-" + vo.getPushModel(), ex.getLocalizedMessage(), ex);
                            throw new RuntimeException(ex);
                        }
                    }
                });
        // 会计平台数据按日推送，每次校验完成后立刻推送到会计
        DataPushAccVo dataPushAccVo = new DataPushAccVo();
        dataPushAccVo.setTaskCode(dmDataDrawVo.getTaskCode());
        dataPushAccVo.setYearMonth(dmDataDrawVo.getYearMonth());
        dataPushAccVo.setUserId(dmDataDrawVo.getCreatorId());
        dataPushAccVo.setEntityId(dmDataDrawVo.getEntityId());
        dmDomainDataPushService.accDataPushDiary(dataPushAccVo);
    }

    /**
     * 多线程校验
     * 基础数据校验, 模型间有依赖，不能用多线程处理
     */
    protected void threadDataVerify(DmDataDrawVo dmDataDrawVo) {
        List<DmOdsDataPushSignalVo> dataPushSignalVo = dmDomainDataPushSignalService.findTodoList(dmDataDrawVo);
        List<String> bizCodes = dataPushSignalVo.stream()
                .sorted(Comparator.comparing(DmOdsDataPushSignalVo::getTypeGroup).thenComparing(DmOdsDataPushSignalVo::getDisplayNo))
                .map(DmOdsDataPushSignalVo::getPushModel)
                .distinct()
                .collect(Collectors.toList());

        ExecutorService executor = Executors.newFixedThreadPool(bizCodes.size());//区分不同模型各自校验
        bizCodes.stream().<Runnable>map(bizCode -> () -> {
            dataPushSignalVo.stream()
                    .filter(v -> (bizCode.equals(v.getPushModel())))
                    .sorted(Comparator.comparing(DmOdsDataPushSignalVo::getTaskCode))
                    .collect(Collectors.toList())
                    .forEach(vo -> {
                        DmBussDataVerifyVo dmBussDataVerifyVo = new DmBussDataVerifyVo();
                        dmBussDataVerifyVo.setEntityId(dmDataDrawVo.getEntityId());
                        dmBussDataVerifyVo.setTaskCode(vo.getTaskCode());
                        dmBussDataVerifyVo.setPushModel(vo.getPushModel());
                        dmBussDataVerifyVo.setUserId(dmDataDrawVo.getCreatorId());
                        dmBussDataVerifyVo.setDrawType(DmConstant.DrawType.ETL);
                        dmDomainDataVerifyService.dataVerify(dmBussDataVerifyVo);
                    });
            // 提交任务并执行
        }).forEach(executor::submit);
    }
}
