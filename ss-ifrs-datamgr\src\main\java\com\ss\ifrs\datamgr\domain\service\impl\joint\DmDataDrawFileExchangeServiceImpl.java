package com.ss.ifrs.datamgr.domain.service.impl.joint;

import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.conf.AppFilePathConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableColumnDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableDao;
import com.ss.ifrs.datamgr.dao.joint.DmBussFileExchangeCtlDao;
import com.ss.ifrs.datamgr.dao.joint.DmBussFileExchangeModelDao;
import com.ss.ifrs.datamgr.dao.joint.DmBussFileExchangeSignalDao;
import com.ss.ifrs.datamgr.dao.joint.OdsDataPushSignalDao;
import com.ss.ifrs.datamgr.dao.log.DmLogFileExchangeFileLineDao;
import com.ss.ifrs.datamgr.dao.log.DmLogFileExchangeTraceDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainDataDraw;
import com.ss.ifrs.datamgr.domain.service.joint.DmDataDrawFileExchangeService;
import com.ss.ifrs.datamgr.domain.service.joint.DmDomainFileChangeSignalService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfControl;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfTable;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfDrawTypeVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableColumnVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.pojo.joint.po.*;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmOdsDataPushSignalVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.MakeSqlResultVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.FileContentVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.SignalModelLineVo;
import com.ss.ifrs.datamgr.service.conf.DmConfControlService;
import com.ss.ifrs.datamgr.service.conf.DmConfDrawTypeService;
import com.ss.ifrs.datamgr.util.FilePathUtils;
import com.ss.library.utils.CacheUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.core.exception.BusinessException;
import com.ss.platform.util.CommonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Scope("prototype")
@Slf4j
public class DmDataDrawFileExchangeServiceImpl extends DmDomainDataDraw implements DmDataDrawFileExchangeService {
    private static final String[] FIXED_COLUMNS = {"draw_time", "draw_type", "task_status", "task_code", "draw_user", "check_msg"};

    /**
     * 存储 action_no,  一个 action_no 指代一次处理目标日期操作
     */
    private static String actionNo;

    /**
     * 存储列的默认值配置
     */
    private Map<ColDefaultValConfVo, ColDefaultValConfVo> defaultColValConfMap;

    /**
     * 存储行日志
     */
    private List<DmLogFileExchangeFileLine> lineLogs;
    private final Object lineLogsLock = new Object();

    //初始数据存放路径
    private String publicPath;

    //私有路径，存放复制的文件数据
    private String privatePath;

    //待处理的文件路径
    private String pendingHandleFilePath;

    private static long entityId;

    private static long userId;

    /**
     * 配置的模型总数
     */
    private int bizConfigTotal;

    private final Map<String, List<DmConfTableColumnVo>> columnConfsCache = new HashMap<>();

    /**
     * 模型的主键字段在行数据中的位置
     */
    private final Map<String, int[]> modelPkIndexCache = new HashMap<>();

    private final Map<String, Long> bizMappingCache = new HashMap<>();

    private static final String LOG_NODE_CTL = "CTL";
    private static final String LOG_NODE_TBD = "TBD";
    private static final String LOG_NODE_SIGNAL = "SIGNAL";
    private static final String LOG_NODE_MODEL = "MODEL";
    private static final String LOG_NODE_OTHER = "OTHER";

    @Resource
    private NamedParameterJdbcTemplate npjt;

    @Resource
    private DmBussFileExchangeCtlDao dmBussFileExchangeCtlDao;

    @Resource
    private DmBussFileExchangeModelDao dmBussFileExchangeModelDao;

    @Resource
    private DmBussFileExchangeSignalDao dmBussFileExchangeSignalDao;

    @Resource
    private DmLogFileExchangeFileLineDao dmLogFileExchangeFileLineDao;

    @Resource
    private AppFilePathConfig appFilePathConfig;

    @Resource
    private DmLogFileExchangeTraceDao dmLogFileExchangeTraceDao;

    @Resource
    private DmConfTableColumnDao dmConfTableColumnDao;

    @Resource
    private DmDomainFileChangeSignalService dmDomainFileChangeSignalService;

    @Resource
    private DmConfTableDao dmConfTableDao;

    @Resource
    private OdsDataPushSignalDao odsDataPushSignalDao;

    @Resource
    private DmConfControlService dmConfControlService;

    @Resource
    DmConfDrawTypeService dmConfDrawTypeService;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void dataDrawAction(Long entityId, String yearMonth, Long userId) {
        //增加数据对接总控与文件交换控制开关，如未开启，返回不处理
//        if (fileExchangeDrawFlag()) {
//            return;
//        }
        actionNo = CommonUtil.nextActionNo();
        //根据业务年月查询待处理的文件交换控制信息
        DmBussFileExchangeCtl pendingCtl = getPendingCtl(yearMonth);
        if (pendingCtl == null) {
            return;
        }
        //初始化
        DmDataDrawFileExchangeServiceImpl.entityId = entityId == null ? 1L : entityId;//废弃
        DmDataDrawFileExchangeServiceImpl.userId = (userId == null ? 1L : userId);//废弃

        pendingCtl.setEntityId(DmDataDrawFileExchangeServiceImpl.entityId);
        //执行文件交换数据入库
        DmDataDrawFileExchangeServiceImpl dmDataDrawFileExchangeService = applicationContext.getBean(DmDataDrawFileExchangeServiceImpl.class);
        dmDataDrawFileExchangeService.fileExchangeDataAnalysis(pendingCtl);

        //数据校验
        dmDataDrawFileExchangeService.dataVerify(pendingCtl);


        //任务结束后的处理
        doTaskAfter(pendingCtl);
    }

    /**
     * 文件交换数据解析
     * @param pendingCtl
     */
    @TrackDataMgrProcess(DmConstant.ProcCode.DM_EXTRACT_DATA)
    public void fileExchangeDataAnalysis(DmBussFileExchangeCtl pendingCtl){
        long startTime = System.currentTimeMillis();
        log.info("文件交换数据解析任务开始:{}", startTime);


        //todo 按模型配置获取模型结构并缓存
        testQueryAndCacheColDefaultValConfs();
        Date curBussDate;
        Date lastBussDate = null;
        init();

        //任务处理
        while (true) {

            lineLogs = new ArrayList<>();

            //TODO 统一用ODS信号表，并判官是否有在处理中：
            /*
             * 判断同模型同一任务号：
             *1、已有在处理中，直接退出不处理；
             * 2、无处理中，继续处理。
             */
            //获取当前处理的年月日时间
            curBussDate = pendingCtl.getBussDate();
            //
            if (lastBussDate != null && !curBussDate.after(lastBussDate)) {
                break;
            }
            lastBussDate = curBussDate;

            //拼装待处理年月的路径
            //1.路径：xxxx/yyyyMMdd/ eg:ifrs_source_data/20120131/
            //this.srcDir = joinPaths(publicPath, DateFormatUtils.format(curBussDate, "yyyyMMdd"));
            //2.路径：xxxx/yyyy/yyyyMM/ eg:ifrs_source_data/2012/201201/
            this.pendingHandleFilePath = joinPaths(publicPath, DateFormatUtils.format(curBussDate, "yyyyMMdd").substring(0, 4), DateFormatUtils.format(curBussDate, "yyyyMMdd").substring(0, 6));
            //执行待处理日期的文件交换任务
            executePendingBussDateFileExchangeTask(pendingCtl);
        }

        long endTime = System.currentTimeMillis();
        long elapsedTime = endTime - startTime;
        log.info("文件交换数据解析任务历时{}毫秒结束", elapsedTime);
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_VERIFY_DATA)
    public void dataVerify(DmBussFileExchangeCtl pendingCtl){
        List<String> taskCodeList = odsDataPushSignalDao.getTaskCodeByYearMonthAndStatusAndDrawType(pendingCtl.getYearMonth()
                , "0", DmConstant.DrawType.FILE_EXCHANGE);
        for(String taskCode : taskCodeList){
            DmDataDrawVo dmDataDrawVo = new DmDataDrawVo();
            dmDataDrawVo.setEntityId(pendingCtl.getEntityId());
            dmDataDrawVo.setYearMonth(pendingCtl.getYearMonth());
            dmDataDrawVo.setCreatorId(userId);
            dmDataDrawVo.setTaskCode(taskCode);
            dmDataDrawVo.setDrawType(DmConstant.DrawType.FILE_EXCHANGE);
            queueDataVerify(dmDataDrawVo);
        }
    }
    private Boolean fileExchangeDrawFlag() {
        DmConfControl dmConfControl = dmConfControlService.searchConfigControl();
        DmConfDrawTypeVo dataByType = dmConfDrawTypeService.findDataByType(DmConstant.DrawType.FILE_EXCHANGE);
        if (dmConfControl.getOpenedIs().equals("0") || dataByType.getOpenedIs().equals("0")) {
            log.info("文件交换任务未开启");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 初始化方法
     */
    private void init() {
        //初始化相关路径
        initRootPath();

        //初始化平台生效模型数量
        DmConfTable confTable = new DmConfTable();
        confTable.setValidIs("1");
        bizConfigTotal = dmConfTableDao.count(confTable).intValue();
    }

    /**
     * 初始化路径
     */
    private void initRootPath() {
        //this.publicPath = joinPaths(FilePathUtils.getBasePath(appFilePathConfig), appFilePathConfig.getFileExchangePath());
        //绝对路径，数据文件所在路径
        this.publicPath = FilePathUtils.getFileExchangePath(appFilePathConfig);
        //私有路径，后续需将数据复制到私有路径下存储
        this.privatePath = joinPaths(FilePathUtils.getBasePath(appFilePathConfig), FilePathUtils.getDataFilePathTextImportPath(appFilePathConfig) + FilePathUtils.getBussPathFileExchangePathPrivate(appFilePathConfig));
        log.info("File Exchange Public Path = {}", publicPath);
        log.info("File Exchange Private Path = {}", privatePath);

        if (StringUtils.isBlank(publicPath)) {
            throw new BusinessException("NoFileExchangePublicPath", "The file exchange public path does not exist");
        }
        if (!new File(publicPath).isDirectory()) {
            throw new BusinessException("NoFileExchangePublicFolder", "The file exchange public path is not a directory");
        }
        if (!new File(publicPath).canWrite() || !new File(publicPath).canRead()) {
            throw new BusinessException("FileExchangePublicFoldersNoPermissions", "File exchange public path does not have read or write permissions");
        }
        if (StringUtils.isBlank(privatePath)) {
            throw new BusinessException("NoFileExchangePrivatePath", "The file exchange private path does not exist");
        }
        if (!new File(privatePath).isDirectory()) {
            throw new BusinessException("NoFileExchangePrivateFolder", "The file exchange private path is not a directory");
        }
        if (!new File(privatePath).canWrite() || !new File(privatePath).canRead()) {
            throw new BusinessException("FileExchangePrivateFoldersNoPermissions", "File exchange private path does not have read or write permissions");
        }
    }

    /**
     * 获取当前待处理的文件交换控制信息
     *
     * @param curYearMonth
     * @return
     */
    private DmBussFileExchangeCtl getPendingCtl(String curYearMonth) {
        //添加文件交换执行记录
        addTrace(LOG_NODE_CTL, "INFO", null, "Get processing date for " + curYearMonth);
        //查询当前业务日期的待执行的文件交换控制信息
        List<DmBussFileExchangeCtl> pendingCtlInfo = dmBussFileExchangeCtlDao.queryPending(curYearMonth);
        if (pendingCtlInfo.isEmpty()) {
            addTrace(LOG_NODE_CTL, "INFO", null,
                    "No processing date available for %s", curYearMonth);
            return null;
        }
        //待处理的控制数据有且只能仅有一个
        return pendingCtlInfo.get(0);
    }

    /**
     * 执行待处理业务时间的文件交换控制任务
     *
     * @param ctl
     */
    private void executePendingBussDateFileExchangeTask(DmBussFileExchangeCtl ctl) {
        addTrace(LOG_NODE_TBD, "INFO", null,
                "Processing target business date %s", ctl.getBussDate());
        //修改文件交换控制信息状态为处理中
        updateDmBussFileExchangeCtl(ctl, "1", null);
        try {
            doTargetDir(ctl);
        } catch (Exception e) {
            updateDmBussFileExchangeCtl(ctl, "3", getExceptionMsg(e));
            throw e;
        }
    }

    /**
     * 处理路径下的信号文件夹
     */
    private void doTargetDir(DmBussFileExchangeCtl ctl) {
        //获取路径下文件
        File dir = new File(pendingHandleFilePath);

        if (!dir.exists()) {
            String errorMsg = String.format("Directory %s has not been created", dir.getName());
            addTrace(LOG_NODE_SIGNAL, "INFO", ctl.getYearMonth(), errorMsg);
            updateDmBussFileExchangeCtl(ctl, "3", errorMsg);
            return;
        }

        if (!dir.canRead() || !dir.canWrite()) {
            String errorMsg = String.format("The target directory %s is not operational", dir.getName());
            addTrace(LOG_NODE_SIGNAL, "INFO", ctl.getYearMonth(), errorMsg);
            updateDmBussFileExchangeCtl(ctl, "3", errorMsg);
            return;
        }

        if (Objects.requireNonNull(dir.listFiles()).length < 1) {
            String errorMsg = String.format("There is no signal folder under the path %s", dir.getName());
            addTrace(LOG_NODE_SIGNAL, "INFO", ctl.getYearMonth(), errorMsg);
            updateDmBussFileExchangeCtl(ctl, "3", errorMsg);
            return;
        }

        String curBussDate = DateFormatUtils.format(ctl.getBussDate(), "yyyyMMdd");
        // 获取信号文件夹列表
        File[] fileList = dir.listFiles();
        assert fileList != null;
        //过滤文件夹,去除非文件夹文件,仅留批次文件夹
        List<File> signalFileList = new ArrayList<>();
        //正则过滤目标文件夹（001~999）
        for (File file : fileList) {
            if (file.isDirectory() && file.getName().matches("00[1-9]|0[1-9][0-9]|[1-9][0-9][0-9]")) {
                signalFileList.add(file);
            }
        }

        if (signalFileList.size() < 1) {
            String errorMsg = String.format("There is no signal folder under the path %s", dir.getName());
            addTrace(LOG_NODE_SIGNAL, "INFO", ctl.getYearMonth(), errorMsg);
            updateDmBussFileExchangeCtl(ctl, "3", errorMsg);
            return;
        }

        //循环处理信号文件夹
        for (int i = 0; i < signalFileList.size(); i++) {
            File file = signalFileList.get(i);
            //信号文件名称
            String signalFileName = "IFRS17_" + curBussDate + "_" + file.getName() + ".ok";
            //信号文件父级路径 yyyy/yyyyMM/001/
            String signalFileParentFolderPath = StringUtil.joinPaths(this.pendingHandleFilePath, file.getName());

            // 处理信号文件
            handleSignalFile(ctl, signalFileName, i + 1 == signalFileList.size(), signalFileParentFolderPath);
        }
    }

    /**
     * 处理信号文件
     *
     * @param ctl                        文件交换控制信息
     * @param signalFileName             信号文件名称
     * @param isLastSignalFile           是否是最后一个信号文件
     * @param signalFileParentFolderPath 信号文件所属的文件夹路径
     */
    private void handleSignalFile(DmBussFileExchangeCtl ctl, String signalFileName, Boolean isLastSignalFile, String signalFileParentFolderPath) {
        //信号文件路径，带信号文件名称
        String signalFilePath = StringUtil.joinPaths(signalFileParentFolderPath, signalFileName);
        File signalFile = new File(signalFilePath);
        if (!signalFile.exists()) {
            String errorMessage = "There are no signal files to process under " + signalFileParentFolderPath;
            addTrace(LOG_NODE_SIGNAL, "INFO", null, errorMessage);
            updateDmBussFileExchangeCtl(ctl, "3", errorMessage);
            return;
        }
        //查询是否已经处理过信号文件
        if (dmBussFileExchangeSignalDao.countBySignalFileName(signalFile.getName()) > 0) {
            addTrace(LOG_NODE_SIGNAL, "INFO",
                    "The signal file \"%s\" has been processed", signalFile.getName());
            if (isLastSignalFile) {
                updateDmBussFileExchangeCtl(ctl, "3", "There are no latest batch signal files to be processed");
            }
            return;
        }
        //未处理的信号文件进行新增信息
        DmBussFileExchangeSignal signal = addSignal(ctl, signalFile.getName());
        try {
            doSignal(ctl, signal, signalFileParentFolderPath);
        } catch (Exception e) {
            log.error("处理信号" + signalFileName + "失败", e);
            signal.setStatus("3");
            signal.setErrorMsg(getExceptionMsg(e));
            dmBussFileExchangeSignalDao.updateAllById(signal);
        }
    }

    /**
     * 操作信号文件
     *
     * @param ctl
     * @param signal
     * @param signalFileParentFolderPath
     */
    private void doSignal(DmBussFileExchangeCtl ctl, DmBussFileExchangeSignal signal, String signalFileParentFolderPath) {
        //开始处理信号文件，修改状态为处理中
        updateSignal(signal, "1", null);
        //信号文件的全路径
        String signalFilePath = joinPaths(signalFileParentFolderPath, signal.getSignalFileName());
        //复制信号文件到private的目录下
        File signalFile = copyToPrivateDir(new File(signalFilePath),false, DateFormatUtils.format(ctl.getBussDate(), "yyyyMMdd"));
        //读取信号文件内容
        FileContentVo signalFileContentInfo = readFileContent(signalFile.getAbsolutePath());
        //信号文件内容行数
        long lineCount = signalFileContentInfo.getLineCount();
        //信号文件内容文本
        List<String> signalFileLines = signalFileContentInfo.getContents();

        if (null == signalFileLines) {
            signalFileLines = new ArrayList<>();
        } else {
            signalFileLines = trimEnd(signalFileLines);
        }

        // 模型对应的行序号
        Map<String, Integer> modelLineIndexMap = new HashMap<>();

        for (int xx = 0; xx < 1; xx++) {
            if (lineCount <= -1) {
                String msg = format("Signal file %s read failure.", signalFilePath);
                addTrace(LOG_NODE_SIGNAL, "ERROR", null, msg);
                updateSignal(signal, "3", msg);
                break;
            }

            if (lineCount == 0) {
                String msg = format("The content of signal file %s is empty.", signalFilePath);
                addTrace(LOG_NODE_SIGNAL, "ERROR", null, msg);
                updateSignal(signal, "3", msg);
                break;
            }

            if (lineCount == 1) {
                String msg = format("The model list in the signal file %s is empty.", signalFilePath);
                addTrace(LOG_NODE_SIGNAL, "ERROR", null, msg);
                updateSignal(signal, "3", msg);
                break;
            }

            // 从第一行内容获取 task_code
            String taskCode = null;
            String yearMonth = null;
            String signalFileLine1 = signalFileLines.get(0);
            if (null != signalFileLine1) {
                String[] ss = signalFileLine1.split("\\|");
                if (ss.length == 2) {
                    taskCode = ss[0];
                    yearMonth = ss[1];
                    signal.setTaskCode(taskCode);
                    dmBussFileExchangeSignalDao.updateById(signal);
                }
            }

            if (taskCode == null) {
                updateSignal(signal, "3", "Unable to resolve task code.");
                break;
            }
            if (yearMonth == null) {
                updateSignal(signal, "3", "Unable to resolve business period.");
                break;
            }
            if (!ctl.getYearMonth().equals(yearMonth)) {
                updateSignal(signal, "3", "The business period of the signal file is not within the specified time.");
                break;
            }

            // 检验信号文件的模型列表是否异常
            List<String> appendixErrors = new ArrayList<>();
            //第二行开始为模型列表
            for (int i = 1; i < signalFileLines.size(); i++) {
                String modelLine = signalFileLines.get(i);
                String[] ss = modelLine.split("\\|", -1);
                if (ss.length != 3) {
                    appendixErrors.add(format("Line %s has the wrong number of fields.", i + 1));
                } else {
                    String bizCode = ss[1];
                    if (getAndCacheBizTypeId(bizCode) == null) {
                        appendixErrors.add(format("The model code at line %s is wrong.", i + 1));
                    } else {
                        Integer old = modelLineIndexMap.put(bizCode, i);
                        if (old != null) {
                            appendixErrors.add(format("The model code on line %s is duplicated.", i + 1));
                        }
                    }
                }
            }

//            if (modelLineIndexMap.size() != bizConfigTotal) {
//                String msg = format("The expected number of models is %s, but it is actually %s.",
//                        bizConfigTotal, modelLineIndexMap.size());
//                appendixErrors.add(msg);
//            }

            if (!appendixErrors.isEmpty()) {
                updateSignal(signal, "3", String.join(IOUtils.LINE_SEPARATOR, appendixErrors));
            }

            for (int i = 1; i < signalFileLines.size(); i++) {
                String signalModelLine = signalFileLines.get(i);
                String[] modelLineInfo = signalModelLine.split("\\|", -1);
                SignalModelLineVo modelVo = new SignalModelLineVo();
                modelVo.setFileName(modelLineInfo[0]);
                modelVo.setBizCode(modelLineInfo[1]);
                modelVo.setLineCount(Integer.valueOf(modelLineInfo[2]));
                modelVo.setDirPath(signalFileParentFolderPath);
                modelVo.setFilePath(joinPaths(signalFileParentFolderPath, modelVo.getFileName()));
                modelVo.setDmBussFileExchangeCtl(ctl);
                modelVo.setDmBussFileExchangeSignal(signal);
                modelVo.setSingleId(signal.getSignalId());
                modelVo.setTaskCode(taskCode);
                handleModelFile(modelVo);
            }

            //查询失败的模型列表
            List<DmBussFileExchangeModel> failModels = queryModels(signal.getSignalId(), "3");
            if (!failModels.isEmpty()) {
                List<String> errorModelCodes = failModels.stream()
                        .map(DmBussFileExchangeModel::getBizCode).sorted().collect(Collectors.toList());
                String msg = String.format("Model [%s] failed to process.", String.join("/", errorModelCodes));
                dmBussFileExchangeSignalDao.updateById(signal);
                updateSignal(signal, "3", msg);
            } else if (!signal.getStatus().equals("3")) {
                updateSignal(signal, "9", null);
            }
        }

        // 回写
        writeBackSignalFile(signal.getSignalId(), signalFileLines, modelLineIndexMap, signalFileParentFolderPath, ctl);
    }

    /**
     * 处理模型文件
     *
     * @param signalModelLineVo 信号文件的模型数据信息参数
     */
    private void handleModelFile(SignalModelLineVo signalModelLineVo) {
        String bizCode = signalModelLineVo.getBizCode();
        DmConfTableVo tableInfo = dmConfTableDao.findBizCode(bizCode);
        if (null == tableInfo || tableInfo.getValidIs().equals("0")) {
            return;
        }
        DmBussFileExchangeModel model = addDmBussFileExchangeModel(signalModelLineVo);

        //ODS_PUSH_DATA_SIGNAL表
        this.initOdsDataPushSignal(signalModelLineVo.getTaskCode(),signalModelLineVo.getDmBussFileExchangeCtl().getYearMonth(),bizCode,Long.valueOf(signalModelLineVo.getLineCount()));


        try {
            //设置此模型以往批次的数据的校验开关为关闭
            dmBussFileExchangeModelDao.updateDealFlag(signalModelLineVo.getSingleId(), "1", bizCode);
        } catch (UnexpectedRollbackException e) {
            log.error(e.getMessage());
        }


        if (signalModelLineVo.getLineCount() == 0) {
            return;
        }
        File modelFile = copyToPrivateDir(new File(signalModelLineVo.getFilePath()),false, DateFormatUtils.format(signalModelLineVo.getDmBussFileExchangeCtl().getBussDate(), "yyyyMMdd"));

        if (null == modelFile) {
            String msg = "The model file does not exist";
            addTrace(LOG_NODE_MODEL, "ERROR", bizCode, msg);
            updateDmBussFileExchangeModel(model, "3", "NoModelFile");
            return;
        }

        updateDmBussFileExchangeModel(model, "1", null);

        //获取模型文件内容行数
        long lineCount = readFileLineCount(modelFile);

        if (lineCount <= -1) {
            String msg = "Failed to read model file " + modelFile.toPath();
            addTrace(LOG_NODE_MODEL, "ERROR", bizCode, msg);
            updateDmBussFileExchangeModel(model, "3", "ReadFileFail");
            return;
        } else if (lineCount != signalModelLineVo.getLineCount()) {
            String msg = "Inconsistency between the amount of data in the model ("
                    + bizCode + ") file and the signal file";
            addTrace(LOG_NODE_MODEL, "ERROR", bizCode, msg);
            updateDmBussFileExchangeModel(model, "3", "InconsistentQuantity");
            return;
        } else if (lineCount == 0) {
            String msg = format("The content of file \"%s\" is empty.", modelFile.toPath());
            addTrace(LOG_NODE_MODEL, "INFO", bizCode, msg);
            return;
        }

        model.setRowCount((int) lineCount);

        //读取模型文件内容并进行存储操作
        readModelFileContent(signalModelLineVo, model, modelFile);

        //查询当前模型失败条数
        List<DmLogFileExchangeFileLine> modelFailLines = queryFailLineLog(signalModelLineVo.getSingleId(), bizCode);
        if (!modelFailLines.isEmpty()) {
            //有失败行，设置模型文件状态为处理失败
            updateDmBussFileExchangeModel(model, "3", "HaveFailData");
            //输出失败信息文件
            writeBackModelFail(signalModelLineVo, modelFailLines);
        }else {
            updateDmBussFileExchangeModel(model, "9", null);
        }

    }

    private void initOdsDataPushSignal(String taskCode,String yearMonth,String pushModel,Long rowCount){
        DmOdsDataPushSignalVo dmOdsDataPushSignalVo = new DmOdsDataPushSignalVo();
        dmOdsDataPushSignalVo.setTaskCode(taskCode);
        dmOdsDataPushSignalVo.setPushModel(pushModel);
        dmOdsDataPushSignalVo.setYearMonth(yearMonth);
        dmOdsDataPushSignalVo.setPushTime(new Date());
        dmOdsDataPushSignalVo.setRowCount(rowCount);
        dmOdsDataPushSignalVo.setTaskStatus("0");
        odsDataPushSignalDao.addDataPushSignal(dmOdsDataPushSignalVo);
    }

    /**
     * 读取模型的txt文件内容
     *
     * @param signalModelLineVo 信号文件的模型数据信息
     */
    public void readModelFileContent(SignalModelLineVo signalModelLineVo, DmBussFileExchangeModel dmBussFileExchangeModel, File modelFile) {
        long startTime = System.currentTimeMillis();
        //允许存储的数据列表
        List<Map<String, Object>> arrangeDataList = new ArrayList<>();

        //创建一个拥有8个线程的线程池
        ExecutorService executor = Executors.newFixedThreadPool(8);

        try (BufferedReader br = new BufferedReader(new FileReader(modelFile))) {
            //构造一个BufferedReader类来读取文件
            String s;
            int i = 0;
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            while ((s = br.readLine()) != null && !s.trim().isEmpty()) {//使用readLine方法，一次读一行
                i++;
                //构建每行数据的存储参数
                Map<String, Object> insertParamData = handleModelLine(i, s, signalModelLineVo);
                if (null != insertParamData) {
                    //参数不为空添加到集合
                    arrangeDataList.add(insertParamData);
                    //集合每满200或计数为txt内容的最后一个，进行批量插入
                    if (arrangeDataList.size() >= 200) {
                        //存储数据
                        List<Map<String, Object>> realInsertDataList = arrangeDataList;
//                        futureList.add(CompletableFuture.runAsync(() ->
//                                saveData(signalModelLineVo, realInsertDataList), executor));
                        futureList.add(CompletableFuture.runAsync(() ->
                                handleInsertData(signalModelLineVo.getSingleId(),signalModelLineVo.getBizCode(), realInsertDataList), executor));
                        arrangeDataList = new ArrayList<>();
                    }
                }
            }
            if (!arrangeDataList.isEmpty()) {
                //存储数据
                List<Map<String, Object>> realInsertDataList = arrangeDataList;
//                futureList.add(CompletableFuture.runAsync(() ->
//                        saveData(signalModelLineVo, realInsertDataList), executor));
                futureList.add(CompletableFuture.runAsync(() ->
                        handleInsertData(signalModelLineVo.getSingleId(),signalModelLineVo.getBizCode(), realInsertDataList), executor));
            }

            //插入数据行失败日志
            tryInsertFileLines(true);

            if (!CollectionUtils.isEmpty(futureList)) {
                //阻塞等待所有存储线程结束
                CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
                allOf.get();
            }
            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            log.info("模型={},文件数据存储操作了：{}毫秒,操作行数：{}", signalModelLineVo.getBizCode(), elapsedTime, i);
        } catch (IOException e) {
            log.info("模型={}文件读取失败", signalModelLineVo.getBizCode());
            updateDmBussFileExchangeModel(dmBussFileExchangeModel, "3", "FailReadFile");
        } catch (InterruptedException | ExecutionException e) {
            log.info("线程出现异常,{}", e.getMessage());
        }


    }

    /**
     * 处理模型内容
     *
     * @param lineNo           行号
     * @param modelContentLine 模型文件单行内容
     * @param modelVo          信号文件的模型数据信息
     * @return 模型文件单行内容转换的预插入数据(为Null则为相关校验失败行数据 ， 不进行插入)
     */
    private HashMap<String, Object> handleModelLine(int lineNo, String modelContentLine,
                                                    SignalModelLineVo modelVo) {
        List<DmConfTableColumnVo> dmConfTableColumns = getAndCacheColumnConfs(modelVo.getBizCode());
        HashMap<String, Object> insertParamData = new HashMap<>();

        String[] lineDate = modelContentLine.split("\\|", -1);
        for (int i = 0; i < lineDate.length; i++) {
            // 根据文档将\N转义为空
            if ("\\N".equals(lineDate[i])) {
                lineDate[i] = "";
            }
        }

        //对技术字段进行封装
        insertParamData.put("draw_time", toDate(lineDate[0]));
        insertParamData.put("draw_type", lineDate[1]);
        insertParamData.put("task_status", lineDate[2]);
        insertParamData.put("task_code", lineDate[3]);
        insertParamData.put("draw_user", Integer.parseInt(lineDate[4]));
        insertParamData.put("check_msg", lineDate[5]);

        //截取前六位技术字段后的真实数据
        String[] realSrcData = Arrays.copyOfRange(lineDate, 6, lineDate.length);
        if (dmConfTableColumns.size() != realSrcData.length) {
            //模型字段总数是否与分割后的数据总数不相等，记为一条转换失败数据
            log.info("字段数量与库中不匹配");
            //创建行信息为失败
            addLineFailLog(modelVo.getSingleId(), modelVo.getBizCode(), lineNo, null,
                    "The expected number of fields is " + (dmConfTableColumns.size() + FIXED_COLUMNS.length)
                            + ", but the actual number is " + (realSrcData.length + FIXED_COLUMNS.length));
            return null;
        }

        insertParamData.put("#lineNo", lineNo);
        insertParamData.put("#values", realSrcData);

        //是否组装数据标识
        boolean isMake = true;

        if (!Objects.equals(modelVo.getTaskCode(), lineDate[3])) {
            addLineFailLog(modelVo.getSingleId(), modelVo.getBizCode(), lineNo, realSrcData,
                    "task_code does not match signal file");
            isMake = false;
        }

        //根据数据库中字段进行校验转换
        for (int i = 0; i < dmConfTableColumns.size(); i++) {
            DmConfTableColumnVo colConf = dmConfTableColumns.get(i);
            String colCode = StringUtils.lowerCase(colConf.getColCode());
            String colType = colConf.getColType();
            String value = realSrcData[i];
            if (StringUtils.isEmpty(value)) {
                value = testGetDefaultColValue(modelVo.getBizCode(), colCode);
            }
            if (colConf.getNeedIs().equals("1") && StringUtils.isBlank(value)) {
                //校验必填字段不可为空
                addLineFailLog(modelVo.getSingleId(), modelVo.getBizCode(), lineNo,
                        realSrcData, "The required field " + colCode + " has no value");
                isMake = false;
//                break;
            }
            try {
                if (colType.equals("NUMBER")) {
                    //数字类型转换
                    if (StringUtils.isBlank(value)) {
                        insertParamData.put(colCode, null);
                    } else {
                        insertParamData.put(colCode, new BigDecimal(value));
                    }
                } else if (colType.equals("DATE") || colType.equals("TIMESTAMP")) {
                    //时间类型转换
                    if (StringUtils.isBlank(value)) {
                        insertParamData.put(colCode, null);
                    } else {
                        insertParamData.put(colCode, toDate(value));
                    }
                } else {
                    if (value != null && value.length() > Integer.parseInt(colConf.getColLength())) {
                        //文本超出数据库字符串限定长度，记录异常数据
                        addLineFailLog(modelVo.getSingleId(), modelVo.getBizCode(), lineNo, realSrcData,
                                "The value of field " + colCode
                                        + " exceeds the maximum length " + colConf.getColLength());
                        isMake = false;
                    }
                    insertParamData.put(colCode, value);
                }
            } catch (Exception e) {
                isMake = false;
                log.error("", e);
                addLineFailLog(modelVo.getSingleId(), modelVo.getBizCode(), lineNo, realSrcData,
                        colCode + ":Value [" + value + "] conversion error: " + getExceptionMsg(e));
            }
        }
        if (isMake) {
            return insertParamData;
        }
        return null;
    }

    /**
     * 文件交换任务执行结束后的操作
     * @param ctl
     */
    private void doTaskAfter(DmBussFileExchangeCtl ctl){
        ctl.setEndTime(new Date());
        ctl.setStatus("0");
        ctl.setErrorMsg("incomplete");

        //查询当前执行日期下成功校验的模型
        List<String> successModels = dmBussFileExchangeCtlDao.queryDealSuccessModels(ctl.getBussDate());
        //查询配置生效的模型数量
        int validTableTotal = dmConfTableDao.countByValid();
        if (successModels.size() < 1) {
            //没有成功的模型
            String msg = "The model is incomplete,All Model failed to process.";
            ctl.setErrorMsg(msg);
            dmBussFileExchangeCtlDao.updateById(ctl);
        } else if (successModels.size() < validTableTotal) {
            //有成功模型但数量小于配置生效模型的数量
            List<String> failModelsName = dmConfTableDao.notInBizCode(successModels);
            String msg = String.format("The model is incomplete,Model [%s] failed to process.", String.join("/", failModelsName));
            ctl.setErrorMsg(msg);
            dmBussFileExchangeCtlDao.updateById(ctl);
        } else if (successModels.size() == validTableTotal) {
            //成功模型数量等于配置生效模型数量
            ctl.setStatus("9");
            ctl.setErrorMsg(null);
            dmBussFileExchangeCtlDao.updateAllById(ctl);
            //创建下一个待处理日期相关配置数据
            createNextBussDate(ctl);
        }
    }

    private File copyToPrivateDir(File file, boolean cut, String bussDateStr) {
        String yearPath = bussDateStr.substring(0, 4);
        String parentDirName = bussDateStr.substring(0, 6);
        String batchNo = getBatchNo(file.getName());
        //xxxx/yyyy/yyyyMM/001/
        String newFilePath = joinPaths(privatePath, yearPath, parentDirName, batchNo, file.getName());
        try {
            File fileNew = new File(newFilePath);
            if (!fileNew.getParentFile().exists()) {
                if (!fileNew.getParentFile().mkdirs()) {
                    throw new RuntimeException("Failed to create the target directory");
                }
            }
            FileUtils.copyFile(file, fileNew);
            if (cut) {
                FileUtils.deleteQuietly(file);
            }
            return fileNew;
        } catch (Exception e) {
            log.error("Error copying file");
            return null;
        }
    }

    private String getBatchNo(String fileName){
        int lastIndex = fileName.indexOf('.');
        return fileName.substring(lastIndex - 3, lastIndex);
    }

    private void testQueryAndCacheColDefaultValConfs() {
        String sql = "select * from dm_temp_file_ex_col_default";
        List<ColDefaultValConfVo> voList = npjt.query(sql, Collections.emptyMap(),
                new BeanPropertyRowMapper<>(ColDefaultValConfVo.class));
        Map<ColDefaultValConfVo, ColDefaultValConfVo> map = new HashMap<>();
        for (ColDefaultValConfVo vo : voList) {
            map.put(vo, vo);
        }
        defaultColValConfMap = map;
    }


    public void createNextBussDate(DmBussFileExchangeCtl ctl) {
        if ("9".equals(ctl.getStatus())) {
            Date bussDate = ctl.getBussDate();
//            Date nextDate = new DateTime(bussDate).plusDays(1).toDate();

            try {
                LocalDate currentBussDate = bussDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                if (currentBussDate.lengthOfMonth() == currentBussDate.getDayOfMonth()) {
                    //如果是月份的最后一天 清理缓存
                    CacheUtil.remove(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.DmIdentity.APP_CODE);
                }
                LocalDate nextBussDate = currentBussDate.plusMonths(1).withDayOfMonth(1).with(TemporalAdjusters.lastDayOfMonth());
                String formattedDate = nextBussDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                Date nextDate = new SimpleDateFormat("yyyy-MM-dd").parse(formattedDate);
                DmBussFileExchangeCtl ctlNew = new DmBussFileExchangeCtl();
                ctlNew.setYearMonth(new SimpleDateFormat("yyyyMM").format(nextDate));
                ctlNew.setBussDate(nextDate);

                if (dmBussFileExchangeCtlDao.count(ctlNew) == 0) {
                    ctlNew.setCreateTime(new Date());
                    ctlNew.setStatus("0");
                    dmBussFileExchangeCtlDao.save(ctlNew);
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }


        }
    }

    //字符串拼接
    private String joinPaths(String... paths) {
        return StringUtil.joinPaths(paths);
    }

    /**
     * 增加轨迹日志
     */
    private void addTrace(String logNode, String logType, String bussInfo, String msg, Object... msgArgs) {
        for (int i = 0; i < msgArgs.length; i++) {
            Object msgArg = msgArgs[i];

            // 日期时间显示优化
            if (msgArg instanceof Date) {
                String dateStr = DateFormatUtils.format((Date) msgArg, "yyyy-MM-dd HH:mm:ss.SSS");
                dateStr = dateStr
                        .replaceFirst("\\.000$", "")
                        .replaceFirst("00:00:00$", "");
                msgArgs[i] = dateStr;
            }
        }

        DmLogFileExchangeTrace po = new DmLogFileExchangeTrace();
        po.setBussInfo(bussInfo);
        po.setCreateTime(new Date());
        po.setMsg(String.format(msg, msgArgs));
        po.setLogType(logType);
        po.setLogNode(logNode);
        po.setActionNo(actionNo);
        dmLogFileExchangeTraceDao.save(po);
    }


    private String testGetDefaultColValue(String bizCode, String colCode) {
        Map<ColDefaultValConfVo, ColDefaultValConfVo> map = defaultColValConfMap;
        ColDefaultValConfVo voQ = new ColDefaultValConfVo();
        voQ.setBizCode(bizCode.toUpperCase());
        voQ.setColCode(colCode.toUpperCase());
        ColDefaultValConfVo vo = map.get(voQ);
        if (vo == null) {
            voQ.setBizCode("*");
            voQ.setColCode(colCode.toUpperCase());
            vo = map.get(voQ);
        }
        return vo == null ? null : vo.getValue();
    }

    public void handleInsertData(Long singleId, String modelCode, List<Map<String, Object>> arrangeDataList){
        //封装SQL语句和参数
        List<DmConfTableColumnVo> dmConfTableColumns = getAndCacheColumnConfs(modelCode);
        MakeSqlResultVo makeSqlResultVo = makeInsertSql(modelCode, dmConfTableColumns, arrangeDataList);
        try{
            npjt.update(makeSqlResultVo.getSql(), makeSqlResultVo.getInsertMap());
        }catch (Exception e){
            // 插入失败，判断数据量
            if (arrangeDataList.size() == 1) {
                // 如果数据列表长度为1，说明这个数据是错误的，记录错误
                log.info("数据存储异常,{}",e.getMessage());
                batchAddLineFailLog(singleId, modelCode , arrangeDataList, getExceptionMsg(e));
            } else {
                // 二分法递归分组
                int mid = arrangeDataList.size() / 2;
                List<Map<String, Object>> left = arrangeDataList.subList(0, mid);
                List<Map<String, Object>> right = arrangeDataList.subList(mid, arrangeDataList.size());

                // 分别处理左半部分和右半部分
                handleInsertData(singleId,modelCode,left);
                handleInsertData(singleId,modelCode,right);
            }
        }
    }

    /**
     * 存储数据
     *
     * @param signalFileModelVo 信号文件的模型数据信息参数
     * @param arrangeDataList   整理的数据集合
     */
    public void saveData(SignalModelLineVo signalFileModelVo, List<Map<String, Object>> arrangeDataList) {
        //封装SQL语句和参数
        String modelCode = signalFileModelVo.getBizCode();
        List<DmConfTableColumnVo> dmConfTableColumns = getAndCacheColumnConfs(modelCode);
        MakeSqlResultVo makeSqlResultVo = makeInsertSql(signalFileModelVo.getBizCode(),
                dmConfTableColumns, arrangeDataList);
        try {
            npjt.update(makeSqlResultVo.getSql(), makeSqlResultVo.getInsertMap());
        } catch (DataAccessException e) {
            log.info(e.getMessage());
            batchAddLineFailLog(signalFileModelVo.getSingleId(),
                    signalFileModelVo.getBizCode(), arrangeDataList, getExceptionMsg(e));
        }
    }

    /**
     * 组装sql和插入参数
     *
     * @param dmConfTableColumns 数据库模型表字段信息
     * @param arrangeDataList    整理的数据集合
     * @return 组装的SQL结果
     */
    private MakeSqlResultVo makeInsertSql(String bizCode, List<DmConfTableColumnVo> dmConfTableColumns,
                                          List<Map<String, Object>> arrangeDataList) {
        MakeSqlResultVo makeSqlResultVo = new MakeSqlResultVo();
        StringBuilder beforeSql = new StringBuilder();
        beforeSql.append("insert into odsuser.ods_")
                .append(bizCode)
                .append(" (")
                .append(String.join(",", FIXED_COLUMNS))
                .append(",");
        for (DmConfTableColumnVo dmConfTableColumn : dmConfTableColumns) {
            beforeSql.append(StringUtils.lowerCase(dmConfTableColumn.getColCode())).append(",");
        }
        beforeSql.deleteCharAt(beforeSql.length() - 1).append(") values ");

        Map<String, Object> insertMap = new HashMap<>();
        StringBuilder afterSql = new StringBuilder();
        //便利预存储数据
        for (int i = 0; i < arrangeDataList.size(); i++) {
            String suffix = "__$" + i;
            afterSql.append("(");

            //单条预存储数据
            Map<String, Object> map = arrangeDataList.get(i);
            for (String fixedColumn : FIXED_COLUMNS) {
                String key = fixedColumn + suffix;
                afterSql.append(":").append(key).append(",");
                insertMap.put(key, map.get(fixedColumn));
            }

            //便利数据库字段集合
            int tableColumnsSize = dmConfTableColumns.size();
            for (int j = 0; j < tableColumnsSize; j++) {
                String columnCode = StringUtils.lowerCase(dmConfTableColumns.get(j).getColCode());
                //获取当前单条预存储数据的对应字段
                Object obj = map.get(columnCode);
                //判断当前字段是否为可默认字段
                if (obj == null || "".equals(obj)) {
                    afterSql.append("default");
                } else {
                    String key = columnCode + suffix;
                    afterSql.append(":").append(key);
                    insertMap.put(key, obj);
                }
                if (j < tableColumnsSize - 1) {
                    afterSql.append(",");
                } else {
                    afterSql.append("),");
                }
            }
        }
        afterSql.deleteCharAt(afterSql.length() - 1);
        beforeSql.append(afterSql);
        makeSqlResultVo.setSql(beforeSql.toString());
        makeSqlResultVo.setInsertMap(insertMap);
        return makeSqlResultVo;
    }

    /**
     * 获取行号
     *
     * @param map 模型文件行数据
     * @return 模型文件内容的行号集合
     */
    private List<Integer> getLineNoList(List<Map<String, Object>> map) {
        List<Integer> lineNoList = new ArrayList<>();
        for (Map<String, Object> item : map) {
            lineNoList.add((Integer) item.get("#lineNo"));
        }
        return lineNoList;
    }

    private List<String[]> getLineValues(List<Map<String, Object>> map) {
        List<String[]> list = new ArrayList<>();
        for (Map<String, Object> item : map) {
            list.add((String[]) item.get("#values"));
        }
        return list;
    }

    /* **************************** 文件读写相关方法 *************************************/

    /**
     * * 读取txt文件的内容的行数
     * * @param fileSrc 想要读取的文件路径
     * * @return 返回文件内容
     */
    public Long readFileLineCount(File modelFile) {
        long count = -1L;
        try (Stream<String> lines = Files.lines(modelFile.toPath(), StandardCharsets.UTF_8)) {
            // 获取文件的行数
            count = lines.filter(StringUtils::isNotBlank).count();
        } catch (IOException e) {
            log.info("文件={}读取失败", modelFile.toPath());
            log.error("", e);
        }
        return count;
    }

    /**
     * * 读取txt文件的内容
     * * @param fileSrc 想要读取的文件路径
     * * @return 返回文件内容
     */
    public FileContentVo readFileContent(String fileSrc) {
        long startTime = System.currentTimeMillis();
        addTrace(LOG_NODE_SIGNAL, "INFO", null, "Read signal file " + fileSrc);
        FileContentVo fileContentVo = new FileContentVo();
        File file = new File(fileSrc);

        List<String> result = new ArrayList<>();
        fileContentVo.setSize(file.length());
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            //构造一个BufferedReader类来读取文件
            String s;
            while ((s = br.readLine()) != null && !s.trim().isEmpty()) {//使用readLine方法，一次读一行
                result.add(s);
            }
            fileContentVo.setContents(result);
            fileContentVo.setLineCount((long) result.size());
            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            addTrace(LOG_NODE_SIGNAL, "INFO", null,
                    "文件读取了%s毫秒,读取行数：%s", elapsedTime, result.size());
        } catch (IOException e) {
            fileContentVo.setStatus(1);
            addTrace(LOG_NODE_SIGNAL, "INFO", null,
                    "文件 %s 读取失败: %s", fileSrc, e.getMessage());
            log.error("", e);
        }
        return fileContentVo;
    }

    /**
     * 输出信号文件
     */
    public void writeBackSignalFile(long signalId, List<String> signalFileLines,
                                    Map<String, Integer> modelLineIndexMap, String signalFilePath, DmBussFileExchangeCtl ctl) {
        DmBussFileExchangeSignal signal = dmBussFileExchangeSignalDao.findById(signalId);
        List<String> content = new ArrayList<>(signalFileLines);

        if (!modelLineIndexMap.isEmpty()) {
            List<DmBussFileExchangeModel> models = queryModels(signal.getSignalId(), null);
            models.forEach(model -> {
                String fileName = StringUtils.trimToEmpty(model.getFileName());
                String bizCode = model.getBizCode();
                Integer lineIndex = modelLineIndexMap.get(bizCode);
                if (lineIndex != null) {
                    if (model.getPushStatus().equals("9")) {
                        content.set(lineIndex, fileName + ".success|" + bizCode
                                + "|" + model.getRowCount() + "|" + model.getRowCount());
                    } else {
                        List<DmLogFileExchangeFileLine> failLines = queryFailLineLog(model.getSignalId(), bizCode);
                        long failCount = failLines.stream().map(DmLogFileExchangeFileLine::getLineNo).distinct().count();
                        content.set(lineIndex, fileName + ".fail|" + bizCode
                                + "|" + model.getRowCount() + "|" + failCount);
                    }
                }
            });
        }

        String fileName = signal.getSignalFileName();
        if (signal.getStatus().equals("9")) {
            fileName += ".success";
        } else {
            String errorMsg = signal.getErrorMsg();
            if (StringUtils.isNotBlank(errorMsg)) {
                content.add("");
                content.add("Appendix:");
                content.add(errorMsg);
            }
            fileName += ".fail";
        }
        try {
//            File backFile = new File(joinPaths(srcDir, fileName));
//            FileUtils.writeLines(backFile, "utf-8", content);
//            copyToPrivateDir(backFile, false);
//            log.info("信号文件 " + fileName + " 已成功输出到" + srcDir);
            File backFile = new File(joinPaths(signalFilePath, fileName));
            FileUtils.writeLines(backFile, "utf-8", content);
            copyToPrivateDir(backFile, false, DateFormatUtils.format(ctl.getBussDate(), "yyyyMMdd"));
            log.info("信号文件 " + fileName + " 已成功输出到" + signalFilePath);
        } catch (Exception e) {
            throw new RuntimeException("Unable to write to file " + fileName, e);
        }
    }

    /**
     * 输出模型失败数据
     *
     * @param vo             信号文件的模型数据信息
     * @param modelFailLines 模型文件行内容集合
     */
    public void writeBackModelFail(SignalModelLineVo vo, List<DmLogFileExchangeFileLine> modelFailLines) {
        String backFilePath = joinPaths(vo.getDirPath(), vo.getFileName() + ".fail");
        List<String> content = modelFailLines.stream()
                .map(DmLogFileExchangeFileLine::getErrorMsg)
                .collect(Collectors.toList());
        try {
            File backFile = new File(backFilePath);
            FileUtils.writeLines(backFile, "utf-8", content);
            copyToPrivateDir(backFile, false, DateFormatUtils.format(vo.getDmBussFileExchangeCtl().getBussDate(), "yyyyMMdd"));
            log.info("模型异常信息已成功输出到" + backFilePath);
        } catch (IOException e) {
            log.error("无法写入模型失败信息文件 " + backFilePath + ",：" + e.getMessage());
        }
    }


    /**
     * 通过模型名称获取对应所有字段
     */
    public List<DmConfTableColumnVo> queryColumn(String bizCode) {
        return dmConfTableColumnDao.findByBizCode(bizCode);
    }

    /**
     * 新增信号文件信息
     *
     * @param ctl            文件交换控制信息
     * @param signalFileName 信号文件名称
     * @return 信号文件信息
     */
    private DmBussFileExchangeSignal addSignal(DmBussFileExchangeCtl ctl, String signalFileName) {
        DmBussFileExchangeSignal signal = new DmBussFileExchangeSignal();
        signal.setYearMonth(ctl.getYearMonth());
        signal.setBussDate(ctl.getBussDate());
        signal.setSignalFileName(signalFileName);
        signal.setStatus("0");
        signal.setStartTime(new Date());
        dmBussFileExchangeSignalDao.save(signal);
        return signal;
    }


    /**
     * 新增文件交换详情信息数据
     *
     * @param modelVo 信号文件的模型数据信息
     * @return 文件交换详情信息
     */
    private DmBussFileExchangeModel addDmBussFileExchangeModel(SignalModelLineVo modelVo) {
        DmBussFileExchangeModel model = new DmBussFileExchangeModel();
        model.setSignalId(modelVo.getSingleId());
        model.setBizCode(modelVo.getBizCode());
        model.setFileName(modelVo.getFileName());
        model.setRowCount(modelVo.getLineCount());
        model.setPushStatus(modelVo.getLineCount() == 0 ? "9" : "0");
        model.setStartPushTime(new Date());
        dmBussFileExchangeModelDao.save(model);
        return model;
    }

    /**
     * 新增模型行信息数据
     */
    private void addLineFailLog(Long signalId, String bizCode, Integer lineNo,
                                String[] values, String errMsg) {
        String pkValues = getModelPkValues(bizCode, values);
        String msg = pkValues + "|" + "Line[" + lineNo + "]:" + errMsg;
        msg = msg.replace("\r\n", " ").replace("\n", " ");
        msg = StringUtils.substring(msg, 0, 2000);

        DmLogFileExchangeFileLine dmLogFileExchangeFileLine = new DmLogFileExchangeFileLine();
        dmLogFileExchangeFileLine.setSignalId(signalId);
        dmLogFileExchangeFileLine.setBizCode(bizCode);
        dmLogFileExchangeFileLine.setLineNo(lineNo);
        dmLogFileExchangeFileLine.setResultStatus("F");
        dmLogFileExchangeFileLine.setErrorMsg(msg);
        dmLogFileExchangeFileLine.setCreateTime(new Date());
        synchronized (lineLogsLock) {
            lineLogs.add(dmLogFileExchangeFileLine);
        }
        tryInsertFileLines(false);
    }

    private void tryInsertFileLines(boolean forced) {
        List<DmLogFileExchangeFileLine> lineLogsNew = null;
        synchronized (lineLogsLock) {
            if (lineLogs.isEmpty()) {
                return;
            }
            if (forced || lineLogs.size() > 200) {
                lineLogsNew = lineLogs;
                lineLogs = new ArrayList<>();
            }
        }
        if (lineLogsNew != null) {
            dmLogFileExchangeFileLineDao.saveList(lineLogsNew);
        }
    }

    /**
     * 批量增加行错误日志
     */
    private void batchAddLineFailLog(Long signalId, String bizCode,
                                     List<Map<String, Object>> arrangeDataList, String errMsg) {
        List<Integer> lineNos = getLineNoList(arrangeDataList);
        List<String[]> lineValues = getLineValues(arrangeDataList);
        for (int i = 0; i < lineNos.size(); i++) {
            addLineFailLog(signalId, bizCode, lineNos.get(i), lineValues.get(i), errMsg);
        }
    }

    /**
     * 修改控制信息
     *
     * @param po         文件交换控制信息
     * @param status     状态
     * @param errMessage 错误信息
     */
    private void updateDmBussFileExchangeCtl(DmBussFileExchangeCtl po, String status, String errMessage) {
        po.setStatus(status);
        po.setErrorMsg(errMessage);
        if ("1".equals(status)) {
            po.setStartTime(new Date());
            po.setEndTime(null);
        }
        if ("9".equals(status)) {
            po.setEndTime(new Date());
        }
        po.setUpdateTime(new Date());
        dmBussFileExchangeCtlDao.updateAllById(po);
    }

    /**
     * 修改信号文件信息
     *
     * @param signal     文件交换信号文件信息
     * @param status     状态
     * @param errMessage 错误信息
     */
    private void updateSignal(DmBussFileExchangeSignal signal, String status, String errMessage) {
        signal.setStatus(status);
        signal.setErrorMsg(StringUtils.substring(errMessage, 0, 3800));
        signal.setEndTime(status.equals("1") ? null : new Date());
        dmBussFileExchangeSignalDao.updateAllById(signal);
    }

    /**
     * 修改文件交换详情信息
     *
     * @param status     状态
     * @param errMessage 错误信息
     */
    private void updateDmBussFileExchangeModel(DmBussFileExchangeModel model, String status, String errMessage) {
        model.setPushStatus(status);
        model.setPushErrorMsg(errMessage);
        model.setEndPushTime(status.equals("1") ? null : new Date());
        dmBussFileExchangeModelDao.updateById(model);
    }

    /**
     * 查询模型文件行集合信息
     */
    private List<DmLogFileExchangeFileLine> queryFailLineLog(Long signalId, String bizCode) {
        DmLogFileExchangeFileLine line = new DmLogFileExchangeFileLine();
        line.setSignalId(signalId);
        line.setBizCode(bizCode);
        line.setResultStatus("F");
        return dmLogFileExchangeFileLineDao.findList(line);
    }

    /**
     * 查询模型数据列表
     */
    private List<DmBussFileExchangeModel> queryModels(Long signalId, String status) {
        DmBussFileExchangeModel po = new DmBussFileExchangeModel();
        po.setSignalId(signalId);
        po.setPushStatus(status);
        return dmBussFileExchangeModelDao.findList(po);
    }

    /**
     * 将yyyy-MM-dd或yyyy-MM-dd HH:mm:ss字符串转化为Date
     */
    public Date toDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 定义日期格式的正则表达式
        String dateFormatRegex = "\\d{4}-\\d{1,2}-\\d{1,2}";
        String dateTimeFormatRegex = "\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}";

        if (dateStr.matches(dateFormatRegex)) {
            return DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateStr).toDate();
        } else if (dateStr.matches(dateTimeFormatRegex)) {
            return DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(dateStr).toDate();
        } else {
            throw new RuntimeException("Date time format not supported: " + dateStr);
        }
    }

    @Getter
    @Setter
    private static class ColDefaultValConfVo {
        private String bizCode;
        private String colCode;
        private String valueType;
        private String value;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ColDefaultValConfVo that = (ColDefaultValConfVo) o;
            return Objects.equals(bizCode, that.bizCode) && Objects.equals(colCode, that.colCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(bizCode, colCode);
        }
    }


    private void callVerify(long signalId) {
        List<DmBussFileExchangeModelVo> modelVos = dmBussFileExchangeModelDao.queryForVerify(Sets.newHashSet(signalId));
        String modelCode = null;
        try {
            for (DmBussFileExchangeModelVo modelVo : modelVos) {
                String taskCode = modelVo.getTaskCode();
                modelCode = modelVo.getBizCode();
                DmConfTableVo tableInfo = dmConfTableDao.findBizCode(modelVo.getBizCode());
                if (null != tableInfo && tableInfo.getValidIs().equals("1")) {
                    if (tableInfo.getTypeGroup().equals("7") || modelVo.getRowCount() == 0) {
                        //基础数据直接校验
                        dmDomainFileChangeSignalService.callVerify(entityId, taskCode, tableInfo.getBizTypeId(), userId);
                        //更新模型校验结束时间
                        dmBussFileExchangeModelDao.updateDealEndTime(signalId, modelCode);
                    } else {
                        //业务数据需判断前置数据是否存在
                        if (checkDependentModelDataTotal(tableInfo.getBizCode())) {
                            dmDomainFileChangeSignalService.callVerify(entityId, taskCode, tableInfo.getBizTypeId(), userId);
                            //更新模型校验结束时间
                            dmBussFileExchangeModelDao.updateDealEndTime(signalId, modelCode);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("数据校验失败", e);
            addTrace(LOG_NODE_OTHER, "WARN", null,
                    "Data verification failed at model %s: %s",
                    modelCode, getExceptionMsg(e));
        }
    }

    private boolean checkDependentModelDataTotal(String bizCode) {
        List<String> dependentModels = dmBussFileExchangeModelDao.queryModelDependentModels(bizCode);
        if (dependentModels.size() < 1) {
            return true;
        }
        for (String dependentModel : dependentModels) {
            //所依赖的模型数据数量等于零则返回false，不校验
            int modelDataTotal = dmBussFileExchangeModelDao.queryDmModelDataTotal("dm_" + dependentModel);
            if (modelDataTotal < 1) {
                return false;
            }
        }
        return true;
    }

    private List<DmConfTableColumnVo> getAndCacheColumnConfs(String bizCode) {
        if (!columnConfsCache.containsKey(bizCode)) {
            synchronized (columnConfsCache) {
                if (!columnConfsCache.containsKey(bizCode)) {
                    columnConfsCache.put(bizCode, queryColumn(bizCode));
                }
            }
        }
        return columnConfsCache.get(bizCode);
    }

    /**
     * 从模型数据中获取其主键数据
     */
    private String getModelPkValues(String bizCode, String[] values) {
        if (!modelPkIndexCache.containsKey(bizCode)) {
            synchronized (modelPkIndexCache) {
                if (!modelPkIndexCache.containsKey(bizCode)) {
                    List<Integer> indexs = new ArrayList<>();
                    List<DmConfTableColumnVo> confs = getAndCacheColumnConfs(bizCode);
                    for (int i = 0; i < confs.size(); i++) {
                        if ("1".equals(confs.get(i).getConstraintType())) {
                            indexs.add(i);
                        }
                    }
                    int[] indexArray = new int[indexs.size()];
                    for (int i = 0; i < indexArray.length; i++) {
                        indexArray[i] = indexs.get(i);
                    }
                    modelPkIndexCache.put(bizCode, indexArray);
                }
            }
        }

        int[] indexs = modelPkIndexCache.get(bizCode);
        if (indexs.length == 0) {
            return "";
        }
        int maxIndex = Arrays.stream(indexs).max().orElse(0);

        String[] pkValues = new String[indexs.length];
        Arrays.fill(pkValues, "");
        if (values != null && values.length >= maxIndex) {
            for (int i = 0; i < indexs.length; i++) {
                pkValues[i] = Strings.nullToEmpty(values[indexs[i]]);
            }
        }
        return String.join("|", pkValues);
    }

    private String getExceptionMsg(Exception e) {
        Throwable rootCause = ExceptionUtils.getRootCause(e);
        return rootCause == null ? e.getMessage() : rootCause.getMessage();
    }

    private Long getAndCacheBizTypeId(String bizCode) {
        if (!bizMappingCache.containsKey(bizCode)) {
            DmConfTableVo vo = dmConfTableDao.findBizCode(bizCode);
            bizMappingCache.put(bizCode, vo.getBizTypeId());
        }
        return bizMappingCache.get(bizCode);
    }

    private static String format(String format, Object... args) {
        return String.format(format, args);
    }

    private List<String> trimEnd(List<String> list) {
        int end = 0;
        for (int i = list.size() - 1; i >= 0; i--) {
            String s = list.get(i);
            if (StringUtils.isNotBlank(s)) {
                end = i;
                break;
            }
        }
        List<String> newList = new ArrayList<>();
        for (int i = 0; i <= end; i++) {
            newList.add(list.get(i));
        }
        return newList;
    }
}
