/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2020-12-17 16:52:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2020-12-17 16:52:00<br/>
 * Description: 数据处理校验规则类型表 Dao类<br/>
 * Related Table Name: DM_CHECKRULE_TYPE<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmDataDealDao{

    void executeDeal(Map<String, Object> map);

    String getTaskCode(Map<String,Object> map);

}