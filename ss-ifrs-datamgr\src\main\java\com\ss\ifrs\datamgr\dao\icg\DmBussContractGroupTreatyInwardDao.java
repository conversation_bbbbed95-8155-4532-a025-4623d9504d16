package com.ss.ifrs.datamgr.dao.icg;

import com.ss.ifrs.datamgr.pojo.stat.po.DmDataRecord;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmMajorRiskSolveVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmBussContractGroupTreatyInwardDao extends IDao<DmDataRecord, Long> {

    String checkPortfolioIsFinish(Long entityId, String bussYearMonth);

    String checkIcgIsFinish(Long entityId, String bussYearMonth);

    void updateMajorRiskByVo(DmMajorRiskSolveVo dmMajorRiskSolveVo);

    String checkMeasurementUnitIsHave(Long entityId, String bussYearMonth);

    String checkMajorRiskIsFinish(Long entityId, String bussYearMonth);

    String checkEvaluateApproachIsFinish(Long entityId, String bussYearMonth);

    String checkProfitIsFinish(Long entityId, String bussYearMonth);

    String checkIcgConfirmIsFinish(Long entityId, String bussYearMonth);

    String checkConfirmIsFinish(Long entityId, String bussYearMonth);

}
