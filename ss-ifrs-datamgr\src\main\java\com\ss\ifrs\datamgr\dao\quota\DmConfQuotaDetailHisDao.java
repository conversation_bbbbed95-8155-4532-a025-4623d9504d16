/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 10:06:14
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;


import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfQuota;
import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfQuotaDetailHis;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 10:06:14<br/>
 * Description: null Dao类<br/>
 * Related Table Name: bbs_conf_quota_detailhis<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 * <AUTHOR>
 */
@Mapper
public interface DmConfQuotaDetailHisDao extends IDao<DmConfQuotaDetailHis, Long> {

    void saveDetailListByQuota(@Param("quota") DmConfQuota dmConfQuota, @Param("userId") Long userId, @Param("operType") String operType, @Param("addFlag") Boolean addFlag);

}