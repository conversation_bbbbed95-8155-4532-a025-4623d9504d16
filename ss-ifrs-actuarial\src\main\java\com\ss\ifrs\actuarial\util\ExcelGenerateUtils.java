package com.ss.ifrs.actuarial.util;

import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.library.constant.ExcelConstant;
import com.ss.platform.core.constant.LocalesConstanst;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 * @date 2021/3/15
 * 生成excel文件
 */
public class ExcelGenerateUtils {


    /**
     * @Method generateWorkbook 生成wordbook
     * <AUTHOR>
     * @Date 2022/9/5
     * @Description
     * @param atrExcelCellVo
     * @Return
     */
    private static XSSFWorkbook generateWorkbook(List<AtrExcelCellVo> atrExcelCellVo) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");
        XSSFCellStyle row1_style = createCellStyle(workbook, (short) 10, XSSFFont.COLOR_NORMAL, (short) 200, "宋体", HorizontalAlignment.CENTER);
        // 设置样式
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置样式
        /*style.setFillForegroundColor(IndexedColors.SKY_BLUE.index);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);*/
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //换行
        //style.setWrapText(true);
        // 生成一种字体
        XSSFFont font = workbook.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 11);
        //设置字体加粗
        font.setBold(true);
        // 在样式中引用这种字体
        style.setFont(font);

        // 生成Excel表格
        generateExcelTable(workbook, sheet, style, atrExcelCellVo);

        return workbook;
    }


    /**
     * @Method generateExportExcel 导出Excel
     * <AUTHOR>
     * @Date 2022/9/5
     * @Description
     * @param atrExcelCellVo 单元格信息列表
     * @param fileName 文件名称
     * @param filePath 文件地址
     * @Return
     */
    public static void generateExportExcel(HttpServletResponse response, List<AtrExcelCellVo> atrExcelCellVo, String fileName, String filePath) throws Exception {
        if (CollectionUtils.isEmpty(atrExcelCellVo)) {
            throw new NoSuchElementException("Generate excel fail : not found the excel header information!");
        }

        response.reset();
        response.setContentType("application/vnd.ms-excel");
        response.addHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("GB2312"), "8859_1") + ".xlsx");

        // 生成POI的Workbook
        XSSFWorkbook workbook = generateWorkbook(atrExcelCellVo);

        /*File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }*/

        // 使用流输出Excel
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();
    }

    /**
     * 生成颜色
     *
     * @param workbook
     * @param xssfColor
     * @return
     */
    private static XSSFFont colorFont(XSSFWorkbook workbook, XSSFColor xssfColor, boolean boldIs) {
        XSSFFont font = workbook.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 11);
        //设置字体加粗
        font.setBold(boldIs);
        //设置颜色
        font.setColor(xssfColor);
        return font;
    }


    /**
     * 生成Excel表格
     *
     * @param workbook
     * @param sheet
     * @param cellStyle
     * @param cell
     */
    private static void generateExcelTable(XSSFWorkbook workbook, XSSFSheet sheet, XSSFCellStyle cellStyle, List<AtrExcelCellVo> cell) {
        XSSFFont font = cellStyle.getFont();
        XSSFFont redFont = colorFont(workbook, new XSSFColor(IndexedColors.RED, null), true);

        //创建绘图对象
        XSSFDrawing patriarch = sheet.createDrawingPatriarch();

        XSSFCellStyle cellStyleFormat;
        AtrExcelCellVo atrExcelCellVo;
        XSSFRow xssfRow;
        XSSFCell xssfCell;
        String colLength;
        String language;
        XSSFDataFormat format;
        String columnCellType;
        StringBuffer commentBuffer;
        StringBuffer stringBuffer;
        String[] split;
        Integer precisionLength;

        XSSFRow tempRow;
        XSSFCell tempCell;
        Object cellValue;
        String content;
        XSSFRichTextString xts;
        int index;
        CellStyle tempCellStyle;

        for (int i = 0; i < cell.size(); i++) {
            cellStyleFormat = workbook.createCellStyle();
            atrExcelCellVo = cell.get(i);
            atrExcelCellVo.setHssfCellStyle(cellStyle);
            xssfRow = sheet.getRow(atrExcelCellVo.getRow());

            if (ObjectUtils.isEmpty(xssfRow)) {
                xssfRow = sheet.createRow(atrExcelCellVo.getRow());
            }

            sheet.setDefaultColumnStyle(atrExcelCellVo.getColumn(), cellStyleFormat);
            xssfCell = xssfRow.getCell(atrExcelCellVo.getColumn());
            if (xssfCell == null) {
                xssfCell = xssfRow.createCell(atrExcelCellVo.getColumn());
                xssfCell.setCellType(CellType.NUMERIC);
            }

            //长度
            colLength = atrExcelCellVo.getColLength();
            //语言
            language = atrExcelCellVo.getLanguage();

            //设置当前列的单元格格式
            format = workbook.createDataFormat();
            columnCellType = atrExcelCellVo.getColumnCellType();
            commentBuffer = new StringBuffer();

            // 表头才需要加批注
            if(atrExcelCellVo.getRow() == 0){

                if (ExcelConstant.ExcelCellType.STRING.equals(columnCellType)) {
                    cellStyleFormat.setDataFormat(format.getFormat("@"));
                    //增加批注
                    if(LocalesConstanst.Language.ZH.equals(language)){
                        commentBuffer.append("文本长度("+colLength+")\n");
                    }else if (LocalesConstanst.Language.TN.equals(language)){
                        commentBuffer.append("文本長度("+colLength+")\n");
                    }else {
                        commentBuffer.append("Text Length("+colLength+")\n");
                    }
                } else if (ExcelConstant.ExcelCellType.INTEGER.equals(columnCellType)) {
                    //cellStyleFormat.setDataFormat(format.getFormat("0"));
                    //增加批注
                    if(LocalesConstanst.Language.ZH.equals(language)){
                        commentBuffer.append( "数值长度("+colLength+")\n");
                    }else if (LocalesConstanst.Language.TN.equals(language)){
                        commentBuffer.append( "數值長度("+colLength+")\n");
                    }else {
                        commentBuffer.append( "Number Length("+colLength+")\n");
                    }
                } else if (ExcelConstant.ExcelCellType.DOUBLE.equals(columnCellType)) {
                    stringBuffer = new StringBuffer("0.");
                    split = colLength.split(",");
                    if(split.length > 1){
                        precisionLength = Integer.valueOf(split[1]);
                        for (int j = 0; j < precisionLength; j++) {
                            stringBuffer.append("0");
                        }
                    }
                    //cellStyleFormat.setDataFormat(format.getFormat(stringBuffer.toString()));
                    //增加批注
                    if(LocalesConstanst.Language.ZH.equals(language)){
                        commentBuffer.append( "数值长度("+colLength+")\n");
                    }else if (LocalesConstanst.Language.TN.equals(language)){
                        commentBuffer.append( "數值長度("+colLength+")\n");
                    }else {
                        commentBuffer.append( "Number Length("+colLength+")\n");
                    }
                } else if (ExcelConstant.ExcelCellType.DATE.equals(columnCellType)) {
                    cellStyleFormat.setDataFormat(format.getFormat("m/d/yy"));
                    //增加批注
                    if(LocalesConstanst.Language.ZH.equals(language)){
                        commentBuffer.append("日期格式('yyyy/MM/DD')\n");
                    }else if (LocalesConstanst.Language.TN.equals(language)){
                        commentBuffer.append("日期格式('yyyy/MM/DD')\n");
                    }else {
                        commentBuffer.append("Date Format('yyyy/MM/DD')\n");
                    }
                }
            }else {
                // 非表头字体不需要加粗
                redFont = colorFont(workbook, new XSSFColor(IndexedColors.RED, null), false);
                //redFont.setBold(false);
            }

            //合并单元格
            if (atrExcelCellVo.isMergerCell()) {
                for (int j = atrExcelCellVo.getMergerRowStart(); j <= atrExcelCellVo.getMergerRowEnd(); j++) {
                    for (int k = atrExcelCellVo.getMergerColumnStart(); k <= atrExcelCellVo.getMergerColumnEnd(); k++) {
                        tempRow = sheet.getRow(j);
                        if (tempRow == null) {
                            tempRow = sheet.createRow(j);
                        }
                        tempCell = tempRow.getCell(k);
                        if (tempCell == null) {
                            tempCell = tempRow.createCell(k);
                        }
                        tempCell.setCellStyle(atrExcelCellVo.getHssfCellStyle());
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(atrExcelCellVo.getMergerRowStart(), atrExcelCellVo.getMergerRowEnd(), atrExcelCellVo.getMergerColumnStart(), atrExcelCellVo.getMergerColumnEnd()));
            }

            //设置单元格的值
            cellValue = atrExcelCellVo.getValue();
            if (cellValue instanceof Boolean) {
                xssfCell.setCellValue((Boolean) cellValue);
            } else if (cellValue instanceof Date) {
                xssfCell.setCellValue((Date) cellValue);
            } else if (cellValue instanceof String) {
                xssfCell.setCellValue((String) cellValue);
                content = (String) cellValue;
                xts = new XSSFRichTextString(content);
                index = content.indexOf("*");
                if (index != -1) {
                    xts.applyFont(index, index + 1, redFont);
                    xts.applyFont(index + 1, content.length(), font);
                    /*String string = xts.getString();*/
                    xssfCell.setCellValue(xts);
                } else {
                    xssfCell.setCellValue((String) cellValue);
                }
            } else if (cellValue instanceof Double) {
                xssfCell.setCellValue((Double) cellValue);
            } else if (cellValue instanceof Integer) {
                xssfCell.setCellValue((Integer) cellValue);
            } else if (cellValue instanceof Calendar) {
                xssfCell.setCellValue((Calendar) cellValue);
            } else if (cellValue instanceof RichTextString) {
                xssfCell.setCellValue((RichTextString) cellValue);
            }

            // 表头样式
            if(atrExcelCellVo.getRow() == 0){
                //设置单元格的样式
                if (ObjectUtils.isNotEmpty(atrExcelCellVo.getHssfCellStyle())) {
                    xssfCell.setCellStyle(atrExcelCellVo.getHssfCellStyle());
                }

                //设置批注
                commentBuffer.append("\n");
                if(StringUtils.isNotEmpty(atrExcelCellVo.getComment())){
                    commentBuffer.append(atrExcelCellVo.getComment());
                }
                //xssfCell.setCellComment(createComment(patriarch,xssfCell,commentBuffer.toString()));


            }else if(atrExcelCellVo.getRow() > 0){
                // 非表头设置对齐方式
                if(ObjectUtils.isNotEmpty(atrExcelCellVo.getAlignment())){
                    tempCellStyle = workbook.createCellStyle();
                    tempCellStyle.setAlignment(atrExcelCellVo.getAlignment());
                    xssfCell.setCellStyle(tempCellStyle);
                }
            }

            // 根据字段长度自动调整列的宽度
            //sheet.autoSizeColumn(atrExcelCellVo.getColumn(), true); // 每个单元格都只适用宽度，执行非常慢
        }
    }

    /**
     *   生成批注对象，XSSFClientAnchor定义批注大小
     */
    public static XSSFComment createComment ( XSSFDrawing patriarch,XSSFCell xssfCell ,String commentValue){
        XSSFComment comment = patriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) xssfCell.getColumnIndex(), xssfCell.getRowIndex(), (short) xssfCell.getColumnIndex() + 5, xssfCell.getRowIndex() + 6));
        comment.setString(commentValue);
        return comment;
    }


    /**
     * 生成样式
     *
     * @param workbook
     * @param fontHeightInPoints
     * @param color
     * @param fontHeight
     * @param fontName
     * @param align
     * @return
     */
    private static XSSFCellStyle createCellStyle(XSSFWorkbook workbook, short fontHeightInPoints, short color, short fontHeight, String fontName, HorizontalAlignment align) {
        XSSFFont font = workbook.createFont();
        // 表头字体大小
        if (fontHeightInPoints != 0) {
            font.setFontHeightInPoints(fontHeightInPoints);
        }

        //字体颜色
        if (color != 0) {
            font.setColor(color);
        }

        if (fontHeight != 0) {
            font.setFontHeight(fontHeight);
        }

        // 表头字体名称
        //font.setFontName("宋体");
        if (StringUtils.isNotEmpty(fontName)) {
            font.setFontName(fontName);
        }

        XSSFCellStyle cellStyle = workbook.createCellStyle();
        if (ObjectUtils.isNotEmpty(align)) {
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
        }

        cellStyle.setFont(font);
        return cellStyle;
    }
}
