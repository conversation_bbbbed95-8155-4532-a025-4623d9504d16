package com.ss.ifrs.datamgr.job;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.datamgr.feign.BmsScheduleFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.ifrs.datamgr.service.DmBussConfigCheckService;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodService;
import com.ss.platform.pojo.bms.job.po.BmsScheduleDetail;
import com.ss.platform.pojo.bms.log.po.BmsLogScheduleJob;
import com.ss.platform.pojo.bms.log.vo.BmsLogScheduleJobVo;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskDetailVo;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DataUtil;
import com.ss.library.utils.FastJsonUtil;
import com.ss.library.utils.TaskExecuteUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: DmJobBussApi
 * @Description: 定时任务业务Api
 */
@RestController
@RequestMapping("/job")
@Api(value = "数据平台定时任务业务Api")
@Slf4j
public class DmJobBussApi extends BaseApi {

	@Autowired
    BmsScheduleFeignClient bmsScheduleFeignClient;
	
	@Autowired
	DmConfBussPeriodService dmConfBussPeriodService;
	
	@Autowired
	DmBussConfigCheckService dmBussConfigCheckService;
	
	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	@ApiOperation(value = "数据平台配置检查(仅供系统管理配置检查任务内部调用)")
	@RequestMapping(value = "/job_check_config", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	@Async("ruleThreadPool")
	BaseResponse<String> jobCheckConfig(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		DmConfBussPeriodVo dmConfBussPeriodVo = new DmConfBussPeriodVo();
		dmConfBussPeriodVo.setEntityId(Long.parseLong(jsonParam.getString("entityId")));
		try {
			DmConfBussPeriodVo checkBussPeriodVo = dmConfBussPeriodService.getOwBussPeriod(dmConfBussPeriodVo);
			if (ObjectUtils.isNotEmpty(checkBussPeriodVo)) {
				dmBussConfigCheckService.configCheck(checkBussPeriodVo, 1L);
			}
			return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, ResCodeConstant.ResCode.SUCCESS);
		} catch (UnexpectedRollbackException e) {
			logger.error(e.getLocalizedMessage(), e);
			return new BaseResponse<String>(ResCodeConstant.ResCode.OTHER_ERROR, null);
		}
	}

	@ApiOperation(value = "文件交换校验自动任务接口")
	@RequestMapping(value = "/check_file_exchange", method = { RequestMethod.GET, RequestMethod.POST })
	@PermissionRequest(required = false)
	public BaseResponse<Object> checkFileExchange(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取准备中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);
	}

	@ApiOperation(value = "文件交换自动任务接口")
	@RequestMapping(value = "/file_exchange", method = { RequestMethod.GET, RequestMethod.POST })
	@PermissionRequest(required = false)
	public BaseResponse<Object> runFileExchange(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取准备中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);
	}
	
	@ApiOperation(value = "数据校验自动任务接口")
	@RequestMapping(value = "/data_check", method = { RequestMethod.GET, RequestMethod.POST })
	@PermissionRequest(required = false)
	public BaseResponse<Object> runDataCheckTask(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取准备中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);
	}
	
	@ApiOperation("同步业务单位下的业务期间状态")
	@RequestMapping(value = "/sync_period_status", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> syncPeriodStatusByEntity(HttpServletRequest request,
	                                                     @RequestBody JSONObject jsonParam) {
		// 获取准备中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);
		// 此处查询准备中的业务期间只是做个引子作用，里面实际根据业务单位去更新状态
		if(ObjectUtils.isEmpty(bussPeriodVoList)){
			bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		}
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);
		
	}
	
	@ApiOperation(value = "删除校验日志自动任务")
	@RequestMapping(value = "/delete_check_log", method = RequestMethod.POST)
	public BaseResponse<Object> deleteCheckLog(HttpServletRequest request,
	                                           @RequestBody JSONObject jsonParam) {
		// 获取准备中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);

		
	}
	
	@ApiOperation(value = "统计etl未处理的数量自动任务接口")
	@RequestMapping(value = "/stat_count_etl", method = { RequestMethod.GET, RequestMethod.POST })
	@PermissionRequest(required = false)
	public BaseResponse<Object> runStatCountEtlTask(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取准备中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);
	}

	@ApiOperation(value = "数据指标核对")
	@RequestMapping(value = "/dataVerify", method = RequestMethod.POST)
	public BaseResponse<Object> dataVerify(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);

	}

	@ApiOperation(value = "直保合同分组")
	@RequestMapping(value = "/contractGroup", method = RequestMethod.POST)
	public BaseResponse<Object> contractGroup(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		
		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, "DD");
		
	}

	@ApiOperation(value = "再保合约分入合同分组")
	@RequestMapping(value = "/treaty_in/contractGroup", method = RequestMethod.POST)
	public BaseResponse<Object> treatyInContractGroup(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, "TI");
		
	}

	@ApiOperation(value = "再保合约分出合同分组")
	@RequestMapping(value = "/treaty_out/contractGroup", method = RequestMethod.POST)
	public BaseResponse<Object> treatyOutContractGroup(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, "TO");
		
	}

	@ApiOperation(value = "临分分入合同分组")
	@RequestMapping(value = "/fac_in/contractGroup", method = RequestMethod.POST)
	public BaseResponse<Object> facInContractGroup(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, "FI");
		
	}

	@ApiOperation(value = "临分分出合同分组")
	@RequestMapping(value = "/fac_out/contractGroup", method = RequestMethod.POST)
	public BaseResponse<Object> facOutContractGroup(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, "FO");
		
	}

	@ApiOperation(value = "费用分摊数据推送")
	@RequestMapping(value = "/expDataPush", method = RequestMethod.POST)
	public BaseResponse<Object> expDataPush(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);

	}

	@ApiOperation(value = "精算平台数据推送")
	@RequestMapping(value = "/atrDataPush", method = RequestMethod.POST)
	public BaseResponse<Object> atrDataPush(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);

	}

	@ApiOperation(value = "会计平台数据推送")
	@RequestMapping(value = "/accDataPush", method = RequestMethod.POST)
	public BaseResponse<Object> accDataPush(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取处理中的业务年月
		List<DmConfBussPeriodVo> bussPeriodVoList = dmConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList, null);

	}
	
	/**
	 * @param request, jsonParam, bussPeriodVo, bizCode, businessModel
	 * @return com.ss.platform.pojo.base.po.BaseResponse<java.lang.Object>
	 * @throws
	 * @description 处理业务功能自动任务【公共，并初始化功能日志】
	 * <AUTHOR>
	 * @date 2022/9/27 16:16
	 */
	private BaseResponse<Object> dealFunctionForJob(HttpServletRequest request, JSONObject jsonParam, List<DmConfBussPeriodVo> bussPeriodVoList, String businessModel) {
		Long userId = ObjectUtils.isEmpty(this.loginUserId(request)) ? 1L : this.loginUserId(request);
		// 根据业务单位和功能组ID获取功能组清单【按优先级排序】
		List<BmsQrtzConfTaskDetailVo> confTaskDtlList = bmsScheduleFeignClient.findConfTaskDtlListByConfTaskId(Long.parseLong(jsonParam.getString("entityId")), Long.valueOf(jsonParam.getString("confTaskId")));
		
		// 检查功能配置是否为空
		if(ObjectUtils.isEmpty(confTaskDtlList)){
				// 无功能任务日志插入，说明未找到合适的业务期间或功能执行，应不做处理
				return new BaseResponse<>(ResCodeConstant.ResCode.RULE_UNMATCH, "No matching The Function found. please check before executing!");
		}
		
		// 有功能任务日志插入，继续调用JobApi
		if (ObjectUtils.isNotEmpty(bussPeriodVoList)) {
			
			// 是否执行DB操作发生异常，默认false
			boolean hasErrorCaused = false;
			
			// 功能任务执行DB异常信息
			String taskDtlErrorMsg = "";
			
			StringBuilder strBud = new StringBuilder();
			
			for (DmConfBussPeriodVo bussPeriodVo : bussPeriodVoList) {
				// 循环功能组
				for (BmsQrtzConfTaskDetailVo confTaskDtlVo : confTaskDtlList) {
					
					// 捕获功能执行异常，若发生异常不影响其它非依赖功能的执行，有依赖功能关系的更新状态不执行处理
					try {
						// 按功能类型处理
						switch (confTaskDtlVo.getTaskType()) {
							case CommonConstant.ScheduleTaskType.METHOD:
								// 写入功能清单方法参数表，合同组功能链方法参数仅业务单元和业务年月
								DmDataSearchVo dmDataSearchVo = new DmDataSearchVo();
								dmDataSearchVo.setEntityId(Long.parseLong(jsonParam.getString("entityId")));
								dmDataSearchVo.setBussYearMonth(bussPeriodVo.getYearMonth());
								dmDataSearchVo.setTaskCode(jsonParam.getString("taskCode"));
								dmDataSearchVo.setTaskMode(CommonConstant.ScheduleTaskMode.AUTO);
								dmDataSearchVo.setRetryOrder(Long.valueOf(jsonParam.getString("retryOrder")));
								
								// 该字段用于各业务模型进行合同分组
								dmDataSearchVo.setBusinessModel(businessModel);
								
								// 转换为json字符串数据
								String jsonData = FastJsonUtil.toJson(dmDataSearchVo);
								
								/* // 查询功能清单方法参数表
								BmsQrtzConfMethodParamVo bplQrtzConfMethodParamVo = new BmsQrtzConfMethodParamVo();
								bplQrtzConfMethodParamVo.setTaskDetailId(confTaskDtlVo.getTaskDetailId());
								bplQrtzConfMethodParamVo.setEntityId(Long.parseLong(jsonParam.getString("entityId")));
								bplQrtzConfMethodParamVo.setYearMonth(bussPeriodVo.getYearMonth());
								bplQrtzConfMethodParamVo.setBookCode(null);
								bplQrtzConfMethodParamVo.setValidIs(CommonConstant.ValidStatus.VALID);
								
								// 查询参数表
								BmsQrtzConfMethodParamVo taskDetailMethodParam = bmsScheduleFeignClient.findTaskDetailMethodParam(bplQrtzConfMethodParamVo);
								
								if (ObjectUtils.isNotEmpty(taskDetailMethodParam)) {
									bplQrtzConfMethodParamVo.setParamId(taskDetailMethodParam.getParamId());
								}
								// 刷新参数表
								bplQrtzConfMethodParamVo.setJsonParam(jsonData);
								bmsScheduleFeignClient.addMethodParam(bplQrtzConfMethodParamVo); */
								
								// 获取方法的参数序列化对象
								Class[] argsClass = ClassUtil.convertClassName(confTaskDtlVo.getSerializedParam());
								Object[] args = ClassUtil.convertArgsObject(argsClass, request, jsonData);
								logger.debug("Class[]: " + Arrays.toString(argsClass));
								logger.debug("Object[]: " + Arrays.toString(args));
								// 执行方法调用，切面内更新功能组日志
								TaskExecuteUtils.invokeMethod(confTaskDtlVo.getMethod(), argsClass, args, confTaskDtlVo.getAsyncIs());
								break;
							case CommonConstant.ScheduleTaskType.PROCEDURE:
								// 执行存储过程，参数个数及数据类型要一致
								TaskExecuteUtils.callProcedureForInOutParam(this.jdbcTemplate,
										confTaskDtlVo.getProcName(),
										confTaskDtlVo.getParam(),
										new Object[]{
												Long.parseLong(jsonParam.getString("entityId")),
												bussPeriodVo.getYearMonth()},
										new Object[]{},
										confTaskDtlVo.getAsyncIs());
								
								// 执行成功后，更新功能组日志
								this.updateScheduleJobLogForCallProcedure(Long.parseLong(jsonParam.getString("entityId")), confTaskDtlVo, bussPeriodVo.getYearMonth(), null, userId);
								break;
							default:
								logger.info("Task type not support");
								break;
						}
						
					} catch (Exception e) {
						// 执行API调度发生异常处理
						logger.error(e.getMessage(), e);
						
						Map<String, Object> paramMap = new HashMap<>();
						// 更新日志参数1
						paramMap.put("entityId", Long.parseLong(jsonParam.getString("entityId")));
						// 更新日志参数2
						paramMap.put("taskCode", jsonParam.getString("taskCode"));
						// 更新日志参数3
						paramMap.put("taskMode", CommonConstant.ScheduleTaskMode.AUTO);
						// 更新日志参数4
						paramMap.put("yearMonth", bussPeriodVo.getYearMonth());
						// 更新日志参数5
						paramMap.put("bookCode", null);
						// 更新日志参数6
						paramMap.put("funcCode", confTaskDtlVo.getFuncCode());
						// 更新日志参数7
						paramMap.put("taskStatus", CommonConstant.ScheduleLogStatus.FAILED);
						
						// 功能执行DB发生异常，更新功能任务日志执行状态为：3-失败
						bmsScheduleFeignClient.updateTaskLogStatus(paramMap);
						
						// 回写每个功能执行异常信息，方便跟踪
						taskDtlErrorMsg = strBud.append(taskDtlErrorMsg).append("; ").append(confTaskDtlVo.getFuncCode()).append(": ").append(DataUtil.getMessageWithMaxLength(ExceptionUtils.getStackTrace(e))).toString();
						
						// 日志返回DB异常
						hasErrorCaused = true;
						
					}
					
				} // 功能任务循环结束
			} // 业务期间循环结束
			
			// 功能组存在某个功能执行发生异常后返回处理
			if (hasErrorCaused) {
				//记录异常信息
				if (taskDtlErrorMsg.length() > 3500) {
					taskDtlErrorMsg = taskDtlErrorMsg.substring(0, 3500);
				}
				return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, taskDtlErrorMsg);
			}
			
			
			// 功能组任务执行正常返回处理
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "success");
			
		} else {
			// 无功能任务日志插入，说明未找到合适的业务期间或功能执行，应不做处理
			return new BaseResponse<>(ResCodeConstant.ResCode.RULE_UNMATCH, "No matching business period found，please check!");
		}
		
	}
	
	/**
	 * @description 执行存储过程成功后更新功能组日志状态，并触发下一个依赖它的自动任务
	 * @param  entityId, confTaskDtlVo, yearMonth, bookCode, userId
	 * @return void
	 * @throws
	 * <AUTHOR>
	 * @date 2023/4/4 17:08
	 */
	private void updateScheduleJobLogForCallProcedure(Long entityId, BmsQrtzConfTaskDetailVo confTaskDtlVo, String yearMonth, String bookCode, Long userId) throws Exception{
		/**
		 * 功能组日志处理：
		 *  1、自动/手动模式，在切面执行功能后更新完功能日志，同步更新功能组日志，如果功能执行失败，
		 *      更新功能组日志状态为失败，记录信息；
		 *  2、自动/手动模式，如果当前功能是最后一个功能，且执行成功，则更新功能组日志状态为2-成功。否则为1-处理中
		 */
		
		BaseResponse<BmsLogScheduleJobVo> jobLogByConfTaskId = bmsScheduleFeignClient.findScheduleJobLogInProgressByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);

		if(ObjectUtils.isNotEmpty(jobLogByConfTaskId) && ObjectUtils.isNotEmpty(jobLogByConfTaskId.getResData())) {
			BmsLogScheduleJobVo scheduleJobLogVo = jobLogByConfTaskId.getResData();
			
			/**
			 * 功能组日志处理：
			 *      1、如果当前功能是最后一个功能，则设置功能组日志状态为2-已完成
			 *      2、然后触发下一个依赖它的自动任务执行
			 */
			
			BmsQrtzConfTaskDetailVo lastTaskDtlVo = bmsScheduleFeignClient.findLastTaskDtlByConfTaskId(entityId, confTaskDtlVo.getConfTaskId());
			// 最后一个功能完成后，更新功能组日志状态为2-已完成
			if (ObjectUtils.isNotEmpty(lastTaskDtlVo) && confTaskDtlVo.getFuncCode().equals(lastTaskDtlVo.getFuncCode())) {
				// 成功
				scheduleJobLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.SUCCESS);
				// 清除旧信息
				scheduleJobLogVo.setResCode(null);
				scheduleJobLogVo.setResData(null);
				// 最近一次成功结束时间
				scheduleJobLogVo.setLastEndDate(new java.util.Date());
				scheduleJobLogVo.setUpdatorId(userId);
				scheduleJobLogVo.setUpdateTime(new java.util.Date());

				// 更新功能组日志
				if (ObjectUtils.isNotEmpty(yearMonth)) {
					scheduleJobLogVo.setYearMonth(yearMonth);
				}
				if (ObjectUtils.isNotEmpty(bookCode)) {
					scheduleJobLogVo.setBookCode(bookCode);
				}
				BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
				bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);


				// 查询该功能组作为被依赖的对象的任务【可能多个】，然后触发下一个依赖它的自动任务执行
				List<BmsQrtzConfTaskVo> taskVoRelationTaskList = bmsScheduleFeignClient.findConfTaskVoByEntityIdAndRelationTaskId(entityId, confTaskDtlVo.getConfTaskId());

				// 获取被依赖的功能组定时任务信息
				if(ObjectUtils.isNotEmpty(taskVoRelationTaskList)){
					for(BmsQrtzConfTaskVo taskVoRelationTaskVo: taskVoRelationTaskList){
						BmsScheduleDetail scheduleDetail = bmsScheduleFeignClient.findJobDetailsByEntityIdAndConfTaskId(entityId, taskVoRelationTaskVo.getConfTaskId());
						if(ObjectUtils.isNotEmpty(scheduleDetail)){
							BmsScheduleJobVo bmsScheduleJobVo = new BmsScheduleJobVo();
							bmsScheduleJobVo.setJobGroup(scheduleDetail.getJobGroup());
							bmsScheduleJobVo.setJobName(scheduleDetail.getJobName());
							// 立即触发依赖任务执行
							bmsScheduleFeignClient.immediateExecutionTask(bmsScheduleJobVo);
						}
					}
				}
			}
		}

		// 若存在待执行的功能组任务日志，则更新日志状态为4-不执行
		BaseResponse<BmsLogScheduleJobVo> jobLogToExecByConfTaskId = bmsScheduleFeignClient.findScheduleJobLogToExecByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);
		if(ObjectUtils.isNotEmpty(jobLogToExecByConfTaskId) && ObjectUtils.isNotEmpty(jobLogToExecByConfTaskId.getResData())) {
			BmsLogScheduleJobVo scheduleJobLogVo = jobLogToExecByConfTaskId.getResData();

			// 更新功能组日志
			if (ObjectUtils.isNotEmpty(yearMonth)) {
				scheduleJobLogVo.setYearMonth(yearMonth);
			}
			if (ObjectUtils.isNotEmpty(bookCode)) {
				scheduleJobLogVo.setBookCode(bookCode);
			}
			// 已经执行过后，更新为其他待执行的状态为4-不执行
			scheduleJobLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.NONEXEC);
			// 清除旧信息
			scheduleJobLogVo.setResData("The Job task has been successfully executed by other tasks ");
			BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
			bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);
		}
	}
	
}
