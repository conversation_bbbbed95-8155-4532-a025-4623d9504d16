package com.ss.ifrs.datamgr.domain.service.model;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.model.vo.DmModelCompensateDataVo;


public interface DmModelCompensateService {

    // 获取映射条件
    String getMappingCondition(Long bizTypeId);

    // 获取需要补偿的保单号
    void insertNeedCompensatePolicy(DmModelCompensateDataVo dmBussDataCompensateVo);

    // 查询是否存在需要补偿的单
    Long getNeedCompensatePolicySize();

    // 将需要补偿的单插入到日志表中并清空补偿表
    void insertLogCompensatePolicy(String taskCode);

    // 插入实际需要补偿的数据
    void getModelCompensateData(DmModelCompensateDataVo dmBussDataCompensateVo);

    // 补偿数据校验
    void compensateDataVerify(DmBussDataVerifyVo dmBussDataVerifyVo);


}
