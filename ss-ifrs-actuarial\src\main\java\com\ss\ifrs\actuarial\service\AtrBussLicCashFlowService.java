package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicIcgAmountVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AtrBussCalcService
 * @Description: 计量计算服务接口类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:56
 * @Version: 1.0
 */
public interface AtrBussLicCashFlowService {


    //模糊查找对象信息
    Page<AtrBussLicCashFlowVo> findForDataTables(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Pageable pageParam);
    //Lic现金流计算
    void save(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId);

    AtrBussLicCashFlowVo findById(Long id);

    void delete(Long id, Long userId);

    Map<String, Object> findClaimPaid(AtrDapDrawVo atrDapDrawVo);

    List<AtrDapDrawVo> findPaidMode(AtrDapDrawVo atrDapDrawVo);

    Map<String, Object> findUlt(AtrDapDrawVo atrDapDrawVo);

    AtrDapDrawVo findId(AtrDapDrawVo vo);

    Map<String, Object> showExpectedClaim(AtrDapDrawVo atrDapDrawVo);

    Map<String, Object> findCalcClaimPaid(AtrDapDrawVo atrDapDrawVo);

    Boolean confirm(AtrBussLicCashFlowVo bussLicCashFlowVo, Long userId);

    void revoke(AtrBussLicCashFlowVo bussLicCashFlowVo, Long userId);

    void calculateAll(AtrBussLicCashFlowVo licCashFlowVo, Long userId);

    String confirmLicCfVersion(AtrBussLicCashFlowVo licCashFlowVo, Long userId);

    Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam);

    void exportLicResultExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) throws Exception;

    void exportLicAioiExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) throws Exception;

    List<AtrBussLicIcgAmountVo> showExpectedClaimTotal(AtrDapDrawVo atrDapDrawVo);

    void reSetLicCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo);
}
