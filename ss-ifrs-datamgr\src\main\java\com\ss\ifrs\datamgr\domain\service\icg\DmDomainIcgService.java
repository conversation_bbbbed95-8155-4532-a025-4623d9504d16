package com.ss.ifrs.datamgr.domain.service.icg;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;

public interface DmDomainIcgService {


    /**
     * 新计量单元识别
     * (在数据质量校验完成后执行）
     * 在前端页面直接调用
     */
    void cmunitIdentify(DmIcgProcVo dmIcgProcVo);

    /**
     * 重大风险测试
     */
    void majorRisk(DmIcgProcVo dmIcgProcVo);

    /**
     * 重大风险测试不通过的处理方案
     */
    void majorRiskNoPass(DmIcgProcVo dmIcgProcVo);

    /**
     * 计量单元判定
     * 在生成计量单元后执行
     * 给计量单元匹配业务线loa_code
     */
    void cmunitDetermine(DmIcgProcVo dmIcgProcVo);

    /**
     * 评估方法适配
     */
    void evaluateApproach(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同组合划分
     */
    void portfolioDiscern(DmIcgProcVo dmIcgProcVo);

    /**
     * 盈亏判定
     */
    void profitableEstimate(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同确认
     */
    void icgBorderSeal(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同组划分
     */
    void icgDiscern(DmIcgProcVo dmIcgProcVo);

    /**
     * 投成拆分
     */
    void icgInvestment(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同分组完成
     */
    void icgConfirm(DmIcgProcVo dmIcgProcVo);
}
