package com.ss.ifrs.datamgr.domain.service.impl.joint;

import com.ss.ifrs.datamgr.dao.domain.joint.DmDomainDataPushSignalDao;
import com.ss.ifrs.datamgr.domain.service.joint.DmDomainDataPushSignalService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelConditionVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelStatResultVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmOdsDataPushSignalVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DmDomainDataPushSignalServiceImpl implements DmDomainDataPushSignalService {

    @Autowired
    DmDomainDataPushSignalDao dmDomainDataPushSignalDao;

    @Override
    public Integer getRowCount(String bizCode, String taskCode) {
        return dmDomainDataPushSignalDao.getRowCount(bizCode, taskCode);
    }

    @Override
    public Integer updateTaskStatus(String bizCode, String taskCode, String taskStatus, String dealMsg) {
        return dmDomainDataPushSignalDao.updateTaskStatus(bizCode, taskCode, taskStatus, dealMsg);
    }

    @Override
    public Integer updateVerifyResult(DapModelStatResultVo dapModelStatResultVo, DmBussDataVerifyVo dmBussDataVerifyVo) {
        return dmDomainDataPushSignalDao.updateVerifyResult(dapModelStatResultVo, dmBussDataVerifyVo);
    }

    @Override
    public String getMaxTaskCode(String taskCodePrefix) {
        return dmDomainDataPushSignalDao.getMaxTaskCode(taskCodePrefix);
    }

    @Override
    public List<DmOdsDataPushSignalVo> findTodoList(DmDataDrawVo dmDataDrawVo) {
        return dmDomainDataPushSignalDao.findTodoList(dmDataDrawVo);
    }

    @Override
    public Integer fileUploadSignalSyncDataPushSignal(String taskCode,String yearMonth,String bizCode) {
        return dmDomainDataPushSignalDao.fileUploadSignalSyncDataPushSignal(taskCode,yearMonth,bizCode);
    }
}
