package com.ss.ifrs.datamgr.domain.service.duct;

import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelStatResultVo;

public interface DmDomainDuctStatService {

    void updateBussprdDtlAll(Long entityId, String yearMonth, Long bizTypeId);

    void updateBussprdDtl(Long entityId, String yearMonth, Long bizTypeId);

    String checkStatCount(Long entityId, String yearMonth, Long bizTypeId);

    void paringStat(Long entityId, String taskCode, Long bizTypeId);

    void paringStatEtl(Long entityId, String taskCode, Long bizTypeId);

    void paringStatReset(Long bizTypeId);

    void paringStatAddCol(Long bizTypeId,String columnName,String statType);

    void paringStatDelCol(Long bizTypeId,String columnName);

    void paringStatAll();

    DapModelStatResultVo getStatStatusCount(String tableName, String taskCode);

}
