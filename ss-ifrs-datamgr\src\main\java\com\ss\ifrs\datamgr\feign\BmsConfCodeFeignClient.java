package com.ss.ifrs.datamgr.feign;

import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.platform.pojo.com.po.ConfCode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsConfCodeFeignClient {

    @RequestMapping(value = "/gg_code/find_code/by_code_type", method = RequestMethod.GET)
    List<ConfCode> findCodeByCodeType(@RequestParam("codeType") String codeType);

    @RequestMapping(value = "/gg_code/find_code_by_codeIdx", method = RequestMethod.GET)
    List<String> findByCodeIdx(@RequestParam("codeCodeIdx") String codeCodeIdx);

    @RequestMapping(method = RequestMethod.POST, value = "/gg_code/find_taskcode")
    BaseResponse<Map<String, Object>> findTaskCode(@RequestBody Map<String, Object> paramMap);

    @RequestMapping(method = RequestMethod.POST, value = "/gg_code/find_other_list")
    BaseResponse<Map<String, Object>> findOtherList(@RequestBody Map<String, Object> params);

    @RequestMapping(method = RequestMethod.POST, value = "/gg_code/find_list_two")
    Map<String, List<ConfCode>> findListTwo(@RequestBody ConfCode sysCodeVo);

    @RequestMapping(method = RequestMethod.GET, value = "/gg_code/find_code/by_code_type/{upperCodeCode}")
    @PermissionRequest(required = false)
    List<ConfCode> findCodeListByCodeType(@PathVariable("upperCodeCode") String upperCodeCode);

    @RequestMapping(method = RequestMethod.POST, value = "/conf_loa/find_loa_vo")
    @PermissionRequest(required = false)
    BbsConfLoaVo findLoaVo(@RequestBody BbsConfLoaVo bbsConfLoaVo);
}
