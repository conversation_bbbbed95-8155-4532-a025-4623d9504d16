package com.ss.ifrs.datamgr.dao.icg;

import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreaty;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitTreatyOutward;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectSelectVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmunitMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitTreatyMajorTestVo;
import org.apache.ibatis.annotations.Mapper;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DmBussCmunitTreatyOutwardDao {

    /**
     * 计量单元划分
     */
    void cmunitIdentify(DmIcgProcVo dmIcgProcVo);
    void updateCmunitIdentifyRest(DmIcgProcVo dmIcgProcVo);
    void updateOldCmunitidentify(DmIcgProcVo dmIcgProcVo);
    void updateNonConformanceDate(DmIcgProcVo dmIcgProcVo);

    /**
     * 重大风险测试
     */
    void updateMajorRiskAllPass(DmIcgProcVo dmIcgProcVo);
    /**
     * 重大风险测试配置+规则方案,查不到配置或配置为否默认全部通过
     */
    void updateMajorRiskNoConfPass(DmIcgProcVo dmIcgProcVo);

    /**
     * 重大风险测试不通过的处理方案
     */
    void majorRiskNoPass(DmIcgProcVo dmIcgProcVo);

    void updateMajorRiskBussNoPass(DmIcgProcVo dmIcgProcVo);

    /**
     * 评估方法适配
     */
    void evaluateApproach(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同组合划分
     */
    void portfolioDiscern(DmIcgProcVo dmIcgProcVo);

    /**
     * 盈亏判定
     */
    void profitableEstimate(DmIcgProcVo dmIcgProcVo);
    Integer queryNeedProfitableEstimateData(DmIcgProcVo dmIcgProcVo);
    void updateProfitableEstimateInit(DmIcgProcVo dmIcgProcVo);
    void updateProfitableEstimateAdapterConfig(DmIcgProcVo dmIcgProcVo);


    void updateIcgBorderSealD2D3D4(DmIcgProcVo dmIcgProcVo);
    void updateIcgBorderSeal(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同组划分
     */
    void icgDiscern(DmIcgProcVo dmIcgProcVo);

    /**
     * 投成拆分
     */
    void icgInvestment(DmIcgProcVo dmIcgProcVo);
    Integer getCurrentYearMonthIcgGroupCount(DmIcgProcVo dmIcgProcVo);

    /**
     * 合同分组完成
     */
    void icgConfirm(DmIcgProcVo dmIcgProcVo);

    DmBussCmunitTreaty findById(Long recordId);

    Page<DmBussCmunitDirectSelectVo> findDataRecord(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo, Pageable pageParam);

    DmBussCmunitTreaty findCmunitVoByqueryMethod(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo);

    String getCmunitInfoById(DmCmunitMajorTestVo dmCmunitMajorTestVo);

    DmCmunitMajorTestVo getDmCmunitMajorTestVo(Long cmunitId);


    List<DmBussCmunitTreatyOutward> findBussCmunitConf(Long entityId);

    Long getCmunitMajorTestCount(DmIcgProcVo dmIcgProcVo);

    List<DmCmunitTreatyMajorTestVo> listCmunitTreatyMajorTestInfo(DmIcgProcVo dmIcgProcVo);

    void updateMajorResultByCmunitId(@Param("item")DmCmunitTreatyMajorTestVo dmCmunitTreatyMajorTestVos);

    void updateMajorResultNull(DmIcgProcVo dmIcgProcVo);

    void updateMajorRiskAdapterConfig(DmIcgProcVo dmIcgProcVo);
}
