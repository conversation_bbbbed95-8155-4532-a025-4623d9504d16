package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmDomainProfitableTiPaaDao {

    /*
     *S001,S002集合保费，费用
     */
    void calCmSetAmount(DmIcgProcVo dmIcgProcVo);
    void deleteDuctProfitableSetAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *跟單IACF率
     */
    void calIacfRate(DmIcgProcVo dmIcgProcVo);

    /*
     *预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
     */
    void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *预期赔付现金流现值
     */
    void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *集合成本率
     */
    void calCmSetCostRate(DmIcgProcVo dmIcgProcVo);

    /*
     *集合成本率(非金融风险调整)
     */
    void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo);

    /*
     *盈亏结果
     */
    void Profitable(DmIcgProcVo dmIcgProcVo);


    void deleteDuctCmUnitProfitable(DmIcgProcVo dmIcgProcVo);
    void insertDuctCmUnitProfitable(DmIcgProcVo dmIcgProcVo);

    Integer getDuctCmUnitProfitableCount(DmIcgProcVo dmIcgProcVo);
    Integer getDuctCmUnitProfitableFailCount(DmIcgProcVo dmIcgProcVo);

    void updateProfitableEstimateCheckFail(DmIcgProcVo dmIcgProcVo);
    void updateProfitableEstimateDuctData(DmIcgProcVo dmIcgProcVo);
    void updateProfitableEstimateResult(DmIcgProcVo dmIcgProcVo);

}
