package com.ss.ifrs.datamgr.domain.service.impl.icg;

import com.ss.ifrs.datamgr.dao.conf.DmConfContractCodeDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfContractRuleDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfContractRuleDefDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfProfitableParamDao;
import com.ss.ifrs.datamgr.domain.exception.DmCmunitException;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgCommonService;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractCodeVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRuleVo;
import com.ss.library.thread.ThreadUtils;
import com.ss.platform.core.constant.ThreadConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class DmDomainIcgCommonServiceImpl implements DmDomainIcgCommonService {


    @Autowired
    DmConfContractRuleDefDao dmConfContractRuleDefDao;

    @Autowired
    DmConfContractRuleDao dmConfContractRuleDao;

    @Autowired
    DmConfContractCodeDao dmConfContractCodeDao;

    @Autowired
    DmConfProfitableParamDao dmConfProfitableParamDao;

    /**
     * @param cmUnitId
     * @param entityId
     * @param bussDefCode
     * @param ruleId
     * @param ruleType
     * @param businessModel
     * @return
     */
    @Override
    public String getFactorValueCal(Long cmUnitId, Long entityId, String bussDefCode, Long ruleId, String ruleType,
                                    String businessModel) {

        /*String majorRisk = null;
        if (ruleId == null) {
            ruleId = dmConfContractRuleDefDao.getContractRuleId(entityId, bussDefCode, ruleType, businessModel,
                    businessModel.toUpperCase().substring(0, 1));
        }
        Long ruleCount = dmConfContractRuleDao.getCount(ruleId, businessModel);
        if (ruleCount == null || ruleCount < 1) return "R:";

        //循环
        List<DmConfContractRuleVo> dmConfContractRuleVos = dmConfContractRuleDao.getListByBusinessModel(ruleId,
                businessModel);
        if (ObjectUtils.isNotEmpty(dmConfContractRuleVos)) {
            for (DmConfContractRuleVo dmConfContractRuleVo : dmConfContractRuleVos) {
                String ruleOperator = dmConfContractRuleVo.getRuleOperator();
                String firValue = dmConfContractRuleVo.getFirValue();
                String secValue = dmConfContractRuleVo.getSecValue();
                if (StringUtils.equals("null", firValue.toLowerCase())) {
                    firValue = "0";
                } else {
                    firValue = getRuleParsing(cmUnitId, firValue, businessModel);
                }

                if (StringUtils.equals("null", secValue)) {
                    secValue = "0";
                } else {
                    secValue = getRuleParsing(cmUnitId, secValue, businessModel);
                }

                if (firValue.startsWith("R:")) return firValue;
                if (secValue.startsWith("R:")) return secValue;

                Long operatorFlag = getOperator(firValue, ruleOperator, secValue);
                if (operatorFlag == 1) {
                    if (StringUtils.equals("1", dmConfContractRuleVo.getTrueType())) {
                        majorRisk = getFactorValueCal(cmUnitId, entityId, bussDefCode,
                                dmConfContractRuleVo.getTrueRuleId(), ruleType,
                                businessModel);
                    } else {
                        majorRisk = getRuleParsing(cmUnitId, dmConfContractRuleVo.getTrueValue(), businessModel);
                    }
                } else {
                    if (StringUtils.equals("1", dmConfContractRuleVo.getFalseType())) {
                        majorRisk = getFactorValueCal(cmUnitId, entityId, bussDefCode,
                                dmConfContractRuleVo.getFalseRuleId(), ruleType, businessModel);
                    } else {
                        majorRisk = getRuleParsing(cmUnitId, dmConfContractRuleVo.getFalseValue(), businessModel);
                    }
                }

            }
        }
        return majorRisk;*/
        return null;
        //TODO 异常信息
    }



    @Override
    public void insertBussCmUnitDtlLog(String traceNo, String traceCode, String traceStatus, String traceMsg) {

    }

    @Override
    public void backupProfitUnitResultTab(Long entityId, String yearMonth, String tableName) {

    }

    @Override
    public void syncProfitableRuleParam() {
        List<Runnable> listDelete = new ArrayList<>();

        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteQuotaDef();
        });
        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteQuota();
        });
        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteQuotaDetail();
        });
        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteInterestRate();
        });
        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteInterestRateDetail();
        });
        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteBussInterestMain();
        });
        listDelete.add(() -> {
            dmConfProfitableParamDao.deleteBussInterestDetail();
        });

        List<Exception> execute = ThreadUtils.execute(listDelete, ThreadConstant.RULE_THREAD_POOL, true);
        if (execute.size() != 0) {
            throw new DmCmunitException(execute.get(0).getMessage());
        }

        List<Runnable> listInsert = new ArrayList<>();
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertQuotaDef();
        });
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertQuota();
        });
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertQuotaDetail();
        });
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertInterestRate();
        });
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertInterestRateDetail();
        });
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertBussInterestMain();
        });
        listInsert.add(() -> {
            dmConfProfitableParamDao.insertBussInterestDetail();
        });
        List<Exception> executeInsert = ThreadUtils.execute(listInsert, ThreadConstant.RULE_THREAD_POOL, true);
        if (executeInsert.size() != 0) {
            throw new DmCmunitException(executeInsert.get(0).getMessage());
        }
    }
}
