package com.ss.ifrs.actuarial.util;

import com.ss.ifrs.actuarial.dao.AtrBussEcfDao;
import com.ss.ifrs.actuarial.dao.AtrBussLrcDao;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrBussExch;
import com.ss.library.utils.DatabaseTypeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;

import java.beans.PropertyDescriptor;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 预期现金流工具类
 * <AUTHOR>
 */
@Slf4j
public class EcfUtil {

    public static final String Q_LAPSE_RATIO = "#lrc_lapse_ratio";
    public static final String Q_LRC_CLAIM_SETTLED_PATTERN = "#lrc_claim_settled_pattern";
    public static final String Q_LRC_CLAIM_RATIO = "#lrc_claim_ratio";
    public static final String Q_LRC_MT_RATIO = "#lrc_mt_ratio";
    public static final String Q_LRC_ULAE_RATIO = "#lrc_ulae_ratio";
    public static final String Q_LRC_RA_RATIO = "#lrc_ra_ratio";

    public static final String Q_LIC_CLAIM_SETTLED_PATTERN = "#lic_claim_settled_pattern";
    public static final String Q_LIC_ULAE_RATIO = "#lic_ulae_ratio";
    public static final String Q_LIC_RA_RATIO = "#lic_ra_ratio";
    public static final String Q_LIC_RD_RATIO = "#lic_rd_ratio";

    public static final int THREADS_CALC;
    public static final int THREADS_DB;

    public static final int SCALE_MONEY = 4;
    public static final int SCALE_RATIO = 6;

    private static final AtomicInteger ACTION_AI = new AtomicInteger(ThreadLocalRandom.current().nextInt());

    static {
        int cpus = (int) Math.round((Runtime.getRuntime().availableProcessors() * 0.8));
        if (cpus > 4) {
            THREADS_DB = (int) Math.round(cpus * 0.66);
            THREADS_CALC = cpus - THREADS_DB;
        } else {
            THREADS_DB = 1;
            THREADS_CALC = 1;
        }
    }

    /**
     * 一定程度上全局唯一的 id ；  <br>
     * 很大程度上， 是有顺序的；   <br>
     * 可从中看到时间
     */
    public static String createActionNo() {
        String time = DateFormatUtils.format(new Date(), "yyyyMMdd_HHmmss");
        String ran = RandomStringUtils.randomAlphanumeric(2).toUpperCase();
        int incr = ACTION_AI.getAndIncrement() & 0x7FFF;
        String incrStr = StringUtils.leftPad(Integer.toString(incr, 36).toUpperCase(), 3, '0');
        return String.format("%s_%s%s", time, ran, incrStr);
    }

    /**
     * round 方法， 从 common-math 抄过来的， 避免增加依赖
     */
    public static double round(double x, int scale) {
        try {
            final double rounded = (new BigDecimal(Double.toString(x))
                    .setScale(scale, RoundingMode.HALF_UP))
                    .doubleValue();
            // MATH-1089: negative values rounded to zero should result in negative zero
            return rounded == 0d ? 0d * x : rounded;
        } catch (NumberFormatException ex) {
            if (Double.isInfinite(x)) {
                return x;
            } else {
                return Double.NaN;
            }
        }
    }

    /**
     * 像直保、临分分出这种到单级别的 LRC 计算， 单量可能巨大， 需要分块处理。
     * 可以在系统配置里配置分多少块， 可通过这个方法获取这个配置。
     * 如果没有配置， 默认为 20
     */
    public static int getLrcConfParts(AtrBussLrcDao dao, String yearMonth, String businessSourceCode) {
        Integer parts = dao.getConfParts(yearMonth, businessSourceCode);
        if (parts == null) {
            parts = dao.getConfParts(yearMonth, "A");
        }
        return parts == null ? 20 : parts;
    }

    public static List<AtrBussExch> getExchList(AtrBussEcfDao dao, Long entityId,
                                                String yearMonth, String baseCurrencyCode) {
        Date deadline = new DateTime(Dates.toDate(yearMonth)).plusMonths(1).toDate();
        return dao.getExchList(entityId, deadline, baseCurrencyCode);
    }

//    public static int getThreadsDB() {
//        if (threadsDB <= 0) {
//            try {
//                JdbcTemplate jdbcTemplate = SpringContextUtil.getBean(JdbcTemplate.class);
//                threadsDB = jdbcTemplate.queryForObject("select * from t_threads_db", Integer.class);
//            } catch (Exception e) {
//                log.warn("error get threads for db", e);
//                threadsDB = 16;
//            }
//        }
//        return threadsDB <= 0 ? 16 : threadsDB;
//    }

    public static void analyseTable(JdbcTemplate jdbcTemplate, String table) {
        if (DatabaseTypeUtil.isPostgreSQL(jdbcTemplate)) {
            jdbcTemplate.execute("analyse " + table);
        }
    }

    public static Object readField(Object obj, String fieldName) {
        try {
            PropertyDescriptor pd = BeanUtils.getPropertyDescriptor(obj.getClass(), fieldName);
            assert pd != null;
            return pd.getReadMethod().invoke(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
