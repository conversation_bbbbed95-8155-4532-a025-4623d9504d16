package com.ss.ifrs.actuarial.util;

import java.util.List;

public class AtrExcelHead {
    String code;
    String label;
    String align;
    List<AtrExcelHead> childHeadList;

    public List<AtrExcelHead> getChildHeadList() {
        return childHeadList;
    }

    public void setChildHeadList(List<AtrExcelHead> childHeadList) {
        this.childHeadList = childHeadList;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getAlign() {
        return align;
    }

    public void setAlign(String align) {
        this.align = align;
    }


}
