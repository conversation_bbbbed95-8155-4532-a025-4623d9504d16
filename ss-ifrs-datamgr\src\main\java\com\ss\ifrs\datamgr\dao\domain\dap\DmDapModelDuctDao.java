package com.ss.ifrs.datamgr.dao.domain.dap;

import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelConditionVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmDapModelDuctDao {

    void createDuctTable(String ductTableName, String odsTableName);

    void insertDuctData(DapModelConditionVo dapModelConditionVo, String ductTableName, String odsTableName, SliceAttributes sliceAttributes);

    void addDuctTableKey(String ductTableName);

    void deleteOdsData(DapModelConditionVo dapModelConditionVo, String odsTableName, SliceAttributes sliceAttributes);

    void rewriteOdsStatus(String ductTableName, String odsTableName, String taskCode, SliceAttributes sliceAttributes);

    void createDuctView(String ductTableName, String ductViewName);

    void dropDuctView(String ductView);

}
