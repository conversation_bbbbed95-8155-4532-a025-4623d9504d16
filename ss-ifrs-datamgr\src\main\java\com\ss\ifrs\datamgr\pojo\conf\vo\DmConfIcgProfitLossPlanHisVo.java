/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-28 11:43:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.pojo.conf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-28 11:43:30<br/>
 * Description: 合同相关配置重大风险测试方案轨迹表<br/>
 * Table Name: dm_conf_icg_profitloss_planhis<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "合同相关配置重大风险测试方案轨迹表")
public class DmConfIcgProfitLossPlanHisVo implements Serializable {
    /**
     * Database column: dm_conf_icg_profitloss_planhis.loss_plan_his_id
     * Database remarks: Loss Plan His Id|主键
     */
    @ApiModelProperty(value = "Loss Plan His Id|主键", required = true)
    private Long lossPlanHisId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.loss_plan_id
     * Database remarks: Loss Plan Id|重大风险测试方案表主键
     */
    @ApiModelProperty(value = "Loss Plan Id|重大风险测试方案表主键", required = true)
    private Long lossPlanId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.entity_id
     * Database remarks: Entity Unit|业务单位
     */
    @ApiModelProperty(value = "Entity Unit|业务单位", required = false)
    private Long entityId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.business_model
     * Database remarks: Business_Model|业务模型：DD-直保 TD-合约 FI-分入 FO-分出
     */
    @ApiModelProperty(value = "Business_Model|业务模型：DD-直保 TD-合约 FI-分入 FO-分出", required = false)
    private String businessModel;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.business_direction
     * Database remarks: Business_Direction|业务方向：D-不区分 I-分入 O-分出
     */
    @ApiModelProperty(value = "Business_Direction|业务方向：D-不区分 I-分入 O-分出", required = false)
    private String businessDirection;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.test_plan_type
     * Database remarks: Test Plan Type|方案类型
     */
    @ApiModelProperty(value = "Test Plan Type|方案类型", required = false)
    private String testPlanType;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.loss_plan_version
     * Database remarks: Loss Plan Version|版本号
     */
    @ApiModelProperty(value = "Loss Plan Version|版本号", required = false)
    private BigDecimal lossPlanVersion;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.audit_state
     * Database remarks: Audit State|审核状态
     */
    @ApiModelProperty(value = "Audit State|审核状态", required = false)
    private String auditState;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.checked_time
     * Database remarks: Checked Time|审核时间
     */
    @ApiModelProperty(value = "Checked Time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.checked_id
     * Database remarks: Checked Id|审核人
     */
    @ApiModelProperty(value = "Checked Id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.checked_msg
     * Database remarks: Checked Msg|审核意见
     */
    @ApiModelProperty(value = "Checked Msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.valid_is
     * Database remarks: Valid Is|有效标志
     */
    @ApiModelProperty(value = "Valid Is|有效标志", required = false)
    private String validIs;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.create_time
     * Database remarks: Create Time|创建时间
     */
    @ApiModelProperty(value = "Create Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.creator_id
     * Database remarks: Creator Id|创建人
     */
    @ApiModelProperty(value = "Creator Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.update_time
     * Database remarks: Update Time|最后变更时间
     */
    @ApiModelProperty(value = "Update Time|最后变更时间", required = false)
    private Date updateTime;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.updator_id
     * Database remarks: Updator Id|最后变更经手人
     */
    @ApiModelProperty(value = "Updator Id|最后变更经手人", required = false)
    private Long updatorId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.oper_type
     * Database remarks: oper_Type|操作类型 ：1-add；2-Modiffy；3-delete；4-audit;
     */
    @ApiModelProperty(value = "oper_Type|操作类型 ：1-add；2-Modiffy；3-delete；4-audit;", required = false)
    private String operType;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.oper_id
     * Database remarks: oper_Id|操作人
     */
    @ApiModelProperty(value = "oper_Id|操作人", required = false)
    private Long operId;

    /**
     * Database column: dm_conf_icg_profitloss_planhis.oper_time
     * Database remarks: oper_Time|操作时间
     */
    @ApiModelProperty(value = "oper_Time|操作时间", required = false)
    private Date operTime;

    private static final long serialVersionUID = 1L;

    public Long getLossPlanHisId() {
        return lossPlanHisId;
    }

    public void setLossPlanHisId(Long lossPlanHisId) {
        this.lossPlanHisId = lossPlanHisId;
    }

    public Long getLossPlanId() {
        return lossPlanId;
    }

    public void setLossPlanId(Long lossPlanId) {
        this.lossPlanId = lossPlanId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getBusinessDirection() {
        return businessDirection;
    }

    public void setBusinessDirection(String businessDirection) {
        this.businessDirection = businessDirection;
    }

    public String getTestPlanType() {
        return testPlanType;
    }

    public void setTestPlanType(String testPlanType) {
        this.testPlanType = testPlanType;
    }

    public BigDecimal getLossPlanVersion() {
        return lossPlanVersion;
    }

    public void setLossPlanVersion(BigDecimal lossPlanVersion) {
        this.lossPlanVersion = lossPlanVersion;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }
}