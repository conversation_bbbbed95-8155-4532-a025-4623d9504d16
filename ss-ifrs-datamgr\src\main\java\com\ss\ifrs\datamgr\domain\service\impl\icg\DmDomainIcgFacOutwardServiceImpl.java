package com.ss.ifrs.datamgr.domain.service.impl.icg;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfContractRiskDefDao;
import com.ss.ifrs.datamgr.dao.dap.rein.DmReinsOutwardDao;
import com.ss.ifrs.datamgr.dao.dap.rein.DmReinsOutwardDetailDao;
import com.ss.ifrs.datamgr.dao.dap.rein.DmReinsTreatyDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainIcg;
import com.ss.ifrs.datamgr.domain.exception.DmCmunitException;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgService;
import com.ss.ifrs.datamgr.domain.service.icg.cmunitno.DmDomainCmUnitNoService;
import com.ss.ifrs.datamgr.domain.service.icg.majorrisk.DmDomainMajorRiskService;
import com.ss.ifrs.datamgr.feign.BmsActProcFeignClient;
import com.ss.ifrs.datamgr.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitFacOutwards;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRiskDefVo;
import com.ss.ifrs.datamgr.pojo.dap.vo.rein.DmReinsOutwardVo;
import com.ss.ifrs.datamgr.pojo.dap.vo.rein.DmReinsTreatyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCreateCmUnitNoVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitDirectMajorTestVo;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitFacOutwardsService;
import com.ss.ifrs.datamgr.service.ods.OdsReinsOutwardService;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.pojo.com.po.SliceAttributes;
import com.ss.platform.util.DataSliceUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Service(DmConstant.BusinessModel.MODEL_FAC_OUT + "ICG")
public class DmDomainIcgFacOutwardServiceImpl extends DmDomainIcg implements DmDomainIcgService {

    @Autowired
    DmDomainBussPeriodService dmDomainBussPeriodService;

    @Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Autowired
    DmBussCmunitFacOutwardsService dmBussCmunitFacOutwardsService;

    @Qualifier(DmConstant.BusinessModel.MODEL_FAC_OUT + "MajorTest")
    @Autowired
    private DmDomainMajorRiskService dmDomainMajorRiskService;

    @Autowired
    BmsActProcFeignClient bmsActProcFeignClient;
    @Qualifier(DmConstant.BusinessModel.MODEL_FAC_OUT)
    @Autowired
    DmDomainCmUnitNoService domainCmUnitNoService;
    @Autowired
    private OdsReinsOutwardService odsReinsOutwardService;
    @Autowired
    private AppConfig appConfig;

    @Qualifier(CommonConstant.BussPeriod.PeriodStatus.COMPLETED)
    @Autowired
    private DmDomainPeriodService dmDomainPeriodService;
    @Autowired
    private DmReinsOutwardDao dmReinsOutwardDao;
    @Autowired
    private DmReinsOutwardDetailDao dmReinsOutwardDetailDao;
    @Autowired
    private DmReinsTreatyDao dmReinsTreatyDao;

    @Autowired
    private DmConfContractRiskDefDao dmConfContractRiskDefDao;

    @Autowired
    private ExecutorService ruleExecutorService; // 替换为 Spring 管理的线程池

    @Override
    public void cmunitIdentify(DmIcgProcVo dmIcgProcVo) {
        if (dmIcgProcVo == null) {
            throw new DmCmunitException("Parameter dmIcgProcVo is null");
        }
        if (dmIcgProcVo.getEntityId() == null || StringUtil.isEmpty(dmIcgProcVo.getYearMonth())){
            throw new DmCmunitException("entityId or yearMonth is empty");
        }

        //获取长短险标识生成是否开启 0-不开启
        this.getShortRiskFlag(dmIcgProcVo);
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE);
        dmIcgProcVo.setProcId(procId);
        //生成计量单元
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.insertCmunitIdentify(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateCmunitOtherField(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

        //修改已生成计量单元标识
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> odsReinsOutwardService.updateTaskStatusByCmunitNo(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
//        DmCreateCmUnitNoVo cmUnitNoVo = new DmCreateCmUnitNoVo();
//        cmUnitNoVo.setEntityId(dmIcgProcVo.getEntityId());
//        cmUnitNoVo.setYearMonth(dmIcgProcVo.getYearMonth());
//        List<DmReinsOutwardVo> reinsOutwardList = dmReinsOutwardDao.findReinsByEntityId(cmUnitNoVo);
//        if(reinsOutwardList!=null && reinsOutwardList.size() > 0) {
//            reinsOutwardList.forEach(reinsOutward -> {
//                String businessSubType = "";
//                if ("91".equals(reinsOutward.getTreatyTypeCode())) {
//                    businessSubType = "7";
//                } else if ("92".equals(reinsOutward.getTreatyTypeCode())) {
//                    businessSubType = "8";
//                }
//                String cmUnitNo = businessSubType + reinsOutward.getRiPolicyNo();
//                dmIcgProcVo.setCmUnitNo(cmUnitNo);
//                dmIcgProcVo.setBusinessSubdivisionType(businessSubType);
//                dmIcgProcVo.setCmunitAdapterNo(reinsOutward.getRiPolicyNo());
//
//            });
//        }
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_UNIT_RECOGNIZER)
    @Override
    public void cmunitDetermine(DmIcgProcVo dmIcgProcVo) {
        validParam(dmIcgProcVo);


        this.getTicCode(dmIcgProcVo);
        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE);
        dmIcgProcVo.setProcId(procId);

        //loa重新适配
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateCmunitAdapterWithoutCode(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

        if (StringUtil.isNotEmpty(dmIcgProcVo.getTicCodeConf())) {
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateCmunitAdapterWithCode(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());
        }

        //loa未配置提示
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateCmunitAdapterLoaFail(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_RISK_TEST)
    @Override
    public void majorRisk(DmIcgProcVo dmIcgProcVo) {
        // 校验参数是否有效
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.MAJOR_RISK_NODE);
        dmIcgProcVo.setProcId(prodNode);
        dmIcgProcVo.setBusinessModel(DmConstant.BusinessModel.MODEL_FAC_OUT);
        //查找重大风险方案的配置
        String majorRiskPlan = this.getMajorRiskPlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_FAC_OUT);
        //清除重风测试数据
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateMajorResultNull(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        switch (majorRiskPlan) {
            //全部通过
            case DmConstant.MajorRiskPlan.ALL_PASS:
                DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateMajorRiskAllPass(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                        , appConfig.getDataSliceQuantity());
                break;
            //配置+规则
            case DmConstant.MajorRiskPlan.ADAPTER_CONFIG_RULE:
                //配置
                DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateMajorRiskAdapterConfig(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                        , appConfig.getDataSliceQuantity());
                //规则 查询重风结果为'C'线上测试的计量单元
                List<DmCmunitDirectMajorTestVo> dmCmunitDirectMajorTestVos = dmBussCmunitFacOutwardsService.listCmunitFacMajorTestInfo(dmIcgProcVo);
                // 在方法内部定义开始时间
                long startTime = System.currentTimeMillis();

                // 提交任务到线程池
                for (DmCmunitDirectMajorTestVo item : dmCmunitDirectMajorTestVos) {
//                    if (ruleExecutorService.isTerminated()) {
//                        ruleExecutorService = Executors.newFixedThreadPool(10); // 重新初始化
//                    }
//                    ruleExecutorService.submit(() -> {
                        String majorResult = dmDomainMajorRiskService.getFactorValueCalc(item.getCmunitId(), dmIcgProcVo.getEntityId(), item.getPolicyNo(), null, "1", DmConstant.BusinessModel.MODEL_FAC_OUT, dmIcgProcVo.getYearMonth(),null);
                        item.setMajorResult(majorResult);
                        item.setProcId(dmIcgProcVo.getProcId());
                        dmBussCmunitFacOutwardsService.updateMajorResultByCmunitId(item);
//                    });
                }

//                // 等待所有任务完成（可选）
//                ruleExecutorService.shutdown();
//                while (!ruleExecutorService.isTerminated()) {
//                    // 等待所有线程执行完毕
//                }
//
//                // 记录结束时间并打印执行时间
//                long endTime = System.currentTimeMillis();
//                System.out.println("Start Time: " + new java.util.Date(startTime));
//                System.out.println("End Time: " + new java.util.Date(endTime));
//                System.out.println("Total Execution Time: " + (endTime - startTime) + " ms");
                break;
            default:
                break;
        }
    }

    @Override
    public void majorRiskNoPass(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //是否存在重测业务不通过
        Integer majorRiskBussNoPassCount = dmBussCmunitFacOutwardsService.getMajorRiskBussNoPassCount(dmIcgProcVo);
        if (majorRiskBussNoPassCount < 0) {
            return;
        }

        String portfolioNo = this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_FAC_OUT);
        Long portfolioId = this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE);
        dmIcgProcVo.setPortfolioColumn(portfolioNo);
        dmIcgProcVo.setPortfolioProcId(portfolioId);
        if (StringUtil.isNotEmpty(portfolioNo)) {
            String icgNo = this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_FAC_OUT);
            Long icgProcId = this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE);
            dmIcgProcVo.setIcgColumn(icgNo);
            dmIcgProcVo.setIcgProcId(icgProcId);
        }

        //评估方法与盈亏设置不区分，合同组合，合同组
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateMajorNoPass(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_EVALDEF_CONFIG)
    @Override
    public void evaluateApproach(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.EVALUATE_APPROACH_NODE);
        dmIcgProcVo.setProcId(prodNode);

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateEvaluateApproach(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateEvaluateApproachFail(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_PORTFOLIO)
    @Override
    public void portfolioDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);


        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE);
        dmIcgProcVo.setProcId(prodNode);
        String icgNo = this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_FAC_OUT);
        dmIcgProcVo.setPortfolioColumn(icgNo);

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updatePortfolioNo(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_DETERMINATION)
    @Override
    public void profitableEstimate(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //this.syncProfitableRuleParam();
        //如果查询当月没有数据做，直接返回
        Integer needProfitableEstimateDataNum = dmBussCmunitFacOutwardsService.queryNeedProfitableEstimateData(dmIcgProcVo);
        if (needProfitableEstimateDataNum <= 0) {
            return;
        }
        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.PROFITABLE_ESTIMATE_NODE);
        dmIcgProcVo.setProcId(prodNode);
        //初始化盈亏状态
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateProfitableEstimateInit(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //查询盈亏判断配置方案
        String profitableEstimatePlan = this.getProfitableEstimatePlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_FAC_OUT);

        //适配配置
        if (profitableEstimatePlan.equals(DmConstant.LossRiskPlan.ADAPTER_RULE)) {
            //规则
        } else {
            //配置
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateProfitableEstimateAdapterConfig(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());
        }
    }

    @Override
    public void icgBorderSeal(DmIcgProcVo dmIcgProcVo) {
        //获取再保前D2、D3、D4
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgBorderSealD2(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgBorderSealD3(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgBorderSealD4(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //得到D5
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgBorderSealD5(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //合同确认日期
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgBorderSeal(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACTGROUP)
    @Override
    public void icgDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //合同确认日期
        this.icgBorderSeal(dmIcgProcVo);

        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE);
        dmIcgProcVo.setProcId(prodNode);

        String icgNo = this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_FAC_OUT);
        dmIcgProcVo.setIcgColumn(icgNo);

        //合同分组
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgNo(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_INVESTMENT_COST)
    @Override
    public void icgInvestment(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);
        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.ICG_INVESTMENT_NODE);
        dmIcgProcVo.setProcId(prodNode);

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACT_CONFIRMATION)
    @Override
    public void icgConfirm(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.DM_CONTRACT_CONFIRMATION);
        dmIcgProcVo.setProcId(procId);

        //合同确认
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitFacOutwardsService.updateIcgYearMonth(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        // 执行完成后更新详情状态
        dmDomainPeriodService.executionDetail(dmIcgProcVo.getEntityId()
                , dmIcgProcVo.getYearMonth(), DmConstant.BussCmunit.CMUNIT_FAC_OUT);
    }


}
