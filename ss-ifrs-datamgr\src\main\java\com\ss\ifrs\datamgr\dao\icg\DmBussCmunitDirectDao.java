package com.ss.ifrs.datamgr.dao.icg;

import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitDirect;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectSelectVo;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmunitMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitDirectMajorTestVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Create Date: 2021-01-14 20:40:00<br/>
 * Description: 计量单元表 Dao类<br/>
 * Related Table Name: DM_BUSS_CMUNIT_DIRECT<br/>
 * <br/>
 * Remark:
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmBussCmunitDirectDao extends IDao<DmBussCmunitDirect, Long> {
    Page<DmBussCmunitDirectSelectVo> findDataRecord(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo, Pageable pageParam);

    Long findStatisticsData(DmBussCmunitDirectVo cmunitVo);

    DmBussCmunitDirect findCmunitVoByqueryMethod(DmBussCmunitDirectSelectVo dmBussCmunitDirectSelectVo);

    Page<DmBussCmunitDirectVo> findDmBussCmunit(DmDataSearchVo searchVo, Pageable pageParam);

    List<String> findPolicyList(DmDataSearchVo dmDataSearchVo);

    BbsConfEntityVo findEntityById(Long entityId);

    String findDmOwActionLog(DmBussCmunitDirectVo dmBussCmunit);

    Page<DmBussCmunitDirectVo> findDmBussCmunitProt(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmBussCmunitDirectVo> findPortfolioNo(DmBussCmunitDirectVo dmBussCmunitDirectVo, Pageable pageParam);



    // 合同分组流程

    /**
     * 计量单元划分
     */
    void insertCmunitIdentify(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void insertCmunitIdentifyRiskClass2(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateCmunitAdapterWithoutCode(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateCmunitAdapterWithCode(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);


    void updateCmunitAdapterLoaFail(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 重大风险测试
     */

    void updateMajorRiskAllPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateMajorRiskAdapterConfig(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

//    void updateMajorRiskAdapterRule(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateMajorRiskBussNoPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 重大风险测试配置+规则方案,查不到配置或配置为否默认全部通过
     */
    void updateMajorRiskNoConfPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 重大风险测试不通过的处理方案
     */

    Integer getMajorRiskBussNoPassCount(DmIcgProcVo dmIcgProcVo);

    void updateMajorNoPass(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 评估方法适配
     */
    void updateEvaluateApproach(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 合同组合划分
     */
    void updatePortfolioDiscern(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 盈亏判定
     */
    void updateProfitableEstimate(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    Integer getProfitableEstimateValidCount(DmIcgProcVo dmIcgProcVo);

    void updateProfitableEstimateInit(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);


    void updateProfitableEstimateAdapterConfig(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void deleteProfitableEstimateProfitUnit(DmIcgProcVo dmIcgProcVo);

    void deleteProfitableEstimateProfitParam(DmIcgProcVo dmIcgProcVo);

    void insertProfitableEstimateProfitUnit(DmIcgProcVo dmIcgProcVo);

    Integer getProfitableEstimateProfitUnitCount(DmIcgProcVo dmIcgProcVo);
    /**
     * 合同确认
     */
    void updateIcgBorderSeal(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD1(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD2(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD3(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD4(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD5(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    void updateIcgBorderSealD6(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);


    /**
     * 合同组划分
     */
    void updateIcgDiscern(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 投成拆分
     */
    void updateIcgInvestment(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    Integer getIcgInvestmentValid(DmIcgProcVo dmIcgProcVo);

    void updateIcgInvestmentFail(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    /**
     * 合同分组完成
     */
    void updateIcgYearMonth(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);

    String getCmunitInfoById(DmCmunitMajorTestVo dmCmunitMajorTestVo);

    DmCmunitMajorTestVo getDmCmunitMajorTestVo(Long cmunitId);

    List<DmCmunitDirectMajorTestVo> listCmunitDirectMajorTestInfo(DmIcgProcVo dmIcgProcVo);

    Long getCmunitDirectMajorTestCount(DmIcgProcVo dmIcgProcVo);

    void updateMajorResultByCmunitId(@Param("item") DmCmunitDirectMajorTestVo dmCmunitDirectMajorTestVo);

    /**
     * 获取关联配置的计量单元数据
     * @param  dmIcgProcVo
     * @return
     */
    List<DmBussCmunitDirect> findBussCmunitConf(DmIcgProcVo dmIcgProcVo);


    void updateMajorResultNull(DmIcgProcVo dmIcgProcVo, SliceAttributes sliceAttributes);
}