package com.ss.ifrs.actuarial.util;

import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelParseVo;
import com.ss.library.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.el.parser.ParseException;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import java.io.InputStream;
import java.rmi.NotBoundException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class ExcelImportUtils {

    public static List<List<AtrExcelParseVo>> parseExcel(InputStream fs, List<AtrExcelParseVo> parseList, Integer sheetNo) throws Exception {
        List<List<AtrExcelParseVo>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(parseList)) {
            throw new NotBoundException("Upload template does not match the actual number of fields!");
        }
        //创建工作簿
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fs);
        if (ObjectUtils.isNotEmpty(sheetNo) && sheetNo <0) {
            sheetNo = 0;
        }
        //读取第一个工作表
        XSSFSheet sheet = xssfWorkbook.getSheetAt(sheetNo);
        //获取最后一行的num，即总行数。此处从1开始计数
        int maxRow = sheet.getLastRowNum();
        
        int maxRol = sheet.getRow(0).getPhysicalNumberOfCells();
        int size = parseList.size();
        if (maxRol != size) {
            throw new ParseException("Parse Exception : Templates not up to date, Please download the latest template");
        }
        try {
            xssfRow : for (int row = 1; row <= maxRow; row++) {
                List<AtrExcelParseVo> rowList = new ArrayList<>();
                //获取最后单元格num，即总单元格数 ***注意：此处从1开始计数***

                //空行处理
                XSSFRow xssfRow= sheet.getRow(row);
                boolean rowEmpty = ExcelImportUtils.isRowEmpty(xssfRow, maxRol);
                if(rowEmpty){
                    continue xssfRow;
                }

                int rol = 0;
                for (; rol < size; rol++) {
                    AtrExcelParseVo parseVo = new AtrExcelParseVo();
                    AtrExcelParseVo atrParseExcelVo = new AtrExcelParseVo();
                    BeanUtils.copyProperties(parseList.get(rol),atrParseExcelVo);

                    XSSFCell cell = xssfRow.getCell(rol);

                    //获取当前表头
                    XSSFCell cell1 = sheet.getRow(0).getCell(rol);
                    String cellHead = "";
                    if (cell1 != null && !CellType.BLANK.equals(cell1.getCellType())) {
                        cellHead = cell1.getStringCellValue();
                    }

                    //判断是否必录
                    if ("1".equals(atrParseExcelVo.getNeedIs()) && (cell == null || CellType.BLANK.equals(cell.getCellType()))) {
                        throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " Can't be empty");
                    }

                    String colType = atrParseExcelVo.getColType();
                    String colLength = atrParseExcelVo.getColLength();
                    String charsetName = "UTF-8";
                    if (ObjectUtils.isNotEmpty(cell)) {
                        if ("TIMESTAMP".equalsIgnoreCase(colType) || "DATE".equalsIgnoreCase(colType)) {
                            String dateStr;
                            // 不在正确的日期范围1970/1/1 ~ 9999/12/31，其始终转字符分别为25569、2958465
                            long startLong = new Long(25569);
                            long endLong = new Long(2958465);

                            try {
                                atrParseExcelVo.setColValue(cell.getDateCellValue());
                                // 重新设置单元格格式为String。如同excel日期格式设置文本格式
                                cell.setCellType(CellType.STRING);
                                dateStr = cell.getStringCellValue();
                                // 排除空或空字符情况
                                if(StringUtil.isNotEmpty(dateStr)){
                                    if(Long.parseLong(dateStr) - startLong < 0 ){
                                        // 单元格非法日期类型时，执行下方语句自动获取catch部分异常
                                        atrParseExcelVo.setColValue(cell.getDateCellValue());
                                    }
                                    if(Long.parseLong(dateStr) - endLong > 0 ){
                                        // 单元格非法日期类型时，执行下方语句自动获取catch部分异常
                                        atrParseExcelVo.setColValue(cell.getDateCellValue());
                                    }
                                }
                            } catch (IllegalStateException e) {
                                throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " value is not of type date!");
                            } catch (NumberFormatException e) {
                                throw new NumberFormatException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead +" Incorrect date type");
                            } catch (Exception e){
                                new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead +" "+ e.getMessage());
                            }
                        } else if ("NUMERIC".equalsIgnoreCase(colType) || "NUMBER".equalsIgnoreCase(colType) || "INTEGER".equalsIgnoreCase(colType)) {
                            double numericCellValue;
                            String cellValue;
                            try {
                                cellValue =  NumberToTextConverter.toText(cell.getNumericCellValue());
                                numericCellValue = Double.valueOf(cellValue);
                            } catch (IllegalStateException e) {
                                throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " value is not of type number!");
                            }
                            //长度校验
                            if (StringUtils.isEmpty(colLength)) {
                                throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " length beyond 0 ");
                            }
                            //String cellValue = String.valueOf(numericCellValue);
                            String[] split = colLength.split(",");
                            //数字长度9,2
                            if (split.length > 1) {
                                Integer length = Integer.valueOf(split[0]);
                                Integer length2 = Integer.valueOf(split[1]);
                                int precisionLength = length - length2;
                                String[] split1 = cellValue.split("\\.");
                                if (split1.length > 1) {
                                    byte[] bytes = split1[0].getBytes(charsetName);
                                    byte[] bytes2 = split1[1].getBytes(charsetName);
                                    if (bytes.length > precisionLength || bytes2.length > length2) {
                                        throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " length beyond " + colLength);
                                    }
                                }else{
                                    if (cellValue.length() > precisionLength) {
                                        throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " length beyond " + colLength);
                                    }
                                }
                            } else {
                                Integer length = Integer.valueOf(split[0]);
                                String[] split1 = cellValue.split("\\.");
                                if (split1.length > 0) {
                                    byte[] bytes = split1[0].getBytes(charsetName);
                                    if (bytes.length > length) {
                                        throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " length beyond " + length);
                                    }
                                }
                            }

                            atrParseExcelVo.setColValue(numericCellValue);
                        } else {
                            String stringCellValue;
                            try {
                                stringCellValue = cell.getStringCellValue();
                            } catch (IllegalStateException e) {
                                throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " value is not of type text!");
                            }
                            //长度校验
                            if (StringUtils.isNotEmpty(stringCellValue)) {
                                stringCellValue = stringCellValue.trim();
                                byte[] bytes = stringCellValue.getBytes(charsetName);
                                Integer length = Integer.valueOf(colLength);
                                if (bytes.length > length) {
                                    throw new ParseException("row : " + (row + 1) + ", column : " + (rol + 1) + ", " + cellHead + " length beyond " + length);
                                }
                            }

                            atrParseExcelVo.setColValue(stringCellValue);
                        }
                    }
                    //处理单元格为空串问题
                    if (StringUtil.isEmpty(String.valueOf(atrParseExcelVo.getColValue()).trim())) {
                        atrParseExcelVo.setColValue(null);
                    }
                    BeanUtils.copyProperties(atrParseExcelVo, parseVo);
                    rowList.add(parseVo);
                }
                result.add(rowList);
            }
        }catch (ParseException e){
            throw e;
        } finally{
            if (xssfWorkbook != null) {
                xssfWorkbook.close();
            }
            if (fs != null) {
                fs.close();
            }
        }
        return result;
    }

    private static  boolean isRowEmpty(XSSFRow row,int headCell){
        if(row==null){
            return true;
        }
        int countCell = 0;
        for (int i = 0; i < headCell; i++) {
            XSSFCell cell = row.getCell(i);
            if(cell == null || CellType.BLANK.equals(cell.getCellType())){
                countCell++;
            }
        }
        if(countCell==headCell){
            return true;
        }
        return  false;
    }

}