package com.ss.ifrs.datamgr.dao;

import com.ss.ifrs.datamgr.pojo.quota.other.vo.DmSyncDataToAccVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface DmQueryDao {

    /**
     * 查询计量单元的合同确认日期与收付数据的凭证日期最大的一个
     * @param vo
     * @return
     */
    String queryMaxDateCmUnitOrAccPaymentByYearMonth(@Param("vo") DmSyncDataToAccVo vo);
}
