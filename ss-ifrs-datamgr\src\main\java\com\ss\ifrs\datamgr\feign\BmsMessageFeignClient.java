package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.conf.FeignAuthConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 发送邮件Feign调用
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsMessageFeignClient {

    @RequestMapping(method = RequestMethod.GET, value = "/dynamics/sendFileChangeExceptionNotice")
    BaseResponse<Map<String, Object>> fileChangeNotice(@RequestParam("noticeMessage") String noticeMessage);
}
