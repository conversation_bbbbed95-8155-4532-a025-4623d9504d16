package com.ss.ifrs.datamgr.domain.abst;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.domain.exception.DmCmunitException;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.common.DmDomainCommonService;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgCommonService;
import com.ss.ifrs.datamgr.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractCodeVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.log.po.DmLogBussCmUnit;
import com.ss.ifrs.datamgr.service.conf.DmConfContractCodeService;
import com.ss.ifrs.datamgr.service.conf.DmConfIcgMajorRiskPlanService;
import com.ss.ifrs.datamgr.service.conf.DmConfIcgProfitLossPlanService;
import com.ss.ifrs.datamgr.service.log.DmBussCmunitLogService;
import com.ss.library.utils.StringUtil;
import com.ss.platform.pojo.com.po.ConfCode;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;


public abstract class DmDomainIcg {

    @Autowired
    private DmConfContractCodeService confContractCodeService;
    @Autowired
    private DmDomainCommonService dmDomainCommonService;
    @Autowired
    private DmConfIcgMajorRiskPlanService dmConfIcgMajorRiskPlanService;

    @Autowired
    private DmDomainBussPeriodService dmDomainBussPeriodService;

    @Autowired
    private BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Autowired
    private DmBussCmunitLogService dmBussCmunitLogService;

    @Autowired
    private DmConfIcgProfitLossPlanService dmConfIcgProfitLossPlanService;

    @Autowired
    private DmDomainIcgCommonService dmDomainIcgCommonService;

    /**
     * 获取合同组合以及合同组编码
     *
     * @param entityId      部门id
     * @param codeType      编码类型G or C
     * @param businessModel 业务类型
     * @return 对应的脚本
     */
    public String getContractCode(Long entityId, String codeType, String businessModel) {
        String result = "";
        StringBuffer sb = new StringBuffer();
        List<DmConfContractCodeVo> dmConfContractCodeVos = confContractCodeService.getConfContractCode(entityId,
                codeType, businessModel);
        if (ObjectUtils.isNotEmpty(dmConfContractCodeVos)) {
            for (DmConfContractCodeVo dmConfContractCodeVo : dmConfContractCodeVos) {
                if (StringUtils.equals("1", dmConfContractCodeVo.getMatchType())) {
                    // 取字段值
                    sb.append(confContractCodeService.getSegment1st(dmConfContractCodeVo,result));
                } else if (StringUtils.equals("5", dmConfContractCodeVo.getMatchType())) {
                    // 码值转换\特殊
                    sb.append(confContractCodeService.getSegment2nd(dmConfContractCodeVo,result));
                } else if (StringUtils.equals("3", dmConfContractCodeVo.getMatchType())) {
                    if (StringUtils.equals("3", dmConfContractCodeVo.getColumnType())) {
                        sb.append(confContractCodeService.getSegment3rd(dmConfContractCodeVo,result));
                    } else {
                        sb.append(confContractCodeService.getSegment4td(dmConfContractCodeVo,result));
                    }
                } else if (StringUtils.equals("4", dmConfContractCodeVo.getMatchType())) {
                    // 预留字段
                    sb.append(confContractCodeService.getSegment5td(dmConfContractCodeVo,result));
                }
            }

            result = sb.toString();
            // 去除多余的 ||
            if (result.length() > 1) {
                result = StringUtils.substring(result, 0, result.length() - 2);
            }
        }
        return result;
    }

    /**
     * 获取节点
     * @param prodNode
     */
    public Long getProdNode(String prodNode){
        ActOverviewVo param = new ActOverviewVo();
        param.setProcCode(prodNode);
        ActOverviewVo byCode = dmDomainCommonService.getActOverviewVoByObject(param);
        if(byCode != null) {
            return byCode.getProcId();
        }
        return null;
    }

    /**
     * 获取重大风险测试方案
     * @param entityId
     * @param businessModel
     * @return
     */
    public String getMajorRiskPlan(Long entityId,String businessModel){
        String majorRiskPlan =  dmConfIcgMajorRiskPlanService.getMajorRiskPlan(entityId, businessModel);
        if (majorRiskPlan == null) {
            majorRiskPlan = DmConstant.MajorRiskPlan.ALL_PASS;
        }
        return majorRiskPlan;
    }

    /**
     * 获取盈亏判定方案
     * @param entityId
     * @param businessModel
     * @return
     */
    public String getProfitableEstimatePlan(Long entityId, String businessModel) {
        String planType = dmConfIcgProfitLossPlanService.getProfitableEstimatePlan(entityId, businessModel);
        if (StringUtil.isEmpty(planType)) {
            planType = DmConstant.LossRiskPlan.ADAPTER_CONFIG;
        }
        return planType;
    }

    public void syncProfitableRuleParam() {
        dmDomainIcgCommonService.syncProfitableRuleParam();
    }

    /**
     * 校验参数有效性
     *
     * @param dmIcgProcVo
     * @return
     */
    public void validParam(DmIcgProcVo dmIcgProcVo) {
        if (dmIcgProcVo == null) {
            throw new DmCmunitException("Parameter dmIcgProcVo is null");
        }
        Long entityId = dmIcgProcVo.getEntityId();
        String yearMonth = dmIcgProcVo.getYearMonth();

        if (entityId == null || StringUtil.isEmpty(yearMonth)){
            throw new DmCmunitException("entityId or yearMonth is empty");
        }
        // 计量单元生成的时候无需校验业务区间状态
        if ( StringUtil.isNotEmpty(dmIcgProcVo.getTaskCode()))
            return;
        //查询处理中业务期间
        String currentYearMonth = dmDomainBussPeriodService.getCurrentYearMonth(entityId);
        if(StringUtil.isEmpty(currentYearMonth) || !yearMonth.equals(currentYearMonth)){
            throw new DmCmunitException("The current business interval is not being processed");
        }
    }

    /**
     * 获取长短线标识
     * @param dmIcgProcVo
     */
    public void getShortRiskFlag(DmIcgProcVo dmIcgProcVo){

        //获取长短险标识生成是否开启 0-不开启
        List<ConfCode> shortRiskFlagCode = bmsConfCodeFeignClient.findCodeByCodeType(DmConstant.BplCodeCode.SHORT_RISK_FLAG_OPEN_IS);
        String shortRiskFlagOpenIs = "0";
        if (shortRiskFlagCode != null && shortRiskFlagCode.size() != 0) {
            shortRiskFlagOpenIs = shortRiskFlagCode.get(0).getValidIs();
        }
        dmIcgProcVo.setShortRiskFlagOpenIs(shortRiskFlagOpenIs);
    }

    /**
     * 获取TicCode
     * @param dmIcgProcVo
     */
    public void getTicCode(DmIcgProcVo dmIcgProcVo){
        //获取适配tic_code的值
        String ticCodeConf = null;
        List<ConfCode> ticCodeConfCode = bmsConfCodeFeignClient.findCodeByCodeType(DmConstant.BplCodeCode.TIC_CODE_CONF);
        if (ticCodeConfCode != null && ticCodeConfCode.size() != 0) {
            ticCodeConf = ticCodeConfCode.get(0).getCodeCode();
        }

        dmIcgProcVo.setTicCodeConf(ticCodeConf);
    }

    public void backProfitResult(DmIcgProcVo dmIcgProcVo,String businessModel) {
        String profitableEstimatePlan = getProfitableEstimatePlan(dmIcgProcVo.getEntityId(), businessModel);
        if (DmConstant.LossRiskPlan.ADAPTER_RULE.equals(profitableEstimatePlan)) {
            //todo 备份表
        }
    }

    public void saveCmunitLog(DmLogBussCmUnit dmLogBussCmUnit) {
        dmLogBussCmUnit.setBussModel(DmConstant.BusinessModel.MODEL_DIRECT);
        dmLogBussCmUnit.setCreateTime(new Date());
        dmBussCmunitLogService.save(dmLogBussCmUnit);
    }
}
