package com.ss.ifrs.datamgr.domain.service.impl.icg;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfContractRiskDefDao;
import com.ss.ifrs.datamgr.dao.dap.policy.DmPolicyMainDao;
import com.ss.ifrs.datamgr.dao.dap.policy.DmPolicyPremiumDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainIcg;
import com.ss.ifrs.datamgr.domain.exception.DmCmunitException;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.ifrs.datamgr.domain.service.bussperiod.DmDomainPeriodService;
import com.ss.ifrs.datamgr.domain.service.icg.DmDomainIcgService;
import com.ss.ifrs.datamgr.domain.service.icg.cmunitno.DmDomainCmUnitNoService;
import com.ss.ifrs.datamgr.domain.service.icg.majorrisk.DmDomainMajorRiskService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableBbaService;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitablePaaService;
import com.ss.ifrs.datamgr.feign.BbsConfAccountSetClient;
import com.ss.ifrs.datamgr.pojo.cmunit.vo.DmBussCmunitDirectVo;
import com.ss.ifrs.datamgr.pojo.cmunit.po.DmBussCmunitDirect;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRiskDefVo;
import com.ss.ifrs.datamgr.pojo.dap.po.policy.DmPolicyMain;
import com.ss.ifrs.datamgr.pojo.dap.po.policy.DmPolicyPremium;
import com.ss.ifrs.datamgr.pojo.dap.vo.policy.DmPolicyMainVo;
import com.ss.ifrs.datamgr.pojo.dap.vo.policy.DmPolicyPremiumVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.majortest.DmCmunitDirectMajorTestVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCreateCmUnitNoVo;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitDirectService;
import com.ss.ifrs.datamgr.service.ods.OdsPolicyMainService;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmUnitNoAdapterVo;
import com.ss.ifrs.datamgr.service.ods.OdsPolicyPremiumService;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.pojo.bbs.vo.BbsConfAccountSetVo;
import com.ss.platform.pojo.com.po.SliceAttributes;
import com.ss.platform.util.DataSliceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service(DmConstant.BusinessModel.MODEL_DIRECT + "ICG")
@Slf4j
public class DmDomainIcgDirectServiceImpl extends DmDomainIcg implements DmDomainIcgService {

    @Autowired
    AppConfig appConfig;

    @Autowired
    DmDomainBussPeriodService dmDomainBussPeriodService;


    @Qualifier(DmConstant.BusinessModel.MODEL_DIRECT)
    @Autowired
    DmDomainCmUnitNoService domainCmUnitNoService;

    @Autowired
    private DmBussCmunitDirectService dmBussCmunitDirectService;

    @Autowired
    private OdsPolicyMainService odsPolicyMainService;

    @Autowired
    private OdsPolicyPremiumService odsPolicyPremiumService;


    @Qualifier(CommonConstant.BussPeriod.PeriodStatus.COMPLETED)
    @Autowired
    private DmDomainPeriodService dmDomainPeriodService;

    @Qualifier(DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_PAA")
    @Autowired
    private DmDomainProfitablePaaService dmDomainProfitablePaaService;

    @Qualifier(DmConstant.BusinessModel.MODEL_DIRECT + "PROFITABLE_BBA")
    @Autowired
    private DmDomainProfitableBbaService dmDomainProfitableBbaService;

    @Autowired
    private BbsConfAccountSetClient bbsConfAccountSetClient;

    @Qualifier(DmConstant.BusinessModel.MODEL_DIRECT + "MajorTest")
    @Autowired
    private DmDomainMajorRiskService dmDomainMajorRiskService;

    @Autowired
    private DmConfContractRiskDefDao dmConfContractRiskDefDao;

    @Autowired
    private DmPolicyMainDao dmPolicyMainDao;

    @Autowired
    private DmPolicyPremiumDao dmPolicyPremiumDao;

    @Autowired
    private ExecutorService ruleExecutorService; // 替换为 Spring 管理的线程池

    /**
     * 生成计量单元
     * （在数据质量校验完成后执行）
     *
     * @param dmIcgProcVo
     */
    @TrackDataMgrProcess(DmConstant.ProcCode.DM_UNIT_RECOGNIZER)
    @Override
    public void cmunitIdentify(DmIcgProcVo dmIcgProcVo) {

        if (dmIcgProcVo == null) {
            throw new DmCmunitException("Parameter dmIcgProcVo is null");
        }
        if (dmIcgProcVo.getEntityId() == null || StringUtil.isEmpty(dmIcgProcVo.getYearMonth())){
            throw new DmCmunitException("entityId or yearMonth is empty");
        }

        DmCreateCmUnitNoVo cmUnitNoVo = new DmCreateCmUnitNoVo();
        cmUnitNoVo.setEntityId(dmIcgProcVo.getEntityId());
        cmUnitNoVo.setYearMonth(dmIcgProcVo.getYearMonth());
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE);
        dmIcgProcVo.setProcId(procId);
        //生成计量单元
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.insertCmunitIdentify(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //生成计量单元 一个保单对应多条风险信息,当保单险类为20时,一个保单可能会生成多个计量单元 ,此时要以保单号和拆分险类进行分组生成计量单元
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.insertCmunitIdentifyRiskClass2(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //修改已生成计量单元标识
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> odsPolicyMainService.updateTaskStatusByCmunitNo(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
       /* List<DmPolicyMainVo> policys = dmPolicyMainDao.findPolicyByEntityId(cmUnitNoVo);
        if(policys!=null && policys.size() > 0) {
            policys.forEach(policy -> {
                //业务细分类型(1)
                String businessSubType = "";
                if ("DB".equals(policy.getBusinessSourceCode())) {
                    businessSubType = "0";
                } else if ("FB".equals(policy.getBusinessSourceCode())) {
                    businessSubType = "1";
                }
                dmIcgProcVo.setBusinessSubdivisionType(businessSubType);
                // 获取长短线标志
                this.getShortRiskFlag(dmIcgProcVo);
                dmIcgProcVo.setCmunitAdapterNo(policy.getPolicyNo());
                if("20".equals(policy.getRiskClassCode())){
                    DmPolicyPremium policyPremium = new DmPolicyPremium();
                    policyPremium.setPolicyNo(policy.getPolicyNo());
                    policyPremium.setEndorseNo(policy.getEndorseNo());
                    policyPremium.setEndorseSeqNo(policy.getEndorseSeqNo());
                    policyPremium.setEntityId(policy.getEntityId());
                    List<DmPolicyPremiumVo> premiumVos = dmPolicyPremiumDao.findDataByPolicyNo(policyPremium);
                    String finalBusinessSubType = businessSubType;
                    premiumVos.forEach(premiumVo ->{
                        //生成计量单元编码 业务细分类型+保单号+当保单险类为20时风险费用信息的拆分险类
                        String cmUnitNo = finalBusinessSubType + policy.getPolicyNo()+premiumVo.getRiskClassCode2();
                        dmIcgProcVo.setCmUnitNo(cmUnitNo);
                        //生成计量单元 一个保单对应多条风险信息,当保单险类为20时,一个保单可能会生成多个计量单元 ,此时要以保单号和拆分险类进行分组生成计量单元
                        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.insertCmunitIdentifyRiskClass2(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                                , appConfig.getDataSliceQuantity());
                    });
                }else {
                    String cmUnitNo = businessSubType + policy.getPolicyNo();
                    dmIcgProcVo.setCmUnitNo(cmUnitNo);
                    //生成计量单元
                    DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.insertCmunitIdentify(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                            , appConfig.getDataSliceQuantity());
                }
            });
        }
      if (DmConstant.CmunitTo.POLICY.equals(dmIcgProcVo.getCmunitTo())) {
      //修改已生成计量单元标识
                DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> odsPolicyMainService.updateTaskStatusByCmunitNo(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
      } else {
              DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> odsPolicyPremiumService.updateTaskStatusByCmunitNo(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
              , appConfig.getDataSliceQuantity());
      }*/
    }

    /**
     * 计量单元判定
     * 1、在生成计量单元后执行
     * 2、在前端页面直接调用
     *
     * @param dmIcgProcVo
     */
    @TrackDataMgrProcess(DmConstant.ProcCode.DM_UNIT_RECOGNIZER)
    @Override
    public void cmunitDetermine(DmIcgProcVo dmIcgProcVo) {


        this.validParam(dmIcgProcVo);

        this.getTicCode(dmIcgProcVo);

        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.CMUNIT_IDENTIFY_NODE);
        dmIcgProcVo.setProcId(prodNode);

        //loa重新适配不带ticCode
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateCmunitAdapterWithoutCode(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

        if (StringUtil.isNotEmpty(dmIcgProcVo.getTicCodeConf())) {
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateCmunitAdapterWithCode(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());
        }

        //loa未配置提示
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateCmunitAdapterLoaFail(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_RISK_TEST)
    @Override
    public void majorRisk(DmIcgProcVo dmIcgProcVo) {
        // 校验参数是否有效
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long prodNode = this.getProdNode(DmConstant.IcgProcidNode.MAJOR_RISK_NODE);
        dmIcgProcVo.setProcId(prodNode);

        dmIcgProcVo.setBusinessModel(DmConstant.BusinessModel.MODEL_DIRECT);
        //查找重大风险方案的配置
        String majorRiskPlan = this.getMajorRiskPlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_DIRECT);
        //清除重风测试数据
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateMajorResultNull(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        switch (majorRiskPlan) {
            //全部通过
            case DmConstant.MajorRiskPlan.ALL_PASS:
                DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateMajorRiskAllPass(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                        , appConfig.getDataSliceQuantity());
                break;
            //配置+规则
            case DmConstant.MajorRiskPlan.ADAPTER_CONFIG_RULE:
                //配置
                DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateMajorRiskAdapterConfig(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                        , appConfig.getDataSliceQuantity());
                //规则 查询重风结果为'C'线上测试的计量单元
                List<DmCmunitDirectMajorTestVo> dmCmunitDirectMajorTestVos = dmBussCmunitDirectService.listCmunitDirectMajorTestInfo(dmIcgProcVo);

                // 在方法内部定义开始时间
                long startTime = System.currentTimeMillis();

                // 提交任务到线程池
                for (DmCmunitDirectMajorTestVo item : dmCmunitDirectMajorTestVos) {
                    if (ruleExecutorService.isTerminated()) {
                        ruleExecutorService = Executors.newFixedThreadPool(10); // 重新初始化
                    }
                    ruleExecutorService.submit(() -> {
                        String majorResult = dmDomainMajorRiskService.getFactorValueCalc(item.getCmunitId(), dmIcgProcVo.getEntityId(), item.getPolicyNo(), null, "1", DmConstant.BusinessModel.MODEL_DIRECT, dmIcgProcVo.getYearMonth(),item.getBusinessSubdivisionType());
                        item.setMajorResult(majorResult);
                        item.setProcId(dmIcgProcVo.getProcId());
                        dmBussCmunitDirectService.updateMajorResultByCmunitId(item);
                    });
                }

                // 等待所有任务完成（可选）
                ruleExecutorService.shutdown();
                while (!ruleExecutorService.isTerminated()) {
                    // 等待所有线程执行完毕
                }

                // 记录结束时间并打印执行时间
                long endTime = System.currentTimeMillis();
                System.out.println("Start Time: " + new java.util.Date(startTime));
                System.out.println("End Time: " + new java.util.Date(endTime));
                System.out.println("Total Execution Time: " + (endTime - startTime) + " ms");
                break;
            default:
                break;
        }
    }

    @Override
    public void majorRiskNoPass(DmIcgProcVo dmIcgProcVo) {


        //是否存在重测业务不通过
        Integer majorRiskBussNoPassCount = dmBussCmunitDirectService.getMajorRiskBussNoPassCount(dmIcgProcVo);
        if (majorRiskBussNoPassCount < 0) {
            return;
        }
        // 生成合同组合以及合同组编码
        String portfolioNo = this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_DIRECT);
        if (StringUtil.isNotEmpty(portfolioNo)) {
            dmIcgProcVo.setPortfolioColumn(portfolioNo);
            String icgNo = this.getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_DIRECT);
            if (StringUtil.isNotEmpty(icgNo)) {
                dmIcgProcVo.setIcgColumn(icgNo);
            }
        }

        //获取合同组合流程节点
        Long portfolioProcId = this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE);
        dmIcgProcVo.setPortfolioProcId(portfolioProcId);
        //获取合同组流程节点
        Long icgProcId = this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE);
        dmIcgProcVo.setIcgProcId(icgProcId);

        //评估方法与盈亏设置不区分，合同组合，合同组

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateMajorNoPass(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_EVALDEF_CONFIG)
    @Override
    public void evaluateApproach(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.EVALUATE_APPROACH_NODE);
        dmIcgProcVo.setProcId(procId);
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateEvaluateApproach(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_PORTFOLIO)
    @Override
    public void portfolioDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.PORTFOLIO_DISCERN_NODE);
        dmIcgProcVo.setProcId(procId);
        String portfolioNo = this.getContractCode(dmIcgProcVo.getEntityId(), "G", DmConstant.BusinessModel.MODEL_DIRECT);
        if (StringUtil.isNotEmpty(portfolioNo)) {
            dmIcgProcVo.setPortfolioColumn(portfolioNo);
        }

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updatePortfolioDiscern(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_DETERMINATION)
    @Override
    public void profitableEstimate(DmIcgProcVo dmIcgProcVo) {
        //profit_loss_discern

        this.validParam(dmIcgProcVo);
//        this.syncProfitableRuleParam();

        //如果查询当月没有数据做，直接返回
        Integer profitableEstimateValidCount = dmBussCmunitDirectService.getProfitableEstimateValidCount(dmIcgProcVo);
        if (profitableEstimateValidCount < 1) {
            return;
        }
        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.PROFITABLE_ESTIMATE_NODE);
        dmIcgProcVo.setProcId(procId);
        dmIcgProcVo.setDataKey(StringUtil.concat(dmIcgProcVo.getEntityId()
                , dmIcgProcVo.getYearMonth()));

        BbsConfAccountSetVo accountSetVo = bbsConfAccountSetClient.findByCenterId(dmIcgProcVo.getEntityId()).getResData();
        dmIcgProcVo.setCurrencyCode(accountSetVo.getCurrencyCode());
        //初始化盈亏状态
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateProfitableEstimateInit(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //盈亏方案选择
        String profitableEstimatePlan = this.getProfitableEstimatePlan(dmIcgProcVo.getEntityId(), DmConstant.BusinessModel.MODEL_DIRECT);
        //适配配置
        if (profitableEstimatePlan.equals(DmConstant.LossRiskPlan.ADAPTER_RULE)) {
            // 同步
            this.syncProfitableRuleParam();
            dmDomainProfitablePaaService.Profitable(dmIcgProcVo);
            dmDomainProfitableBbaService.Profitable(dmIcgProcVo);
        } else {
            DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateProfitableEstimateAdapterConfig(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                    , appConfig.getDataSliceQuantity());
        }

    }

    @Override
    public void icgBorderSeal(DmIcgProcVo dmIcgProcVo) {
        //icg_Fixed
        //更新D2
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgBorderSealD2(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //更新D3
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgBorderSealD3(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //更新D4
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgBorderSealD4(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //更新合同确认日期
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgBorderSeal(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACTGROUP)
    @Override
    public void icgDiscern(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //合同确认
        this.icgBorderSeal(dmIcgProcVo);
        String icgNo = getContractCode(dmIcgProcVo.getEntityId(), "C", DmConstant.BusinessModel.MODEL_DIRECT);
        dmIcgProcVo.setIcgColumn(icgNo);
        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.ICG_DISCERN_NODE);
        dmIcgProcVo.setProcId(procId);

        //更新合同组编码

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgDiscern(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());

    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_INVESTMENT_COST)
    @Override
    public void icgInvestment(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
        Integer icgInvestmentValid = dmBussCmunitDirectService.getIcgInvestmentValid(dmIcgProcVo);
        if (icgInvestmentValid != 0) {
            throw new DmCmunitException("There are measurement units that have not completed the contract group division");
        }
        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.ICG_INVESTMENT_NODE);
        dmIcgProcVo.setProcId(procId);

        //进行投成拆分
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgInvestment(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        //投成拆分未配置
        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgInvestmentFail(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
    }

    @TrackDataMgrProcess(DmConstant.ProcCode.DM_CONTRACT_CONFIRMATION)
    @Override
    public void icgConfirm(DmIcgProcVo dmIcgProcVo) {
        this.validParam(dmIcgProcVo);

        //获取流程节点
        Long procId = this.getProdNode(DmConstant.IcgProcidNode.DM_CONTRACT_CONFIRMATION);
        dmIcgProcVo.setProcId(procId);

        DataSliceUtils.runThreadsThrows((dataSliceQuantity, dataSliceNo) -> dmBussCmunitDirectService.updateIcgYearMonth(dmIcgProcVo, SliceAttributes.init(dataSliceQuantity, dataSliceNo))
                , appConfig.getDataSliceQuantity());
        // 执行完成后更新详情状态
        dmDomainPeriodService.executionDetail(dmIcgProcVo.getEntityId()
                , dmIcgProcVo.getYearMonth(), DmConstant.BussCmunit.CMUNIT_DIRECT);

        //备份盈亏过程
        this.backProfitResult(dmIcgProcVo, DmConstant.BusinessModel.MODEL_DIRECT);
    }

}
