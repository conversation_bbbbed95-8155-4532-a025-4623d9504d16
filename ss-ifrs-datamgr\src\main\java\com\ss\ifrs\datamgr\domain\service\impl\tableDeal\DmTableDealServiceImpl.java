package com.ss.ifrs.datamgr.domain.service.impl.tableDeal;

import com.ss.ifrs.datamgr.dao.conf.DmConfTableColumnRefDao;
import com.ss.ifrs.datamgr.dao.schema.DmSequenceStructDao;
import com.ss.ifrs.datamgr.dao.schema.DmSynonymStructDao;
import com.ss.ifrs.datamgr.dao.schema.DmTableStructDao;
import com.ss.ifrs.datamgr.domain.service.tableDeal.DmTableDealService;
import com.ss.ifrs.datamgr.service.conf.DmConfCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DmTableDealServiceImpl implements DmTableDealService {


    @Autowired
    private DmTableStructDao dmTableStructDao;

    @Autowired
    private DmSequenceStructDao dmSequenceStructDao;

    @Autowired
    private DmConfCodeService dmConfCodeService;

    @Autowired
    private DmSynonymStructDao dmSynonymStructDao;

    @Resource
    private DataSource dataSource;

    @Resource
    private DmConfTableColumnRefDao dmConfTableColumnRefDao;

    @Override
    public void addOdsTable(String tableName, String tableComment, String schema) {
        String newTableName = "ODS_" + tableName;
        Integer createTableRes = createTable(newTableName, tableComment, schema);
        if (createTableRes > 0) {
            log.info("{}.{}表已存在",schema,newTableName);
            return;
        }
        //添加默认字段
        List<Map<String, String>> defColumnList = dmConfTableColumnRefDao.queryTableDefaultColumn();
        for (int i = 0; i < defColumnList.size(); i++) {
            Map<String, String> defColumn = defColumnList.get(i);
            Integer columnTotal = dmTableStructDao.queryTableAndColumnIsExist(newTableName, defColumn.get("srcColumn"), schema);
            if (columnTotal < 1) {
                dmTableStructDao.alterTableAddColumn(newTableName, defColumn.get("srcColumn"), defColumn.get("columnType"), schema);
                dmTableStructDao.modifyTableColumnComment(newTableName, defColumn.get("srcColumn"), defColumn.get("colDesc"), schema);
            }
        }
        //添加序列
        String sequenceName = "ODS_SEQ_" + tableName;
        Integer seqTotal = dmSequenceStructDao.querySequenceIsExist(sequenceName, schema);
        if (seqTotal < 1) {
            dmSequenceStructDao.createSequence(sequenceName, schema);
            //创建触发器
            //TODO 调用异常
            //String triggerName = "ODS_TR_" + tableName;
            //dmSequenceStructDao.createTrigger(triggerName,newTableName,sequenceName,schema);
        }

        //添加同义词
        if ("Oracle".equalsIgnoreCase(getDatabaseProductName())) {
            dmSynonymStructDao.createSynonym(schema + "." + newTableName, newTableName);
        }
    }

    @Override
    public void addDmTable(String tableName, String tableComment, String schema) {
        String newTableName = "DM_" + tableName;
        Integer createTableRes = createTable(newTableName, tableComment, schema);
        if (createTableRes > 0) {
            log.info("{}.{}表已存在",schema,newTableName);
            return;
        }
        //添加默认字段
        List<Map<String, String>> defColumnList = dmConfTableColumnRefDao.queryTableDefaultColumn();
        for (int i = 0; i < defColumnList.size(); i++) {
            Map<String, String> defColumn = defColumnList.get(i);
            Integer columnTotal = dmTableStructDao.queryTableAndColumnIsExist(newTableName, defColumn.get("srcColumn"), schema);
            if (columnTotal < 1) {
                dmTableStructDao.alterTableAddColumn(newTableName, defColumn.get("srcColumn"), defColumn.get("columnType"), schema);
                dmTableStructDao.modifyTableColumnComment(newTableName, defColumn.get("srcColumn"), defColumn.get("colDesc"), schema);
            }
        }
    }

    @Override
    public void addStatTable(String tableName, String tableComment, String schema) {
        String newTableName = "DM_STAT_" + tableName;
        Integer createTableRes = createTable(newTableName, tableComment, schema);
        if (createTableRes > 0) {
            return;
        }
        String[] staColumn = null;
        String[] staColumnType = null;
        String[] staColumnComment = null;
        if("Oracle".equalsIgnoreCase(getDatabaseProductName())){
            staColumn = new String[]{"ID", "task_code", "draw_time", "task_status", "quantity"};
            staColumnType = new String[]{"NUMBER(11) not null", "VARCHAR2(20) not null", "TIMESTAMP(6) not null", "VARCHAR2(1) not null", "NUMBER(10)"};
            staColumnComment = new String[]{"主键Id", "task_code||任务编号", "draw_time||提数时间", "task_status||任务状态", "quantity|数量"};

        }else if("PostgreSQL".equalsIgnoreCase(getDatabaseProductName())){
            staColumn = new String[]{"ID", "task_code", "draw_time", "task_status", "quantity"};
            staColumnType = new String[]{"NUMBER(11) not null", "VARCHAR2(20) not null", "TIMESTAMP(6) not null", "VARCHAR2(1) not null", "NUMBER(10)"};
            staColumnComment = new String[]{"主键Id", "task_code||任务编号", "draw_time||提数时间", "task_status||任务状态", "quantity|数量"};
        }else{
            return;
        }
        for(int i = 1;i<staColumn.length;i++){
            Integer columnTotal = dmTableStructDao.queryTableAndColumnIsExist(newTableName, staColumn[i], schema);
            if (columnTotal < 1) {
                dmTableStructDao.alterTableAddColumn(newTableName, staColumn[i], staColumnType[i], schema);
                dmTableStructDao.modifyTableColumnComment(newTableName, staColumn[i], staColumnComment[i], schema);
            }
        }
        //创建序列
        String sequenceName = "DM_SEQ_STAT_" + tableName;
        Integer seqTotal = dmSequenceStructDao.querySequenceIsExist(sequenceName, schema);
        if (seqTotal < 1) {
            dmSequenceStructDao.createSequence(sequenceName, schema);
        }
    }

    @Override
    public void alterTableAddColumn(String table, String column, String columnType, String columnComment, String schema) {
        Integer res = dmTableStructDao.queryTableAndColumnIsExist(table, column, schema);
        if(res > 0){
            log.info("{}.{}.{}表字段已存在",schema,table,column);
            return;
        }
        dmTableStructDao.alterTableAddColumn(table,column,columnType,schema);
        dmTableStructDao.modifyTableColumnComment(table,column,columnComment,schema);
    }

    @Override
    public void dropTable(String schema, String table) {
        Integer res = dmTableStructDao.queryTableIsExist(schema, table);
        if (res < 1) {
            log.info("{}.{}表不存在", schema, table);
            return;
        }
        dmTableStructDao.dropTable(schema, table);
    }

    @Override
    public void dropColumn(String table, String column, String schema) {
        Integer res = dmTableStructDao.queryTableAndColumnIsExist(table, column, schema);
        if(res < 1){
            log.info("{}.{}.{}表字段不存在",schema,table,column);
            return;
        }
        dmTableStructDao.dropColumn(table, column, schema);
    }

    @Override
    public void dropSequence(String sequence, String schema) {
        Integer res = dmSequenceStructDao.querySequenceIsExist(sequence, schema);
        if(res < 1){
            log.info("{}.{}序列不存在",schema,sequence);
            return;
        }
        dmSequenceStructDao.dropSequence(sequence,schema);

    }

    @Override
    public void modifyTableComment(String table, String comment, String schema) {
        Integer res = dmTableStructDao.queryTableIsExist(table, schema);
        if(res < 1){
            log.info("{}.{}表不存在",schema,table);
            return;
        }
        dmTableStructDao.modifyTableComment(table, comment, schema);
    }

    @Override
    public void modifyTableColumnComment(String tableName, String columnName, String comment, String schema) {
        Integer count = dmTableStructDao.queryTableAndColumnIsExist(tableName, columnName, schema);
        if (count > 0) {
            dmTableStructDao.modifyTableColumnComment(tableName, columnName, comment, schema);
        }
    }

    @Override
    public Boolean isColumnExists(String tableName, String columnName, String schema) {
        return dmTableStructDao.queryTableAndColumnIsExist(tableName, columnName, schema) != 0;
    }

    @Override
    public Boolean checkIfPartitionedTableExists(String tableName) {
        return dmTableStructDao.checkIfPartitionedTableExists(tableName);
    }

    @Override
    public void createPartitionedTable(String yearMonth) {
        List<String> tableNames = dmConfCodeService.findByCodeIdx("PartitionTable");
        tableNames.forEach(item -> {
            String odsPartitionTableName = String.format("odsuser.ods_%s_%s", item, yearMonth);
            String dmPartitionTableName = String.format("dm_%s_%s", item, yearMonth);
            String odsTableName = String.format("odsuser.ods_%s", item);
            String dmTableName = String.format("dmuser.dm_%s", item);
            // 分区表存在则跳过不处理
            if (dmTableStructDao.checkIfPartitionedTableExists(dmPartitionTableName)) {
                return;
            }
            dmTableStructDao.createPartitionedTable(odsTableName, odsPartitionTableName, yearMonth);
            dmTableStructDao.createPartitionedTable(dmTableName, dmPartitionTableName, yearMonth);
        });
    }

    /**
     * 创建表,ID为主键
     *
     * @param tableName
     * @param tableComment
     * @param schema
     * @return
     */
    private Integer createTable(String tableName, String tableComment, String schema) {
        Integer tableCount = dmTableStructDao.queryTableIsExist(tableName, schema);
        if (tableCount > 0) {
            //表存在无需创建
            return tableCount;
        }

        //创建表,设置ID为主键，设置表注释
        dmTableStructDao.createTable(schema + "." + tableName.toUpperCase());
        dmTableStructDao.modifyTableColumnComment(tableName.toUpperCase(), "ID", "主键Id", schema);
        dmTableStructDao.modifyTableComment(tableName.toUpperCase(), tableComment, schema);
        return tableCount;
    }

    /**
     * 获取当前连接的数据库类型
     *
     * @return
     */
    private String getDatabaseProductName() {
        String databaseProductName;
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            databaseProductName = metaData.getDatabaseProductName();
            return databaseProductName;
        } catch (SQLException e) {
            e.printStackTrace();
            return "";
        }
    }
}
