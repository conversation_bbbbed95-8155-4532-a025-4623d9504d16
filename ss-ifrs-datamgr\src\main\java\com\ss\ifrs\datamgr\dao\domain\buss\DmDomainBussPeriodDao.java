package com.ss.ifrs.datamgr.dao.domain.buss;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DmDomainBussPeriodDao {

    String getValidYearMonth(Long entityId, String yearMonth, String bizCode);

    String getMinYearMonth(BussPeriodReqVo bussPeriodReqVo);

    String getMaxYearMonth(Long entityId);

    List<String> listCmunitIdentifyYear(Long entityId);

    String getYearMonth(Long entityId,String yearMonth,String periodState);
}
