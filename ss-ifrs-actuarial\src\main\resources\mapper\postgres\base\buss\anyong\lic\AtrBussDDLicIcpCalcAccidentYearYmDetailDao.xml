<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-04 11:41:51 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussDDLicIcpCalcAccidentYearYmDetailDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcpCalcAccidentYmDetail">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="ACCIDENT_YEAR_MONTH" property="accidentYearMonth" jdbcType="VARCHAR" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="PAID_MODE" property="paidMode" jdbcType="DECIMAL" />
    <result column="IBNR" property="ibnr" jdbcType="DECIMAL" />
    <result column="OS" property="os" jdbcType="DECIMAL" />
    <result column="ULAE" property="ulae" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, MAIN_ID, ACCIDENT_YEAR_MONTH, DEV_NO, PAID_MODE, IBNR, OS, ULAE
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="mainId != null ">
          and MAIN_ID = #{mainId,jdbcType=DECIMAL}
      </if>
      <if test="accidentYearMonth != null and accidentYearMonth != ''">
          and ACCIDENT_YEAR_MONTH = #{accidentYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="devNo != null ">
          and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="paidMode != null ">
          and PAID_MODE = #{paidMode,jdbcType=DECIMAL}
      </if>
      <if test="ibnr != null ">
          and IBNR = #{ibnr,jdbcType=DECIMAL}
      </if>
      <if test="os != null ">
          and OS = #{os,jdbcType=DECIMAL}
      </if>
      <if test="ulae != null ">
          and ULAE = #{ulae,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.mainId != null ">
          and MAIN_ID = #{condition.mainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.accidentYearMonth != null and condition.accidentYearMonth != ''">
          and ACCIDENT_YEAR_MONTH = #{condition.accidentYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.devNo != null ">
          and DEV_NO = #{condition.devNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.paidMode != null ">
          and PAID_MODE = #{condition.paidMode,jdbcType=DECIMAL}
      </if>
      <if test="condition.ibnr != null ">
          and IBNR = #{condition.ibnr,jdbcType=DECIMAL}
      </if>
      <if test="condition.os != null ">
          and OS = #{condition.os,jdbcType=DECIMAL}
      </if>
      <if test="condition.ulae != null ">
          and ULAE = #{condition.ulae,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="mainId != null ">
          and MAIN_ID = #{mainId,jdbcType=DECIMAL}
      </if>
      <if test="accidentYearMonth != null and accidentYearMonth != ''">
          and ACCIDENT_YEAR_MONTH = #{accidentYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="devNo != null ">
          and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="paidMode != null ">
          and PAID_MODE = #{paidMode,jdbcType=DECIMAL}
      </if>
      <if test="ibnr != null ">
          and IBNR = #{ibnr,jdbcType=DECIMAL}
      </if>
      <if test="os != null ">
          and OS = #{os,jdbcType=DECIMAL}
      </if>
      <if test="ulae != null ">
          and ULAE = #{ulae,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    where ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcpCalcAccidentYmDetail">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcpCalcAccidentYmDetail">
    insert into ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="mainId != null">
        MAIN_ID,
      </if>
      <if test="accidentYearMonth != null">
        ACCIDENT_YEAR_MONTH,
      </if>
      <if test="devNo != null">
        DEV_NO,
      </if>
      <if test="paidMode != null">
        PAID_MODE,
      </if>
      <if test="ibnr != null">
        IBNR,
      </if>
      <if test="os != null">
        OS,
      </if>
      <if test="ulae != null">
        ULAE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="mainId != null">
        #{mainId,jdbcType=DECIMAL},
      </if>
      <if test="accidentYearMonth != null">
        #{accidentYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="devNo != null">
        #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="paidMode != null">
        #{paidMode,jdbcType=DECIMAL},
      </if>
      <if test="ibnr != null">
        #{ibnr,jdbcType=DECIMAL},
      </if>
      <if test="os != null">
        #{os,jdbcType=DECIMAL},
      </if>
      <if test="ulae != null">
        #{ulae,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL values
       (#{item.id,jdbcType=DECIMAL}, 
        #{item.mainId,jdbcType=DECIMAL}, #{item.accidentYearMonth,jdbcType=VARCHAR}, #{item.devNo,jdbcType=DECIMAL}, 
        #{item.paidMode,jdbcType=DECIMAL}, #{item.ibnr,jdbcType=DECIMAL}, #{item.os,jdbcType=DECIMAL}, 
        #{item.ulae,jdbcType=DECIMAL})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcpCalcAccidentYmDetail">
    update ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    <set>
      <if test="mainId != null">
        MAIN_ID = #{mainId,jdbcType=DECIMAL},
      </if>
      <if test="accidentYearMonth != null">
        ACCIDENT_YEAR_MONTH = #{accidentYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="devNo != null">
        DEV_NO = #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="paidMode != null">
        PAID_MODE = #{paidMode,jdbcType=DECIMAL},
      </if>
      <if test="ibnr != null">
        IBNR = #{ibnr,jdbcType=DECIMAL},
      </if>
      <if test="os != null">
        OS = #{os,jdbcType=DECIMAL},
      </if>
      <if test="ulae != null">
        ULAE = #{ulae,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcpCalcAccidentYmDetail">
    update ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    <set>
      <if test="record.mainId != null">
        MAIN_ID = #{record.mainId,jdbcType=DECIMAL},
      </if>
      <if test="record.accidentYearMonth != null">
        ACCIDENT_YEAR_MONTH = #{record.accidentYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.devNo != null">
        DEV_NO = #{record.devNo,jdbcType=DECIMAL},
      </if>
      <if test="record.paidMode != null">
        PAID_MODE = #{record.paidMode,jdbcType=DECIMAL},
      </if>
      <if test="record.ibnr != null">
        IBNR = #{record.ibnr,jdbcType=DECIMAL},
      </if>
      <if test="record.os != null">
        OS = #{record.os,jdbcType=DECIMAL},
      </if>
      <if test="record.ulae != null">
        ULAE = #{record.ulae,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    where ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcpCalcAccidentYmDetail">
    select count(1) from ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>