package com.ss.ifrs.datamgr.domain.abst;

import com.ss.ifrs.datamgr.pojo.conf.po.DmConfBussPeriod;
import com.ss.ifrs.datamgr.service.conf.DmConfBussPeriodService;
import com.ss.library.utils.CacheUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class DmDomainPeriod {

    @Autowired
    private DmConfBussPeriodService confBussPeriodService;

    /**
     * 更新期间
     */
    protected void update(DmConfBussPeriod dmConfBussPeriod) {
        confBussPeriodService.update(dmConfBussPeriod);
        CacheUtil.remove(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.DmIdentity.APP_CODE);
    }
}
