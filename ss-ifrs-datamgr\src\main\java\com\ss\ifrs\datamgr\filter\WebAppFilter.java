package com.ss.ifrs.datamgr.filter;

import com.ss.ifrs.datamgr.conf.AppConfig;
import com.ss.platform.core.model.SsException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Component
public class WebAppFilter extends OncePerRequestFilter {

    @Autowired
    public PathMatcher pathMatcher;

    @Autowired
    private AppConfig appConfig;

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            if (!this.checkPrefixFilter(request, this.appConfig.getExcludedUrls())) {
                this.logger.debug("Check excluded Urls: {} request to {}", request.getMethod(), request.getRequestURL().toString());
                this.doFilter(request);
            }
            filterChain.doFilter(request, response);
        } catch (SsException exception) {
            throw exception;
        } catch (Exception exception) {
            this.logger.warn("Do WebAppFilter Error: {}", exception.getLocalizedMessage());
            throw new SsException(exception);
        }
    }

    private boolean checkPrefixFilter(HttpServletRequest request, List<String> authPrefix) throws Exception {
        boolean ischeckPrefix = false;
        String currentUrl = request.getServletPath();
        if (!CollectionUtils.isEmpty(authPrefix)) {
            // 遍历List
            for (String prefix : authPrefix) {
                if (StringUtils.isNotEmpty(prefix) && this.pathMatcher.match(prefix, currentUrl)) {
                    ischeckPrefix = true;
                    break;
                }
            }
        }
        return ischeckPrefix;
    }

    @SuppressWarnings("unchecked")
    private void doFilter(HttpServletRequest request) throws Exception {
        /*this.logger.debug("Check signin: {} request to {}", request.getMethod(), request.getRequestURL().toString());
        String sessionId = HttpRequestUtil.getSessionId(request);
        this.logger.debug("WebAppFilter: sessionId is " + sessionId);
        String token = HttpRequestUtil.getToken(request, sessionId);
        this.logger.debug("WebAppFilter: token is " + token);
        this.logger.debug("WebAppFilter: cookies is " + JSON.toJSONString(request.getCookies()));
        if (StringUtils.isEmpty(sessionId) || StringUtils.isEmpty(token)) {
            throw new SsException("no login or login timeout!", ResCodeConstant.ResCode.UNAUTHORIZED);
        }
        if (CommonConstant.APP_NAME.equals(sessionId) && token.equals(EncryptUtils.encrypt(sessionId))) {
            //处理特殊情况：系统自动任务调用的接口予以放行
            return;
        } else if (!HttpRequestUtil.checkValidateToken(request, sessionId, token)) {
            throw new SsException("The Session or Token is invalid!", ResCodeConstant.ResCode.SIGNATURE_ERROR);
        }*/
    }
}