package com.ss.ifrs.actuarial.util;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrVo;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.utils.DateUtil;
import com.ss.library.utils.StringUtil;
import org.apache.el.parser.ParseException;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @ClassName AtrExcelIbnrImportUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/1
 **/
public class AtrExcelIbnrImportUtil {
    public static AtrBussReserveIbnrVo parseExcel(InputStream fs) throws Exception {

        List<AtrBussReserveIbnrDetailVo> detailVoList = new ArrayList<>();
        AtrBussReserveIbnrVo atrBussReserveIbnrVo = new AtrBussReserveIbnrVo();
        AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo = new AtrBussReserveIbnrDetailVo();
        //创建工作簿
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fs);
        //读取第一个工作表
        XSSFSheet sheet = xssfWorkbook.getSheetAt(0);

        XSSFRow xssfRow;
        XSSFCell cell;
        int col;
        String dateStr;
        Date date;
        String cellValue;
        String yearMonth;
        BigDecimal value;

        int statisZones;
        Date endDate;
        try {

            //*业务单位：
            cell = sheet.getRow(1).getCell(1);
            if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                cellValue = cell.getStringCellValue();
                atrBussReserveIbnrVo.setEntityCode(cellValue);
            }else {
                throw new ParseException("row : " + 2 + ", column : " + 1 + ", "  + " Can't be empty");
            }

            //产品风险大类：
            cell = sheet.getRow(2).getCell(1);
            if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                cellValue = cell.getStringCellValue();
                atrBussReserveIbnrVo.setLoaCode(cellValue);
            }

            //*币别：
            cell = sheet.getRow(3).getCell(1);
            if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                cellValue = cell.getStringCellValue();
                atrBussReserveIbnrVo.setCurrencyCode(cellValue);
            }else {
                throw new ParseException("row : " + 4 + ", column : " + 1 + ", " + " Can't be empty");
            }

            //*IBNR类型：
            cell = sheet.getRow(4).getCell(1);
            if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                cellValue = cell.getStringCellValue();
                atrBussReserveIbnrVo.setIbnrType(cellValue);
            }else {
                throw new ParseException("row : " + 5 + ", column : " + 1 + ", " + " Can't be empty!");
            }

            //业务类型：
            cell = sheet.getRow(5).getCell(1);
            if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                cellValue = cell.getStringCellValue();
                atrBussReserveIbnrVo.setBusinessSourceCode(cellValue);
            }

            //*提取区间数：
            try{
                cell = sheet.getRow(6).getCell(1);
                if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                    cell.setCellType(CellType.STRING);
                    cellValue = cell.getStringCellValue();
                    atrBussReserveIbnrVo.setStatisZones(Long.parseLong(cellValue));
                    statisZones = Integer.parseInt(cellValue);
                }else {
                    throw new ParseException("row : " + 7 + ", column : " + 1 + ", " + " Can't be empty");
                }
            }catch  (Exception e){
                throw new ParseException("row : " + 7 + ", column : " + 1 + ", " +  "the value is not a integer numeric type!");
            }

            //*提取结束日期：
            try{
                cell = sheet.getRow(7).getCell(1);
                if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                    cell.setCellType(CellType.STRING);
                    date = DateUtil.doubleToDate(Double.parseDouble(cell.getStringCellValue()));
                    atrBussReserveIbnrVo.setEndDate(date);
                    endDate = date;
                }else {
                    throw new ParseException("row : " + 8 + ", column : " + 1 + ", " + " Can't be empty");
                }
            }catch  (Exception e){
                throw new ParseException("row : " + 8 + ", column : " + 1 + ", " +  "the value is not a date type!");
            }


            sheet.getRow(7).getCell(1).setCellType(CellType.STRING);
            dateStr = sheet.getRow(7).getCell(1).getStringCellValue();
            // 不在正确的日期范围1970/1/1 ~ 9999/12/31，其始终转字符分别为25569、2958465
            long startLong = new Long(25569);
            long endLong = new Long(2958465);

            if(StringUtil.isNotEmpty(dateStr)){
                if(Long.parseLong(dateStr) - startLong < 0 ){
                    // 单元格非法日期类型时，执行下方语句自动获取catch部分异常
                    atrBussReserveIbnrVo.setEndDate(sheet.getRow(7).getCell(1).getDateCellValue());
                }
                if(Long.parseLong(dateStr) - endLong > 0 ){
                    // 单元格非法日期类型时，执行下方语句自动获取catch部分异常
                    atrBussReserveIbnrVo.setEndDate(sheet.getRow(7).getCell(1).getDateCellValue());
                }
            }

            xssfRow= sheet.getRow(12);
            // 会计期间年月正则
            String patternYM = CommonConstant.PatternRule.YEAR_MONTH;
            // 两位小数数字正则（正负数）
            String patternDecimal = CommonConstant.PatternRule.DECIMAL;
            String colMsg = " (1 ~ "  + (statisZones+1) + ") ";
            // 获取第一个月份
            String startYM = dateMinusMonth(endDate, statisZones);
            // 跟进首月获取其他月份
            //String atr2 = dateMinusMonth(startYM, 1);
            String tempMsg;
            // xssfRow.getPhysicalNumberOfCells()
            for (col = 1; col <= statisZones; col++) {

                cell = xssfRow.getCell(col);
                cell.setCellType(CellType.STRING);

                if(col >= 1){
                    //事故发生年月
                    if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                        yearMonth = cell.getStringCellValue();
                        if(StringUtil.isNotEmpty(yearMonth)){
                            if(!Pattern.matches(patternYM, yearMonth)){
                                throw new ParseException("row : " + 12 + ", column : " + (col+1) + ", " +"the format of " + "Year Month" + " is incorrect");
                            }
                            if(!yearMonth.equals(dateMinusMonth(startYM, col-1))){
                                tempMsg = "according to the Statis Zones and the Draw End Date, the value should be ";
                                throw new ParseException("row : " + 12 + ", column : " + (col+1) + ", " + tempMsg + dateMinusMonth(startYM, col-1));
                            }
                            atrBussReserveIbnrDetailVo = new AtrBussReserveIbnrDetailVo();
                            atrBussReserveIbnrDetailVo.setDamageYearMonth(yearMonth);
                        }else {
                            throw new ParseException("row : " + 12 + ", column : " + (col+1) + colMsg + ", "  + " Can't be empty");
                        }
                    }

                    //IBNR未决赔款
                    cell = sheet.getRow(13).getCell(col);
                    try{

                        if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                            // 校验符合两位小数的数字正则
                            if(!Pattern.matches(patternDecimal, String.valueOf(cell.getNumericCellValue()))){
                                throw new NumberFormatException("row : " + 13 + ", column : " + (col + 1) + ", " +" the value is not a correct numeric type!");
                            }
                            //cell.setCellType(CellType.NUMERIC);
                            if(StringUtil.isNotEmpty(String.valueOf(cell.getNumericCellValue()))){
                                value = new BigDecimal(cell.getNumericCellValue());
                                atrBussReserveIbnrDetailVo.setValue(value);
                            }else {
                                throw new ParseException("row : " + 13 + ", column : " + (col+1) + colMsg + ", "+ " Can't be empty!");
                            }
                        }else {
                            throw new ParseException("row : " + 13 + ", column : " + (col+1)  + colMsg + ", "+ " Can't be empty!");
                        }
                    }/*catch (IllegalStateException e) {
                        throw new ParseException("row : " + 13 + ", column : " + (col + 1) + ", " + " value is not of type date!");
                    }*/ catch (NumberFormatException e) {
                        throw new NumberFormatException("row : " + 13 + ", column : " + (col + 1) + ", " +" value is not a numeric type!");
                    } catch (Exception e){
                        //throw new ParseException("row : " + 13 + ", column : " + (col + 1) + ", " +" "+ e.getMessage());
                        throw new ParseException("row : " + 13 + ", column : " + (col + 1) + ", " +  "the value is not a numeric type!");
                    }

                    detailVoList.add(atrBussReserveIbnrDetailVo);
                }
            }

            atrBussReserveIbnrVo.setList(detailVoList);

        }catch (Exception e){
            throw e;
        } finally{
            if (xssfWorkbook != null) {
                xssfWorkbook.close();
            }
            if (fs != null) {
                fs.close();
            }
        }

        return atrBussReserveIbnrVo;
    }

    private static  boolean isRowEmpty(XSSFRow row, int headCell){
        if(row==null){
            return true;
        }
        int countCell = 0;
        for (int i = 0; i < headCell; i++) {
            XSSFCell cell = row.getCell(i);
            if(!AtrExcelIbnrImportUtil.isColEmpty(cell)){
                countCell++;
            }
        }
        if(countCell==headCell){
            return true;
        }
        return  false;
    }

    private static boolean isColEmpty(XSSFCell col){
        
        if(col == null || CellType.BLANK.equals(col.getCellType())){
            return true;
        }
        return false;
    }

    /**
     * 日期减几月
     */

    public static String dateMinusMonth(Date date, int num) throws Exception {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");

        //Date dt = sdf.parse(str);//将字符串生成Date

        Calendar rightNow = Calendar.getInstance();

        rightNow.setTime(date);//使用给定的 Date 设置此 Calendar 的时间。

        rightNow.add(Calendar.MONTH, -num + 1);// 日期减1个月

        Date dt1 = rightNow.getTime();//返回一个表示此 Calendar 时间值的 Date 对象。

        String reStr = sdf.format(dt1);//将给定的 Date 格式化为日期/时间字符串，并将结果添加到给定的 StringBuffer。

        return reStr;
    }

    /**
     * 日期减几月
     */
    public static String dateMinusMonth(String str, int num) throws Exception {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");

        Date dt = sdf.parse(str);//将字符串生成Date

        Calendar rightNow = Calendar.getInstance();

        rightNow.setTime(dt);//使用给定的 Date 设置此 Calendar 的时间。

        rightNow.add(Calendar.MONTH, num);// 日期减1个月

        Date dt1 = rightNow.getTime();//返回一个表示此 Calendar 时间值的 Date 对象。

        String reStr = sdf.format(dt1);//将给定的 Date 格式化为日期/时间字符串，并将结果添加到给定的 StringBuffer。

        return reStr;
    }
}
