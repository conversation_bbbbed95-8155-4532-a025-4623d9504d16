package com.ss.ifrs.actuarial.util;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuota;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaImportVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaVo;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefService;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.pojo.com.po.ConfCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class AtrConfQuotaImportUtil {

    public static enum BfQuotaEnum {
        YM(0), ICG(1), RISK(2);

        private final int description;

        BfQuotaEnum(int description) {
            this.description = description;
        }
        public int getDescription() {
            return description;
        }
    }
    public static enum OutQuotaEnum {
        YM(0),BT(1), ICG(2), RISK(3);

        private final int description;

        OutQuotaEnum(int description) {
            this.description = description;
        }
        public int getDescription() {
            return description;
        }
    }

    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private AtrConfQuotaDefService atrConfQuotaDefService;

    public List<AtrConfQuotaVo> quotaIcgImport(MultipartFile file, AtrConfQuotaImportVo importVo) throws Exception {
        // 验证文件
        if (ObjectUtils.isEmpty(file)) {
            LOG.error("上传的文件为空");
            throw new Exception("上传的文件为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.endsWith(".xlsx")) {
            LOG.error("文件格式不正确，只支持.xlsx格式");
            throw new Exception("文件格式不正确，只支持.xlsx格式");
        }
        LOG.info("开始按列位置处理Excel文件: {}", fileName);
        // 验证基本参数
        if (importVo == null) {
            LOG.error("导入参数为空");
            throw new Exception("导入参数为空");
        }

        if (importVo.getEntityId() == null) {
            LOG.error("业务单位ID不能为空");
            throw new Exception("业务单位ID不能为空");
        }

        AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
        atrConfQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        List<AtrConfQuotaDef> atrConfQuotaDefList = atrConfQuotaDefService.findList(atrConfQuotaDef);

        List<AtrConfQuotaVo> atrConfQuotaVos  = new ArrayList<>();
        if (CollectionUtils.isEmpty(atrConfQuotaDefList)) {
            LOG.warn("未找到有效的假设值定义，请检查假设值定义配置");
            return atrConfQuotaVos;
        }

        //未到期直保&临分分入非发展期
        List<Map<Integer, String>> sheetDataList1 = ExcelUtil.readExcelRandomSheetByPosition(file,1);
        atrConfQuotaVos.addAll(
                analysisQuotaExcelByPosition(sheetDataList1, importVo, atrConfQuotaDefList, "DD", false, 1)
        );

        //未到期合约分入非发展期
        List<Map<Integer, String>> sheetDataList2 = ExcelUtil.readExcelRandomSheetByPosition(file,2);
        atrConfQuotaVos.addAll(
                analysisQuotaExcelByPosition(sheetDataList2, importVo, atrConfQuotaDefList, "TI", false, 1)
        );

        //未决分出非发展期
        List<Map<Integer, String>> sheetDataList3 = ExcelUtil.readExcelRandomSheetByPosition(file,3);
        atrConfQuotaVos.addAll(
                analysisQuotaExcelByPosition(sheetDataList3, importVo, atrConfQuotaDefList, "", false, 0)
        );

        //未决分出再保人不履约风险率
        List<Map<Integer, String>> sheetDataList4 = ExcelUtil.readExcelRandomSheetByPosition(file,4);
        atrConfQuotaVos.addAll(
                analysisQuotaExcelByPosition(sheetDataList4, importVo, atrConfQuotaDefList, "", true, 0)
        );


        //5 未到期直保&临分分入发展期
        List<Map<Integer, String>> sheetDataList5 = ExcelUtil.readExcelRandomSheetByPosition(file,5);
        atrConfQuotaVos.addAll(
                analysisDevelopQuotaExcelByPosition(sheetDataList5, importVo, atrConfQuotaDefList, "DD")
        );

        //6 未到期直保&临分分入发展期
        List<Map<Integer, String>> sheetDataList6 = ExcelUtil.readExcelRandomSheetByPosition(file,6);
        atrConfQuotaVos.addAll(
                analysisDevelopQuotaExcelByPosition(sheetDataList6, importVo, atrConfQuotaDefList, "DD")
        );


        //7 未到期合约分入发展期
        List<Map<Integer, String>> sheetDataDetailList7 = ExcelUtil.readExcelRandomSheetByPosition(file, 7);
        atrConfQuotaVos.addAll(analysisDevelopQuotaExcelByPosition(sheetDataDetailList7, importVo, atrConfQuotaDefList, "TI"));


        //8未决分出发展期
        List<Map<Integer, String>> sheetDataDetailList8 = ExcelUtil.readExcelRandomSheetByPosition(file, 8);
        atrConfQuotaVos.addAll(analysisDevelopQuotaExcelByPosition(sheetDataDetailList8, importVo, atrConfQuotaDefList, "TI"));


        //9未决分出发展期
        List<Map<Integer, String>> sheetDataDetailList9 = ExcelUtil.readExcelRandomSheetByPosition(file, 9);
        atrConfQuotaVos.addAll(analysisDevelopQuotaExcelByPosition(sheetDataDetailList9, importVo, atrConfQuotaDefList, ""));




            // 保存数据
            if (CollectionUtils.isNotEmpty(atrConfQuotaVos)) {
                LOG.info("开始保存解析的数据，共 {} 条记录", atrConfQuotaVos.size());

                for (AtrConfQuotaVo quotaVo : atrConfQuotaVos) {

                }

                LOG.info("数据保存完成");
            } else {
                LOG.warn("没有有效数据需要保存");
            }
/*        } catch (Exception e) {
            LOG.error("按列位置处理Excel文件时发生错误: {}", e.getMessage(), e);
            throw new Exception("按列位置处理Excel文件时发生错误: " + e.getMessage());
        }*/
        return atrConfQuotaVos;
    }

    /**
     * 按列位置解析Excel非发展期数据，不依赖列名
     *
     * @param sheetDataList Excel数据列表，按列位置索引组织
     * @param atrConfQuotaVo 假设值导入VO对象
     * @return 解析后的假设值列表
     */
    private List<AtrConfQuotaVo> analysisQuotaExcelByPosition(List<Map<Integer, String>> sheetDataList,
                                                              AtrConfQuotaImportVo atrConfQuotaVo,
                                                              List<AtrConfQuotaDef> atrConfQuotaDefList,
                                                              String businessSourceCode,
                                                              boolean isRisk,
                                                              int headCount) {
        if (ObjectUtils.isEmpty(sheetDataList)) {
            LOG.info("数据为空，无需解析");
            return new ArrayList<>();
        }
        LOG.info("开始按列位置解析数据，共 {} 行", sheetDataList.size());

        List<AtrConfQuotaVo> qtcConfQuotas = new ArrayList<>();
        // 获取假设值定义列表
        AtrConfQuotaDefVo confQuotaDef = new AtrConfQuotaDefVo();
        confQuotaDef.setQuotaClass(atrConfQuotaVo.getQuotaClass());
        confQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        confQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        confQuotaDef.setLanguage("en");

        // 筛选非发展期相关的假设值定义
        List<AtrConfQuotaDef> nonDevelopQuotaDefList = atrConfQuotaDefList.stream()
                .filter(def -> "0".equals(def.getQuotaType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonDevelopQuotaDefList)) {
            LOG.warn("未找到非发展期相关的假设值定义，请检查假设值定义配置");
        }

        Map<Integer, Long>  quotaDefIdMap = new HashMap<>(8);
        Map<Integer, String> headDataMap =  sheetDataList.get(headCount);
        List<Integer> keyLists = new ArrayList<>(headDataMap.keySet());

        for (int i = ObjectUtils.isEmpty(businessSourceCode)? isRisk? 3 :4 : isRisk? 2 :3; i < keyLists.size(); i++) {
            String quotaDef = ExcelUtil.getValueByPosition(headDataMap, i, "");
            if (StringUtil.isEmpty(quotaDef)) {
                LOG.warn("第 {} 行数据缺少假设值定义，跳过处理", i);
                continue;
            }
            // 查找假设定义 - 处理可能有编码-名称格式的情况
            String quotaCode = quotaDef.contains("：") ? quotaDef.split("：")[0] : quotaDef.contains(":") ? quotaDef.split(":")[0] : quotaDef;

            Optional<AtrConfQuotaDef> quotaDefOpt = nonDevelopQuotaDefList.stream()
                    .filter(def -> def.getQuotaCode().equals(quotaCode))
                    .findFirst();
            if (!quotaDefOpt.isPresent()) {
                LOG.warn("第 {} 行数据的假设编码 '{}' 无效，跳过处理", i, quotaDef);
                continue;
            }
            quotaDefIdMap.put(i, quotaDefOpt.get().getQuotaDefId());
        }


        // 处理每一行数据
        //int rowIndex = 1; // 行索引从0开始
        for (int rowIndex = 1 + headCount ; rowIndex < sheetDataList.size(); rowIndex++) {
            Map<Integer, String> dataMap = sheetDataList.get(rowIndex);
            //rowIndex++;
            try {
                // 从Excel中获取所有必要信息，使用按列位置的方式获取
                String yearMonth = ExcelUtil.getValueByPosition(dataMap, BfQuotaEnum.YM.getDescription(), "");
                String businessModel = null;
                String icgNo = null;
                String riskClassCode = null;

                if (ObjectUtils.isEmpty(businessSourceCode)) {
                    businessModel = ExcelUtil.getValueByPosition(dataMap, 1, "");
                     icgNo = ExcelUtil.getValueByPosition(dataMap, 2, "");
                     if (!isRisk) {
                         riskClassCode = ExcelUtil.getValueByPosition(dataMap, 3, "");
                     }
                } else {
                    businessModel = businessSourceCode;
                    icgNo = ExcelUtil.getValueByPosition(dataMap, BfQuotaEnum.ICG.getDescription(), "");
                    if (!isRisk) {
                        riskClassCode = ExcelUtil.getValueByPosition(dataMap, BfQuotaEnum.RISK.getDescription(), "");
                    }
                }
                // 验证必填字段
                if (StringUtil.isEmpty(yearMonth)) {
                    LOG.warn("第 {} 行数据缺少评估期，跳过处理", rowIndex);
                    continue;
                }
                if (StringUtil.isEmpty(businessModel)) {
                    LOG.warn("第 {} 行数据缺少业务类型，跳过处理", rowIndex);
                    continue;
                }
                if (!isRisk) {
                    if (StringUtil.isEmpty(riskClassCode)) {
                        LOG.warn("第 {} 行数据缺少险类，跳过处理", rowIndex);
                        continue;
                    }
                }

                if (StringUtil.isEmpty(icgNo)) {
                    LOG.warn("第 {} 行数据缺少合同组，跳过处理", rowIndex);
                    continue;
                }
                String finalRiskClassCode = riskClassCode;
                String finalBusinessModel = businessModel;
                String finalIcgNo = icgNo;
                quotaDefIdMap.forEach((key, value) -> {
                    // 创建主表对象
                    AtrConfQuotaVo qtcConfQuota = new AtrConfQuotaVo();
                    qtcConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
                    qtcConfQuota.setYearMonth(yearMonth);
                    qtcConfQuota.setQuotaClass(atrConfQuotaVo.getQuotaClass());
                    qtcConfQuota.setDimension("C");
                    qtcConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
                    qtcConfQuota.setAuditState(CommonConstant.AuditStatus.APPROVED);
                    qtcConfQuota.setRiskClassCode(finalRiskClassCode);
                    qtcConfQuota.setSerialNo(1);
                    qtcConfQuota.setBusinessSourceCode(finalBusinessModel);
                    qtcConfQuota.setDimensionValue(finalIcgNo);
                    qtcConfQuota.setQuotaDefId(value);
                    qtcConfQuota.setQuotaValue(ExcelUtil.getValueByPosition(dataMap, key, "") );
                    qtcConfQuotas.add(qtcConfQuota);
                });
                //LOG.debug("成功解析第 {} 行数据，假设编码: {}, 假设值: {}",
                 //       rowIndex, quotaDef, qtcConfQuota.getQuotaValue());

            } catch (Exception e) {
                LOG.error("解析第 {} 行数据时发生错误: {}", rowIndex, e.getMessage(), e);
            }
        }

        LOG.info("非发展期数据解析完成，共解析出 {} 条有效记录", qtcConfQuotas.size());
        return qtcConfQuotas;
    }


    /**
     * 按列位置解析Excel非发展期数据，不依赖列名
     *
     * @param sheetDataList Excel数据列表，按列位置索引组织
     * @param atrConfQuotaVo 假设值导入VO对象
     * @return 解析后的假设值列表
     */
    private List<AtrConfQuotaVo> analysisDevelopQuotaExcelByPosition(List<Map<Integer, String>> sheetDataList,
                                                              AtrConfQuotaImportVo atrConfQuotaVo,
                                                              List<AtrConfQuotaDef> atrConfQuotaDefList,
                                                              String businessSourceCode                      ) {
        if (ObjectUtils.isEmpty(sheetDataList)) {
            LOG.info("数据为空，无需解析");
            return new ArrayList<>();
        }
        LOG.info("开始按列位置解析数据，共 {} 行", sheetDataList.size());

        // 获取假设值定义列表
        AtrConfQuotaDefVo confQuotaDef = new AtrConfQuotaDefVo();
        confQuotaDef.setQuotaClass(atrConfQuotaVo.getQuotaClass());
        confQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        confQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        confQuotaDef.setLanguage("en");

        // 筛选非发展期相关的假设值定义
        List<AtrConfQuotaDef> nonDevelopQuotaDefList = atrConfQuotaDefList.stream()
                .filter(def -> "1".equals(def.getQuotaType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonDevelopQuotaDefList)) {
            LOG.warn("未找到非发展期相关的假设值定义，请检查假设值定义配置");
        }
        Map<Integer, String> headDataMap =  sheetDataList.get(0);
        List<AtrConfQuotaVo> qtcConfQuotas = new ArrayList<>();
        // 处理每一行数据
        for (int rowIndex = 1 ; rowIndex < sheetDataList.size(); rowIndex++) {
            Map<Integer, String> dataMap = sheetDataList.get(rowIndex);
            try {
                // 从Excel中获取所有必要信息，使用按列位置的方式获取
                String yearMonth = ExcelUtil.getValueByPosition(dataMap, BfQuotaEnum.YM.getDescription(), "");
                String businessModel = null;
                String icgNo = null;
                String riskClassCode = null;
                String quotaDefCode = null;

                if (ObjectUtils.isEmpty(businessSourceCode)) {
                    businessModel = ExcelUtil.getValueByPosition(dataMap, 1, "");
                    icgNo = ExcelUtil.getValueByPosition(dataMap, 2, "");
                    riskClassCode = ExcelUtil.getValueByPosition(dataMap, 3, "");
                    quotaDefCode = ExcelUtil.getValueByPosition(dataMap, 4, "");
                } else {
                    businessModel =  businessSourceCode;
                    icgNo = ExcelUtil.getValueByPosition(dataMap, BfQuotaEnum.ICG.getDescription(), "");
                    riskClassCode = ExcelUtil.getValueByPosition(dataMap, BfQuotaEnum.RISK.getDescription(), "");
                    quotaDefCode = ExcelUtil.getValueByPosition(dataMap, 3, "");
                }
                // 验证必填字段
                if (StringUtil.isEmpty(yearMonth)) {
                    LOG.warn("第 {} 行数据缺少评估期，跳过处理", rowIndex);
                    continue;
                }
                if (StringUtil.isEmpty(businessModel)) {
                    LOG.warn("第 {} 行数据缺少业务类型，跳过处理", rowIndex);
                    continue;
                }

                if (StringUtil.isEmpty(riskClassCode)) {
                    LOG.warn("第 {} 行数据缺少险类，跳过处理", rowIndex);
                    continue;
                }

                if (StringUtil.isEmpty(icgNo)) {
                    LOG.warn("第 {} 行数据缺少合同组，跳过处理", rowIndex);
                    continue;
                }

                // 查找假设定义 - 处理可能有编码-名称格式的情况
                String quotaCode = quotaDefCode.contains("：") ? quotaDefCode.split("：")[0] : quotaDefCode.contains(":") ? quotaDefCode.split(":")[0] : quotaDefCode;

                Optional<AtrConfQuotaDef> quotaDefOpt = nonDevelopQuotaDefList.stream()
                        .filter(def -> def.getQuotaCode().equals(quotaCode))
                        .findFirst();

                if (!quotaDefOpt.isPresent()) {
                    LOG.warn("第 {} 行数据的假设编码 '{}' 无效，跳过处理", rowIndex,  quotaCode);
                    continue;
                }

                String finalRiskClassCode = riskClassCode;
                String finalBusinessModel = businessModel;
                String finalIcgNo = icgNo;


                AtrConfQuotaVo qtcConfQuota = new AtrConfQuotaVo();
                qtcConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
                qtcConfQuota.setYearMonth(yearMonth);
                qtcConfQuota.setQuotaClass(atrConfQuotaVo.getQuotaClass());
                qtcConfQuota.setDimension("C");
                qtcConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
                qtcConfQuota.setAuditState(CommonConstant.AuditStatus.APPROVED);
                qtcConfQuota.setRiskClassCode(finalRiskClassCode);
                qtcConfQuota.setSerialNo(1);
                qtcConfQuota.setBusinessSourceCode(finalBusinessModel);
                qtcConfQuota.setDimensionValue(finalIcgNo);
                qtcConfQuota.setQuotaDefId(quotaDefOpt.get().getQuotaDefId());
                qtcConfQuota.setQuotaValue(null);

                // 处理发展期数据
                List<AtrConfQuotaDetailVo> qtcConfQuotaDetailVos = new ArrayList<>();
                // 直接从第6列开始，将第6列作为第1期，第7列作为第2期，依此类推
                // 通过检查列是否有值来判断有多少期的假设值
                int periodIndex = 1; // 期次从1开始
                int colIndex = ObjectUtils.isEmpty(businessSourceCode) ? 5:4; // 列索引从第6列开始

                while (dataMap.containsKey(colIndex)) {
                    String periodValue = ExcelUtil.getValueByPosition(dataMap, colIndex, "");
                    if (!StringUtil.isEmpty(periodValue)) {
                        AtrConfQuotaDetailVo detailVo = new AtrConfQuotaDetailVo();
                        detailVo.setQuotaPeriod((long) periodIndex);
                        detailVo.setSerialNo(periodIndex);
                        detailVo.setQuotaValue(periodValue);
                        qtcConfQuotaDetailVos.add(detailVo);
                        LOG.debug("第 {} 行数据，第 {} 期，假设值: {}", rowIndex, periodIndex, periodValue);
                    }
                    periodIndex++;
                    colIndex++;
                }
                if (!qtcConfQuotaDetailVos.isEmpty()) {
                    qtcConfQuota.setAtrConfQuotaDetailVoList(qtcConfQuotaDetailVos);
                    qtcConfQuotas.add(qtcConfQuota);
                    LOG.debug("成功解析第 {} 行数据，假设编码: {}, 发展期数据: {} 条",
                            rowIndex,  quotaCode, qtcConfQuotaDetailVos.size());
                } else {
                    LOG.warn("第 {} 行数据没有有效的发展期数据，跳过处理", rowIndex);
                }
            } catch (Exception e) {
                LOG.error("解析第 {} 行数据时发生错误: {}", rowIndex, e.getMessage(), e);
            }
        }

        LOG.info("非发展期数据解析完成，共解析出 {} 条有效记录", qtcConfQuotas.size());
        return qtcConfQuotas;
    }
}
