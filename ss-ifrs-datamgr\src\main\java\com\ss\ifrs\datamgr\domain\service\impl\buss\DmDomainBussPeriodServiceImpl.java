package com.ss.ifrs.datamgr.domain.service.impl.buss;

import com.ss.ifrs.datamgr.dao.domain.buss.DmDomainBussPeriodDao;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.datamgr.domain.service.buss.DmDomainBussPeriodService;
import com.ss.platform.core.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DmDomainBussPeriodServiceImpl implements DmDomainBussPeriodService {

    @Autowired
    DmDomainBussPeriodDao dmDomainBussPeriodDao;

    @Override
    public String getValidYearMonth(Long entityId, String yearMonth, String bizCode) {
        String validYearMonth = dmDomainBussPeriodDao.getValidYearMonth(entityId, yearMonth, bizCode);
        return validYearMonth;
    }

    @Override
    public String getPreparingYearMonth(Long entityId) {
        return getPreparingYearMonth(entityId, null);
    }

    @Override
    public String getPreparingYearMonth(Long entityId, String bizCode) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PREPARING);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.PREPARING);
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.INPUT);
        String currentYearMonth = dmDomainBussPeriodDao.getMinYearMonth(bussPeriodReqVo);
        return currentYearMonth;
    }

    @Override
    public String getPreparedYearMonth(Long entityId) {
        return getPreparedYearMonth(entityId, null);
    }

    @Override
    public String getPreparedYearMonth(Long entityId, String bizCode) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PREPARED);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.READIED);
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.INPUT);
        String currentYearMonth = dmDomainBussPeriodDao.getMinYearMonth(bussPeriodReqVo);
        return currentYearMonth;
    }

    @Override
    public String getCurrentYearMonth(Long entityId) {
        return getCurrentYearMonth(entityId, null);
    }

    @Override
    public String getCurrentYearMonth(Long entityId, String bizCode) {

        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
        bussPeriodReqVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.PREPARING);
        bussPeriodReqVo.setDirection(CommonConstant.BussPeriod.DetailBizDirection.OUTPUT);
        String currentYearMonth = dmDomainBussPeriodDao.getMinYearMonth(bussPeriodReqVo);
        return currentYearMonth;
    }

    @Override
    public String getCurrentYearMonth(Long entityId, String yearMonth, String bizCode,String periodState,String readyState,String direction) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setBizCode(bizCode);
        bussPeriodReqVo.setPeriodState(periodState);
        bussPeriodReqVo.setReadyState(readyState);
        bussPeriodReqVo.setDirection(direction);
        return dmDomainBussPeriodDao.getMinYearMonth(bussPeriodReqVo);
    }

    @Override
    public String getMaxYearMonth(Long entityId) {
        String maxYearMonth = dmDomainBussPeriodDao.getMaxYearMonth(entityId);
        return maxYearMonth;
    }

    @Override
    public String getYearMonth(Long entityId, String yearMonth,String periodState) {
        return dmDomainBussPeriodDao.getYearMonth(entityId,yearMonth,periodState);
    }

    @Override
    public List<String> listCmunitIdentifyYear(Long entityId) {
        return dmDomainBussPeriodDao.listCmunitIdentifyYear(entityId);
    }

}
