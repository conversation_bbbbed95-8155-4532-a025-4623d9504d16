package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.*;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface AtrBussAutoquotaService {

    String saveAction(AtrBussAutoquotaDrawVo vo, Long userId);

    void deleteAction(long id);

    void approveAction(AtrBussAutoquotaApproveVo vo, Long userId);

    void saveConfWeight(List<AtrBussAutoquotaWeightDetailVo> vos, Long userId);

    Page<AtrBussAutoquotaActionVo> queryPage(AtrBussAutoquotaActionVo vo, Pageable pageParam);

    AtrBussAutoquotaWeightResultVo queryBussWeight(String actionNo);
    BaseResponse<?> doAction(String actionNo);
}
