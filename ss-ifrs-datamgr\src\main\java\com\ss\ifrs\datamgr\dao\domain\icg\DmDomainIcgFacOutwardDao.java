package com.ss.ifrs.datamgr.dao.domain.icg;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.mapstruct.Mapper;

@Mapper
public interface DmDomainIcgFacOutwardDao {

    Long getCmunitNoSeqNo();

    String getCmunitNoSeqSql();

    /**
     * 获取计量单元编码分片数据
     * @return
     */
    String getSegment1st();
    String getSegment2nd();
    String getSegment3rd();
    String getSegment4th();
    String getSegment5th();
    String getSegment6th();
    String getSegment7th();
    String getSegment8th();
    String getSegment9th();

}
