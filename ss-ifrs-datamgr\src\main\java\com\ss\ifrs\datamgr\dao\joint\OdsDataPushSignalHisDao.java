package com.ss.ifrs.datamgr.dao.joint;

import com.ss.ifrs.datamgr.pojo.joint.po.DmOdsDataPushSignalHis;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12). Create Date:
 * 2022-02-14 16:28:52<br/>
 * Description: DmSrcModelSignal|模型信号表，用于记录源数据供应和处理情况 Dao类<br/>
 * Related Table Name: odsuser.ods_data_push_signal<br/>
 * <br/>
 * Remark: 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface OdsDataPushSignalHisDao extends IDao<DmOdsDataPushSignalHis, Long> {

}