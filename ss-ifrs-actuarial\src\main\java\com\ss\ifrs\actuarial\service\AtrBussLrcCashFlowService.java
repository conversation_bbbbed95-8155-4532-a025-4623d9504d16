package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AtrBussCalcService
 * @Description: 计量计算服务接口类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:56
 * @Version: 1.0
 */
public interface AtrBussLrcCashFlowService {

    //模糊查找对象信息
    Page<AtrBussLrcActionVo> findForDataTables(AtrBussLrcActionVo atrBussLrcActionVo, Pageable pageParam);
    //Lrc现金流计算
    void save(AtrBussLrcActionVo atrBussLrcActionVo, Long userId);

    AtrBussLrcActionVo findById(Long id);

    void delete(Long id, Long userId);

    Boolean confirm(AtrBussLrcActionVo atrBussLrcActionVo, Long userId);

    Page<List<Map<String, Object>>> findPeriodData(AtrDapDrawVo atrDapDrawVo, Pageable pageable);

    List<Integer> findPeriodHeaderData(AtrDapDrawVo atrDapDrawVo);

    void download(AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception;

    void download1(AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception;


    Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam);

    void calculateAll(AtrBussLrcActionVo lrcActionVo, Long userId);

    String confirmLrcCfVersion(AtrBussLrcActionVo lrcActionVo, Long userId);

    void revoke(AtrBussLrcActionVo bussLrcActionVo, Long userId);

    void calculateTransitionEarnedPremium(Long entityId, Long userId);

    void reSetLrcCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo);
}
