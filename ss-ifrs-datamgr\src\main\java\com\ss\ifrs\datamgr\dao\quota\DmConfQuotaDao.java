/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;


import com.ss.ifrs.datamgr.pojo.joint.vo.DmDrawColumnParseVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfQuota;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaDefVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaDetailVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaImportVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaPeriodVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 指标配置表 Dao类<br/>
 * Related Table Name: ATR_CONF_QUOTA<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmConfQuotaDao extends IDao<DmConfQuota, Long> {
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/13
     * @Description 模糊查询
     * @Return
     */
    Page<Map<String,Object>> fuzzySearchPage(DmConfQuotaVo bbsConfQuotaVo, Pageable pageable);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 修改审核状态
     * @Return
     */
    void updateAudit(DmConfQuotaVo bbsBussQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 激活或禁用
     * @Return
     */
    void updateValid(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 更新指标值和审核状态
     * @Return
     */
    void updateValue(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 根据Vo对象查询列表
     * @Return
     */
    List<DmConfQuotaVo> findListByVo(DmConfQuotaVo bbsConfQuotaVo);


    List<DmConfQuotaVo> selectQuotaValue(DmConfQuotaVo bbsConfQuotaVo);
    
    /**  无用代码
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15
     * @Description 查找指标定义Map
     * @Return
     */
    List<DmConfQuotaDefVo> findDefMap(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15
     * @Description 查找指标配置Vo
     * @Return
     */
    List<DmConfQuotaVo> findVoBy(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找指标定义Vo列报
     * @Return
     */
    List<DmConfQuotaDefVo> findConfQuotaVo(DmConfQuotaVo bbsConfQuotaVo);


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找发展期Vo列表
     * @Return
     */
    List<DmConfQuotaPeriodVo> findConfQuotaPeriod(DmConfQuotaVo bbsConfQuotaVo);
    
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/24
     * @Description 查找发展期Vo列表
     * @Return
     */
    List<DmConfQuotaPeriodVo> findConfQuotaPeriodByAdd();

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找发展期Vo列报
     * @Return
     */
    List<String> findConfQuotaPeriodValue(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/24
     * @Description 查找发展期Vo列报
     * @Return
     */
    List<String> findConfQuotaPeriodValueByAdd();


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找指标定义Vo列报
     * @Return
     */
    List<DmConfQuotaDefVo> findQuotaByAdd(DmConfQuotaVo bbsConfQuotaVo);


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/23
     * @Description 根据指标类型查找List
     * @Return
     */
    List<DmConfQuota> findListByVoAndType(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/23
     * @Description 根据指标类型查找List
     * @Return
     */
    DmConfQuota findByUnique(DmConfQuotaVo vo);

    /**
     * <AUTHOR>
     * @Date 2021/12/27
     * @Description 根据指标类型查找List
     * @Return
     */
    DmConfQuota findByUniqueLoa(DmConfQuotaVo vo);


    List<DmConfQuotaDetailVo> findQuotaDetailByVo(DmConfQuotaVo bbsConfQuotaVo);

    /*
    * 查询当前最大版本号
    * */
    Integer findMaxSerialNo(DmConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * @Date 2021/11/23
     * @Description 根据假设值审计信息
     */
    DmConfQuotaVo findAuditInformation(DmConfQuotaVo vo);


    List<DmConfQuotaImportVo> findImportQuotaList(DmConfQuotaImportVo dmConfQuotaImportVo);


    boolean hasSameQuota(DmConfQuota dmConfQuota);

    List<DmConfQuotaDefVo> findConfQuotaDefList(DmConfQuotaImportVo dmConfQuotaImportVo);

    void saveTableData(@Param("lists") List<List<DmDrawColumnParseVo>> lists);


    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    List<DmConfQuota> selectByLoaVo(DmConfQuota bbsConfQuota);
    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByLoaDimension(DmConfQuota bbsConfQuota);


    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息查询
     */
    List<DmConfQuota> findListByDimensionVo(DmConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByDimensionVo(DmConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByOtherModel(DmConfQuota bbsConfQuota);

    Long countQuota(DmConfQuota bbsConfQuota);

    void deleteLoadQuota(DmConfQuotaVo dmConfQuotaVo);

    String selectPreviousPeriod(DmConfQuota bbsConfQuota);

    void loadPrePeriodQuota(DmConfQuotaVo bbsConfQuota);

    void syncQuotaClassByQuotaDef(DmConfQuotaDefVo confQuotaDefVo);
}