/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-01-30 14:56:37
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.dap.rein;

import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.ifrs.datamgr.pojo.dap.po.rein.DmReinsBillDetail;
import com.ss.ifrs.datamgr.pojo.dap.vo.rein.DmReinsBillDetailVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-01-30 14:56:37<br/>
 * Description: 再保帐单信息 Dao类<br/>
 * Related Table Name: DM_REINS_BILL_DETAIL<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmReinsBillDetailDao extends IDao<DmReinsBillDetail, Long> {
    List<String> findListByBill(DmDataSearchVo dmDataSearchVo);
    List<String> findTreatyNoByList(DmDataSearchVo dmDataSearchVo);
    Page<DmReinsBillDetailVo> findDataListProtInwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmReinsBillDetailVo> findDataListInwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmReinsBillDetailVo> findDataListProtTreaty(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmReinsBillDetailVo> findDataListTreaty(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmReinsBillDetailVo> findDataListMain(DmDataSearchVo searchVo, Pageable pageParam);
}