package com.ss.ifrs.datamgr.domain.abst;

import com.ss.ifrs.datamgr.domain.service.icg.majorrisk.DmDomainMajorRiskService;
import com.ss.ifrs.datamgr.domain.service.tableDeal.DmTableDealService;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfContractRuleVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.icg.DmCmunitMajorTestVo;
import com.ss.ifrs.datamgr.service.conf.DmConfContractRuleDefService;
import com.ss.ifrs.datamgr.service.conf.DmConfContractRuleService;
import com.ss.ifrs.datamgr.service.icg.DmBussCmunitCommonService;
import com.ss.library.utils.SpringApplicationContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.*;

public abstract class DmMajorTest {

    @Autowired
    private DmConfContractRuleDefService dmConfContractRuleDefService;
    @Autowired
    private DmConfContractRuleService dmConfContractRuleService;

    @Autowired
    private DmTableDealService dmTableDealService;

    @Autowired
    private DmBussCmunitCommonService dmBussCmunitCommonService;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 获取规则
     *
     * @param entityId
     * @return 对应的规则
     */
    protected List<DmConfContractRuleVo> getRuleInfo(Long entityId, Long ruleId, String bussDefCode, String ruleType, String businessModel,String businessSubdivisionType) {

        if (ruleId == null) {
            ruleId = dmConfContractRuleDefService.getContractRuleId(entityId, bussDefCode, ruleType, businessModel,businessSubdivisionType);
        }
        return dmConfContractRuleService.getListByBusinessModel(ruleId, businessModel);


    }

    /**
     * 根据传进来的规则拆分出对应的因子
     *
     * @param rule
     * @return
     */
    protected List<String> getRuleFactory(String rule) {
        String ruleWithOutBrackets = rule.replace("(", "").replace(")", "");
        return new ArrayList<>(Arrays.asList(ruleWithOutBrackets.split("[*\\-+\\/]+")));
    }

    protected String getOtherValue(DmCmunitMajorTestVo dmCmunitMajorTestVo) {
        Boolean columnExists = dmTableDealService.isColumnExists(dmCmunitMajorTestVo.getRelatedTable(), "business_model", null);
        dmCmunitMajorTestVo.setHasBusinessModel(columnExists ? 1 : 0);
        return dmBussCmunitCommonService.getOtherInfo(dmCmunitMajorTestVo);
    }

    protected String execute(String beanName, String rule, Long cmUnitId, String businessModel, String yearMonth) {
        DmDomainMajorRiskService dmDomainMajorRiskService = (DmDomainMajorRiskService) applicationContext.getBean(beanName);
        List<String> ruleFactory = getRuleFactory(rule);
        for (int i = 0; i < ruleFactory.size(); i++) {
            String s = ruleFactory.get(i);
            if (!s.contains("[") || !s.contains("]")) {
                continue;
            }
            String factorWithOutBrackets = s.replace("[", "").replace("]", "");
            String value = dmDomainMajorRiskService.getRuleParsing(cmUnitId, factorWithOutBrackets, businessModel, yearMonth);
            rule = rule.replace(s, value);
        }
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("JavaScript");
        try {
            return engine.eval(rule).toString();
        } catch (ScriptException e) {
            return rule;
        }
    }

    /**
     * 获取比对结果
     *
     * @param firValue 实际结果值
     * @param operator 操作符
     * @param secValue 因子值
     * @return 成功为1，不成功为0
     */
    protected Long getResult(String firValue, String operator, String secValue) {
        try {
            if (StringUtils.isEmpty(firValue) || StringUtils.equals("null", firValue.toLowerCase())) {
                if (StringUtils.isNotEmpty(secValue) || !StringUtils.equals("null", secValue.toLowerCase())) {
                    if (StringUtils.equals("4", operator)) {
                        return 1L;
                    } else {
                        return 0L;
                    }
                } else if (StringUtils.isEmpty(secValue) || StringUtils.equals("null", secValue.toLowerCase())) {
                    if (StringUtils.equals("5", operator)) {
                        return 1L;
                    } else {
                        return 0L;
                    }
                }
            } else if (StringUtils.isEmpty(secValue) || StringUtils.equals("null", secValue.toLowerCase())) {
                if (StringUtils.equals("4", operator)) {
                    return 1L;
                } else {
                    return 0L;
                }
            }

            boolean flag;
            //两个因子值比较
            switch (operator) {
                case "0":
                    flag = Double.valueOf(firValue) < Double.valueOf(secValue);
                    break;
                case "1":
                    flag = Double.valueOf(firValue) <= Double.valueOf(secValue);
                    break;
                case "2":
                    flag = Double.valueOf(firValue) > Double.valueOf(secValue);
                    break;
                case "3":
                    flag = Double.valueOf(firValue) >= Double.valueOf(secValue);
                    break;
                case "4":
                    flag = !StringUtils.equals(firValue, secValue);
                    break;
                case "5":
                    flag = StringUtils.equals(firValue, secValue);
                    break;
                default:
                    flag = false;
                    break;
            }
            if (flag) {
                return 1L;
            } else {
                return 0L;
            }
        } catch (Exception e) {
            return 0L;
        }
    }
}
