package com.ss.ifrs.actuarial.util.dc;

import com.ss.ifrs.actuarial.dao.conf.AtrCodeDao;
import com.ss.library.utils.SpringContextUtil;
import org.springframework.beans.BeanUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Array;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class DataConverter {

    private final Map<List<Object>, String> cache = new HashMap<>();

    private final Map<String, Set<DictParam>> dictParamsMap = new HashMap<>();

    private String language;

    private final AtrCodeDao atrCodeDao;

    public DataConverter() {
        atrCodeDao = SpringContextUtil.getBean(AtrCodeDao.class);
    }

    public DataConverter language(String language) {
        this.language = language;
        return this;
    }

    public DataConverter addDictParam(DictParam dictParam) {
        dictParamsMap.computeIfAbsent(dictParam.getField(), k -> new HashSet<>()).add(dictParam);
        return this;
    }

    public Object toData(Object obj) {
        return toData0(obj);
    }

    /**
     * 翻译码表， 将翻译结果设置到对应的字段中
     */
    public <T> void translate(T obj) {
        if (obj == null) {
            return;
        }

        PropertyDescriptor[] pds = BeanUtils.getPropertyDescriptors(obj.getClass());
        for (PropertyDescriptor pd : pds) {
            Method writeMethod = pd.getWriteMethod();
            Method readMethod = pd.getReadMethod();
            if (writeMethod != null && readMethod != null) {
                String field = pd.getName();
                Object code = getValue(obj, pd);

                if (code == null || "".equals(code.toString())) {
                    continue;
                }

                Set<DictParam> dictParams = dictParamsMap.get(field);
                if (dictParams != null) {
                    for (DictParam dictParam : dictParams) {
                        if (field.equals(dictParam.getField())) {
                            String type = dictParam.getType();
                            Object entityId = null;

                            if (DictParam.TYPE_LOA.equals(type)) {
                                String entityIdField = dictParam.getEntityIdField();
                                for (PropertyDescriptor pd2 : pds) {
                                    if (pd2.getName().equals(entityIdField)) {
                                        entityId = getValue(obj, pd2);
                                    }
                                }
                                if (entityId == null || entityId.toString().isEmpty()) {
                                    continue;
                                }
                            }

                            String name = getAndCacheCodeName(dictParam, code, entityId);
                            setValue(obj, dictParam.getNameField(), name);
                        }
                    }
                }
            }
        }
    }

    /**
     * 翻译码表， 将翻译结果设置到对应的字段中
     */
    public <T> void translate(List<T> objs) {
        if (objs == null || objs.isEmpty()) {
            return;
        }
        for (T obj : objs) {
            translate(obj);
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private Object toData0(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof Date) {
            return obj;
        } else if (CharSequence.class.isAssignableFrom(obj.getClass())) {
            return obj;
        } else if (Number.class.isAssignableFrom(obj.getClass())) {
            return obj;
        } else if (obj instanceof Collection || obj.getClass().isArray()) {
            Object la = obj;
            if (obj instanceof Set) {
                la = new ArrayList<>((Set) obj);
            }
            int len = len(la);
            List<Object> list = new ArrayList<>();
            for (int i = 0; i < len; i++) {
                list.add(toData0(valueAt(la, i)));
            }
            return list;
        } else if (obj instanceof Map) {
            Map map = (Map) obj;
            Map<Object, Object> newMap = new LinkedHashMap<>();
            for (Object key : map.keySet()) {
                Object value = map.get(key);
                value = toData0(value);
                newMap.put(key, value);
            }
            addCodeName(newMap);
            return newMap;
        } else {
            Map<Object, Object> map = new LinkedHashMap<>();
            PropertyDescriptor[] pds = BeanUtils.getPropertyDescriptors(obj.getClass());
            for (PropertyDescriptor pd : pds) {
                String fieldName = pd.getName();
                if (pd.getReadMethod() == null) {
                    continue;
                }
                if ("class".equals(fieldName)) {
                    continue;
                }
                Object value = getValue(obj, pd);
                value = toData0(value);
                map.put(fieldName, value);
            }
            addCodeName(map);
            return map;
        }
    }

    private void addCodeName(Map<Object, Object> map) {
        Map<String, Object> nameMap = new LinkedHashMap<>();

        for (Object key : map.keySet()) {
            if (key instanceof String) {
                String field = (String) key;
                Object code = map.get(field);
                if (code == null || code.toString().isEmpty()) {
                    continue;
                }

                Set<DictParam> dictParams = dictParamsMap.get(field);
                if (dictParams != null) {
                    for (DictParam dictParam : dictParams) {
                        if (field.equals(dictParam.getField())) {
                            String type = dictParam.getType();
                            Object entityId = null;

                            if (DictParam.TYPE_LOA.equals(type)) {
                                String entityIdField = dictParam.getEntityIdField();
                                entityId = map.get(entityIdField);
                                if (entityId == null || entityId.toString().isEmpty()) {
                                    continue;
                                }
                            }

                            String name = getAndCacheCodeName(dictParam, code, entityId);
                            nameMap.put(dictParam.getNameField(), name);
                        }
                    }
                }
            }
        }

        map.putAll(nameMap);
    }

    private String getAndCacheCodeName(DictParam param, Object code, Object entityId) {
        String type = param.getType();
        String codeType = param.getCodeType();

        List<Object> key = new ArrayList<>();
        key.add(type);
        key.add(codeType);
        key.add(code.toString());
        key.add(entityId);

        if (cache.containsKey(key)) {
            return cache.get(key);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("language", language);
        paramMap.put("codeType", codeType);
        paramMap.put("code", code);
        paramMap.put("entityId", entityId);

        String name;
        if (DictParam.TYPE_ATR_CODE.equals(type)) {
            name = atrCodeDao.getAtrCodeName(paramMap);
        } else if (DictParam.TYPE_BPL_CODE.equals(type)) {
            name = atrCodeDao.getBplCodeName(paramMap);
        } else if (DictParam.TYPE_ENTITY_NAME.equals(type)) {
            name = atrCodeDao.getEntityName(paramMap);
        } else if (DictParam.TYPE_ENTITY_CODE.equals(type)) {
            name = atrCodeDao.getEntityCode(paramMap);
        } else if (DictParam.TYPE_USER.equals(type)) {
            name = atrCodeDao.getUserName(paramMap);
        } else if (DictParam.TYPE_LOA.equals(type)) {
            name = atrCodeDao.getLoaName(paramMap);
        } else {
            throw new RuntimeException("Unsupport type = " + type + ", codeType = " + codeType);
        }
        cache.put(key, name);
        return name;
    }

    private int len(Object listOrArray) {
        if (listOrArray == null) {
            return 0;
        } else if (listOrArray instanceof List) {
            return ((List<?>) listOrArray).size();
        } else if (listOrArray.getClass().isArray()) {
            return Array.getLength(listOrArray);
        } else {
            throw new RuntimeException("No support type " + listOrArray.getClass());
        }
    }

    private Object valueAt(Object listOrArray, int index) {
        if (listOrArray == null) {
            return null;
        } else if (listOrArray instanceof List) {
            return ((List<?>) listOrArray).get(index);
        } else if (listOrArray.getClass().isArray()) {
            return Array.get(listOrArray, index);
        } else {
            throw new RuntimeException("No support type " + listOrArray.getClass());
        }
    }

    private Object getValue(Object obj, PropertyDescriptor pd) {
        try {
            return pd.getReadMethod().invoke(obj);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get the value of field " + pd.getName(), e);
        }
    }

    private void setValue(Object obj, String field, Object value) {
        try {
            PropertyDescriptor[] pds = BeanUtils.getPropertyDescriptors(obj.getClass());
            for (PropertyDescriptor pd : pds) {
                if (pd.getName().equals(field)) {
                    pd.getWriteMethod().invoke(obj, value);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to get the value of field " + field, e);
        }
    }

}
