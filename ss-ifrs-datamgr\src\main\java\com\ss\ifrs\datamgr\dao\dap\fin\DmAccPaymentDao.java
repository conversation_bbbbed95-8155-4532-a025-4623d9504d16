/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2020-12-18 18:23:02
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.dap.fin;

import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.ifrs.datamgr.pojo.dap.po.fin.DmAccPayment;
import com.ss.ifrs.datamgr.pojo.dap.vo.fin.DmAccPaymentVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2020-12-18 18:23:02<br/>
 * Description: I17直保财务收付 Dao类<br/>
 * Related Table Name: DM_ACC_PAYMENT<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmAccPaymentDao extends IDao<DmAccPayment,Serializable> {
    Page<DmAccPaymentVo> findDataList(DmDataSearchVo searchVo, Pageable pageParam);
    List<String> findListByVoucher(DmDataSearchVo dmDataSearchVo);
    Page<DmAccPaymentVo> findDataListProt(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmAccPaymentVo> findDataListOutwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmAccPaymentVo> findDataListProtOutwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmAccPaymentVo> findDataListInwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmAccPaymentVo> findDataListProtInwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmAccPaymentVo> findDataListTreaty(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmAccPaymentVo> findDataListProtTreaty(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmAccPaymentVo> findDataListMain(DmDataSearchVo searchVo, Pageable pageParam);
}