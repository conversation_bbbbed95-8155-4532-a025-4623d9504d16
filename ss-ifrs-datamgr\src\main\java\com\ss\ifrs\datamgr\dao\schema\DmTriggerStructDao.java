package com.ss.ifrs.datamgr.dao.schema;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface DmTriggerStructDao {
    @Update({
            "<script>",
            "CREATE OR REPLACE TRIGGER ${schema}.${triggerName}",
            "BEFORE INSERT ON ${schema}.${tableName}",
            "FOR EACH ROW",
            "BEGIN",
            "  :new.id := ${schema}.${sequenceName}.NEXTVAL;",
            "END;",
            "</script>"
    })
    void createTrigger(@Param("triggerName") String triggerName, @Param("tableName") String tableName, @Param("sequenceName") String sequenceName, @Param("schema") String schema);

}
