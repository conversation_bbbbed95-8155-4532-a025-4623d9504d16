/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 20:54:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.datamgr.dao.dap.rein;


import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.ifrs.datamgr.pojo.dap.po.rein.DmReinsRisk;
import com.ss.ifrs.datamgr.pojo.dap.vo.rein.DmReinsRiskVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.List;

/**
 *  MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 20:54:27<br/>
 * Description: 採集合约业务风险对应再保风险合约映射资讯 Dao类<br/>
 * Related Table Name: DM_reins_risk<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmReinsRiskDao extends IDao<DmReinsRisk, Serializable> {
    Page<DmReinsRiskVo> findDataListProtTreaty(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmReinsRiskVo> findDataListTreaty(DmDataSearchVo searchVo, Pageable pageParam);
}