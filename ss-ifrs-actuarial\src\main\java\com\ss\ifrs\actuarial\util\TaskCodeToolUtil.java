package com.ss.ifrs.actuarial.util;

import java.util.concurrent.atomic.AtomicLong;
import java.util.Random;

/**
 * 全局唯一任务编码生成器
 * <AUTHOR>
 */
public class TaskCodeToolUtil {

    private static final AtomicLong LAST_TIMESTAMP = new AtomicLong(System.currentTimeMillis());
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final Random RANDOM = new Random();

    /**
     * 生成全局唯一的任务编码
     *
     * @param entityId 实体ID
     * @param yearMonth 年月
     * @return 全局唯一的任务编码字符串
     */
    public static String createActionNo(Long entityId, String yearMonth) {
        long currentTimestamp = System.currentTimeMillis();
        // 如果当前时间戳与上次时间戳相同，则递增序列号
        if (currentTimestamp == LAST_TIMESTAMP.get()) {
            long seq = SEQUENCE.incrementAndGet();
            // 如果序列号超过最大值，则等待下一毫秒
            if (seq > 999) {
                SEQUENCE.set(0);
                while (currentTimestamp == LAST_TIMESTAMP.get()) {
                    currentTimestamp = System.currentTimeMillis();
                }
            }
            return String.format("%s_%s%s_%d%03d",
                    generateRandomUpperCaseLetter(),
                    entityId,
                    yearMonth,
                    currentTimestamp,
                    seq);
        } else {
            // 更新时间戳
            LAST_TIMESTAMP.set(currentTimestamp);
            SEQUENCE.set(0);
            return String.format("%s_%s%s_%d%03d",
                    generateRandomUpperCaseLetter(),
                    entityId,
                    yearMonth,
                    currentTimestamp,
                    SEQUENCE.get());
        }
    }

    /**
     * 生成随机英文大写字母
     *
     * @return 随机英文大写字母
     */
    private static char generateRandomUpperCaseLetter() {
        return (char) ('A' + RANDOM.nextInt(26));
    }

}
