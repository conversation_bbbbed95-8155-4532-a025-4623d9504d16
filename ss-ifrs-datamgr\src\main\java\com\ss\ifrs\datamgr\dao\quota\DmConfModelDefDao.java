/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-01-05 15:08:15
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.quota;

import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfModelDef;
import com.ss.ifrs.datamgr.pojo.quota.conf.po.DmConfModelPlan;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfModelDefVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfModelPlanVo;
import com.ss.ifrs.datamgr.pojo.quota.conf.vo.DmConfQuotaVo;
import com.ss.ifrs.datamgr.pojo.quota.evaluate.po.DmBussLrcAction;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-01-05 15:08:15<br/>
 * Description: 模型定义 Dao类<br/>
 * Related Table Name: dm_conf_model_def<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmConfModelDefDao extends IDao<DmConfModelDef, Long> {

    Page<DmConfModelDefVo> fuzzySearchPage(DmConfModelDefVo vo, Pageable pageParam);

    DmConfModelDef selectVoByModelCode(String modelCode);

    List<Long> findRelationDataByModelId(Long modelDefId);

    DmConfModelDefVo findByPk(Long id);

    DmConfModelDefVo findModelByModelPlan(DmConfModelPlanVo vo);

    DmConfModelDefVo findModel(DmConfModelDefVo vo);

    List<DmConfModelDefVo> findModelPlanByBusiness(DmConfModelPlanVo dmConfModelPlanVo);

    Page<BbsConfLoaVo> findQuotaLoaVo(DmConfQuotaVo dmConfQuotaVo, Pageable pageParam);

    Integer countBecfImportConfirm(DmBussLrcAction bussAction);

    Integer countBecfCalculateConfirm(DmBussLrcAction bussAction);

    /**
     * @Description: 计量流程日志信息生成
     * @Param : [params]
     * @Return: void
     * @CreateDate: 2023/2/3 20:46
     */
    void saveDmActionLog(Map<String,Object> params);

    List<DmConfModelDefVo> findModelLoaList(DmConfModelPlan dmConfModelPlan);
}