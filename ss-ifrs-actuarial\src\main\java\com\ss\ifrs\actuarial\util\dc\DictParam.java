package com.ss.ifrs.actuarial.util.dc;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Setter @Getter
public class DictParam {

    public static final String TYPE_ATR_CODE = "atr_code";
    public static final String TYPE_BPL_CODE = "bpl_code";
    public static final String TYPE_USER = "user";
    public static final String TYPE_ENTITY_NAME = "entity_name";
    public static final String TYPE_ENTITY_CODE = "entity_code";
    public static final String TYPE_LOA = "loa";

    private String type;

    private String codeType;

    private String field;

    private String entityIdField;

    private String nameField;

    public static DictParam ofAtrCode(String field, String nameField, String codeType) {
        DictParam param = new DictParam();
        param.type = TYPE_ATR_CODE;
        param.field = field;
        param.nameField = nameField;
        param.codeType = codeType;
        return param;
    }

    public static DictParam ofAtrCode(String field, String codeType) {
        DictParam param = new DictParam();
        param.type = TYPE_ATR_CODE;
        param.field = field;
        param.nameField = field + "Name";
        param.codeType = codeType;
        return param;
    }

    public static DictParam ofBplCode(String field, String nameField, String codeType) {
        DictParam param = new DictParam();
        param.type = TYPE_BPL_CODE;
        param.field = field;
        param.nameField = nameField;
        param.codeType = codeType;
        return param;
    }

    public static DictParam ofLoa(String field, String entityIdField, String nameField) {
        DictParam param = new DictParam();
        param.type = TYPE_LOA;
        param.field = field;
        param.entityIdField = entityIdField;
        param.nameField = nameField;
        return param;
    }

    public static DictParam ofLoa() {
        return ofLoa("loaCode", "entityId", "loaName");
    }

    public static DictParam ofUser(String field, String nameField) {
        DictParam param = new DictParam();
        param.type = TYPE_USER;
        param.field = field;
        param.nameField = nameField;
        return param;
    }

    public static DictParam ofUser(String field) {
        return ofUser(field, field + "Name");
    }

    public static DictParam ofEntityName(String field, String nameField) {
        DictParam param = new DictParam();
        param.type = TYPE_ENTITY_NAME;
        param.field = field;
        param.nameField = nameField;
        return param;
    }

    public static DictParam ofEntityName() {
        return ofEntityName("entityId", "entityName");
    }

    public static DictParam ofEntityCode(String field, String nameField) {
        DictParam param = new DictParam();
        param.type = TYPE_ENTITY_CODE;
        param.field = field;
        param.nameField = nameField;
        return param;
    }

    public static DictParam ofEntityCode() {
        return ofEntityCode("entityId", "entityCode");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DictParam param = (DictParam) o;
        return Objects.equals(type, param.type)
                && Objects.equals(codeType, param.codeType)
                && Objects.equals(field, param.field)
                && Objects.equals(entityIdField, param.entityIdField)
                && Objects.equals(nameField, param.nameField);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, codeType, field, entityIdField, nameField);
    }
}
