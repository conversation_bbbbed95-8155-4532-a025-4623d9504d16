package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmDomainProfitableDdPaaDao {

    void deleteDuctCmunitProfitable(DmIcgProcVo dmIcgProcVo);

    void insertDuctCmunitProfitable(DmIcgProcVo dmIcgProcVo);

    int getCountDuctCmunitProfitable(String dataKey,String nodeState);

    /*
     *S001,S002集合保费，费用
     */
    void deleteSetAmount(String dataKey);
    void calCmSetAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *跟單IACF率
     */
    void calIacfRate(DmIcgProcVo dmIcgProcVo);

    /*
     *预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
     */
    void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *预期赔付现金流现值
     */
    void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *集合成本率
     */
    void calCmSetCostRate(DmIcgProcVo dmIcgProcVo);

    /*
     *成本率
     */
    void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo);

    /*
     *盈亏结果
     */
    void Profitable(DmIcgProcVo dmIcgProcVo);

    /*
     * 更新盈亏参数校验失败结果
     * */
    void updateProfitableEstimateCheckFail(String dataKey);


    /*
     * 更新盈亏结果
     * */
    void updateProfitableEstimateResult(DmIcgProcVo dmIcgProcVo);
}
