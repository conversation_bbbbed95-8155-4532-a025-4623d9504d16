package com.ss.ifrs.datamgr.dao.domain.dap;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.*;
import com.ss.platform.pojo.com.po.SliceAttributes;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmDapModelDao {

    Integer getOdsVerifyDataCount(DmBussDataVerifyVo dmBussDataVerifyVo);

    String getTableBusinessNo(DmBussDataVerifyVo dmBussDataVerifyVo);

    Integer getSuccessDataCount(DmBussDataVerifyVo dmBussDataVerifyVo);

    DapModelColumnMappingVo getMappingColumn(Long bizTypeId);

    DapModelColumnRefMappingVo getRefMappingColumn(String bizCode,Long bizTypeId);

    DapModelColumnMappingVo getOriginalColumn();

    String getTableNameRefExt(String bizCode);

    DapModelStatResultVo getVerifyStatResult(DmBussDataVerifyVo dmBussDataVerifyVo);

    Integer insertTgtData(DapModelInsertVo dapModelInsertVo, SliceAttributes sliceParam);

    Integer updateFinalStatus(DapModelConditionVo dapModelConditionVo, SliceAttributes sliceParam);

    Integer updateTgtStatus(DmBussDataVerifyVo dmBussDataVerifyVo, SliceAttributes sliceParam);

    Integer updateInitStatus(DmBussDataVerifyVo dmBussDataVerifyVo, SliceAttributes sliceParam);

    void updatePolicyVaildStatus(DmBussDataVerifyVo dmBussDataVerifyVo,String ruleSql);


    Integer deleteOdsData(DapModelConditionVo dapModelConditionVo);

    Integer deleteTgtData(DmBussDataVerifyVo dmBussDataVerifyVo, SliceAttributes sliceParam);

    Integer deleteCmunitDirect(DmBussDataVerifyVo dmBussDataVerifyVo);

    Integer deleteCmunitFacOut(DmBussDataVerifyVo dmBussDataVerifyVo);

    Integer deleteCmunitTreatyInward(DmBussDataVerifyVo dmBussDataVerifyVo);
    Integer deleteCmunitTreatyOutward(DmBussDataVerifyVo dmBussDataVerifyVo);

}
