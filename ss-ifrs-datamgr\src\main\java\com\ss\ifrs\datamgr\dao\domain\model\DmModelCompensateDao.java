package com.ss.ifrs.datamgr.dao.domain.model;

import com.ss.ifrs.datamgr.pojo.model.vo.DmModelCompensateCdtVo;
import com.ss.ifrs.datamgr.pojo.model.vo.DmModelCompensateDataVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DmModelCompensateDao {


    Long getModelCompensateData();

    List<DmModelCompensateDataVo> getInitCompensateVerify();

    void insertNeedCompensatePolicy(DmModelCompensateDataVo dmBussDataCompensateVo);

    Long getNeedCompensatePolicySize();

    void insertModelCompensateData(DmModelCompensateDataVo dmBussDataCompensateVo);

    void insertLogCompensatePolicy(String taskCode);

    void deleteAllNeedCompensatePolicy();

    List<DmModelCompensateDataVo> listCompensateData();

    void deleteAllModelCompensateData();
}
