package com.ss.ifrs.datamgr.domain.service.impl.duct;

import com.ss.ifrs.datamgr.dao.conf.DmConfBussPeriodDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfBussPeriodDetailDao;
import com.ss.ifrs.datamgr.dao.conf.DmConfTableDao;
import com.ss.ifrs.datamgr.dao.domain.stat.DmDomainDuctStatCustDao;
import com.ss.ifrs.datamgr.dao.joint.OdsDataPushSignalDao;
import com.ss.ifrs.datamgr.dao.schema.DmTableStructDao;
import com.ss.ifrs.datamgr.domain.service.duct.DmDomainDuctStatService;
import com.ss.ifrs.datamgr.domain.service.tableDeal.DmTableDealService;
import com.ss.ifrs.datamgr.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodDetailVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfBussPeriodVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfTableVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DapModelStatResultVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmCheckOdsTotalVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmParingStatVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmStatNeedAmountVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.stat.DmStatVo;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.List;

@Slf4j
@Service
public class DmDomainDuctStatServiceImpl implements DmDomainDuctStatService {

    @Resource
    private DmDomainDuctStatCustDao dmDomainDuctStatCustDao;

    @Resource
    private BbsConfEntityFeignClient bbsConfEntityFeignClient;

    @Resource
    private DmConfTableDao dmConfTableDao;

    @Resource
    private DmTableStructDao dmTableStructDao;

    @Resource
    private OdsDataPushSignalDao odsDataPushSignalDao;

    @Resource
    private DmConfBussPeriodDao dmConfBussPeriodDao;

    @Resource
    private DmConfBussPeriodDetailDao dmConfBussPeriodDetailDao;

    @Resource
    private DataSource dataSource;

    @Resource
    private DmTableDealService dmTableDealService;

    @Override
    public void updateBussprdDtlAll(Long entityId, String yearMonth, Long bizTypeId) {
        if (null == bizTypeId) {
            List<DmConfTableVo> tablesOrdered = dmConfTableDao.findTablesOrdered();
            for (DmConfTableVo tableInfo : tablesOrdered) {
                this.updateBussprdDtl(entityId, yearMonth, tableInfo.getBizTypeId());
            }
        } else {
            this.updateBussprdDtl(entityId, yearMonth, bizTypeId);
        }
    }

    @Override
    public void updateBussprdDtl(Long entityId, String yearMonth, Long bizTypeId) {
        if (null == bizTypeId) {
            return;
        }
        DmConfTableVo dmConfTableVo = dmConfTableDao.findVoById(bizTypeId);
        if (null == dmConfTableVo) {
            log.info("[EXCEPTION][数据统计]统计数量-proc_update_bussprd_dtl: 无效模型");
            return;
        }

        String checkStatus = this.checkStatCount(entityId, yearMonth, bizTypeId);
        if (checkStatus.equals("1")) {
            List<String> taskCodeList = odsDataPushSignalDao.queryTaskCodePushSignal(yearMonth, dmConfTableVo.getBizCode(), "3");
            if (taskCodeList.isEmpty()) {
                log.info("[EXCEPTION][数据统计]统计数量-proc_update_bussprd_dtl: 无效信号表数据");
                return;
            }
            DmConfBussPeriodVo dmConfBussPeriodParam = new DmConfBussPeriodVo();
            dmConfBussPeriodParam.setEntityId(entityId);
            dmConfBussPeriodParam.setYearMonth(yearMonth);
            DmConfBussPeriodVo dmConfBussPeriodVo = dmConfBussPeriodDao.findByVo(dmConfBussPeriodParam);
            if (null == dmConfBussPeriodVo) {
                log.info("[EXCEPTION][数据统计]统计数量-proc_update_bussprd_dtl: 无效月份-{}", yearMonth);
                return;
            }
            DmConfBussPeriodDetailVo dmConfBussPeriodDetailVo = new DmConfBussPeriodDetailVo();
            dmConfBussPeriodDetailVo.setReadyState("1");
            dmConfBussPeriodDetailVo.setExecResult("success");
            dmConfBussPeriodDetailVo.setBussPeriodId(dmConfBussPeriodVo.getBussPeriodId());
            dmConfBussPeriodDetailVo.setBizTypeId(bizTypeId);
            dmConfBussPeriodDetailVo.setDirection("1");
            dmConfBussPeriodDetailDao.updateReadyState(dmConfBussPeriodDetailVo);
        } else if (checkStatus.equals("2")) {
            List<String> restStatTaskCodeList = odsDataPushSignalDao.getRestStatTaskCode(yearMonth, dmConfTableVo.getBizCode());
            for (String taskCode : restStatTaskCodeList) {
                this.paringStat(entityId, taskCode, bizTypeId);
            }
        }


    }

    @Override
    public String checkStatCount(Long entityId, String yearMonth, Long bizTypeId) {
        BbsConfEntityVo entityInfo = bbsConfEntityFeignClient.findByEntityId(entityId);
        if (null == entityInfo) {
            return "0";
        }
        DmConfBussPeriodVo dmConfBussPeriodParam = new DmConfBussPeriodVo();
        dmConfBussPeriodParam.setEntityId(entityId);
        dmConfBussPeriodParam.setYearMonth(yearMonth);
        dmConfBussPeriodParam.setPeriodState("0");
        DmConfBussPeriodVo dmConfBussPeriodVo = dmConfBussPeriodDao.findByVo(dmConfBussPeriodParam);
        if (null == dmConfBussPeriodVo) {
            return "0";
        }
        DmConfTableVo confTableVo = dmConfTableDao.findVoById(bizTypeId);
        String bizCode = confTableVo.getBizCode();
        String odsTableName = "ODS_" + confTableVo.getBizCode();
        String dmStatTableName = "DM_STAT_" + confTableVo.getBizCode();
        List<String> taskCodeList = odsDataPushSignalDao.queryTaskCodePushSignal(yearMonth, bizCode, "3");
        if (taskCodeList.isEmpty()) {
            return "0";
        }
        Integer isHaveEntityCode = dmDomainDuctStatCustDao.queryTableHaveEntityCode(dmStatTableName);
        DmCheckOdsTotalVo vo = new DmCheckOdsTotalVo();
        vo.setStatTableName(dmStatTableName);
        vo.setOdsTableName(odsTableName);
        if (isHaveEntityCode > 0) {
            vo.setEntityCode(entityInfo.getEntityCode());
        }
        for (String taskCode : taskCodeList) {
            vo.setTaskCode(taskCode);
            Integer count = dmDomainDuctStatCustDao.checkOdsTableDataTotal(vo);
            if (count > 0) {
                return "2";
            }
        }

        return "1";
    }

    @Override
    public void paringStat(Long entityId, String taskCode, Long bizTypeId) {
        //查询是否为需要统计金额的
        DmStatNeedAmountVo dmStatNeedAmountVo = dmDomainDuctStatCustDao.queryIsNeedStatAmount(bizTypeId);
        if (null == dmStatNeedAmountVo) {
            return;
        }
        String statTableName = "DM_STAT_" + dmStatNeedAmountVo.getBizCode();
        String odsTableName = "ODSUSER.ODS_" + dmStatNeedAmountVo.getBizCode();
        String statTableSequence = "DM_SEQ_STAT_" + dmStatNeedAmountVo.getBizCode();

        //查询entityCode
        BbsConfEntityVo entityInfo = bbsConfEntityFeignClient.findByEntityId(entityId);
        Integer isHaveEntityCode = dmDomainDuctStatCustDao.queryTableHaveEntityCode("ODS_"+dmStatNeedAmountVo.getBizCode());

        if (null == entityInfo && isHaveEntityCode != 0) {
            //模型存在entity_code 但查不到时返回
            return;
        }

        //清除old数据
        if (isHaveEntityCode > 0 && !dmStatNeedAmountVo.getBizCode().equals("BASE_ENTITY")) {
            dmDomainDuctStatCustDao.cleanOldStatData(statTableName, taskCode, entityInfo.getEntityCode());
        } else {
            dmDomainDuctStatCustDao.cleanOldStatData(statTableName, taskCode, null);
        }

        //开始统计
        if (dmStatNeedAmountVo.getColumnTotal() == 0) {
            DmStatVo statAmountParam = makeStatAmountParam(statTableName, odsTableName, statTableSequence, isHaveEntityCode, entityInfo.getEntityCode(), taskCode, dmStatNeedAmountVo.getBizCode());
            dmDomainDuctStatCustDao.statAmount(statAmountParam);
        } else {
            //只统计数量
            DmStatVo statCountParam = makeStatCountParam(statTableName, odsTableName, statTableSequence, bizTypeId, isHaveEntityCode, entityInfo.getEntityCode(), taskCode, dmStatNeedAmountVo.getBizCode());
            dmDomainDuctStatCustDao.statCount(statCountParam);
        }
    }

    @Override
    public void paringStatEtl(Long entityId, String taskCode, Long bizTypeId) {
        if (null == entityId) {
            log.info("[EXCEPTION][数据统计]统计金额-proc_paring_stat_etl:无效参数");
            return;
        }

        DmParingStatVo dmParingStatVo = new DmParingStatVo();
        dmParingStatVo.setTaskCode(taskCode);
        dmParingStatVo.setBizTypeId(bizTypeId);
        List<DmParingStatVo> dmParingStatVos = dmDomainDuctStatCustDao.queryStatSignal(dmParingStatVo);
        for (DmParingStatVo vo : dmParingStatVos) {
            this.paringStat(entityId, vo.getTaskCode(), vo.getBizTypeId());
        }
    }

    @Override
    public void paringStatReset(Long bizTypeId) {
        DmConfTableVo confTableVo = dmConfTableDao.findVoById(bizTypeId);
        if (null == confTableVo) {
            return;
        }
        String bizCode = confTableVo.getBizCode();
        String odsTableName = "ODS_" + confTableVo.getBizCode();
        String dmStatTableName = "DM_STAT_" + confTableVo.getBizCode();
        String statTableSequence = "DM_SEQ_STAT_" + confTableVo.getBizCode();

        //清理数据
        dmDomainDuctStatCustDao.cleanOldStatData(dmStatTableName, null, null);

        //查询是否为需要统计金额的
        DmStatNeedAmountVo dmStatNeedAmountVo = dmDomainDuctStatCustDao.queryIsNeedStatAmount(bizTypeId);

        DmParingStatVo dmParingStatVo = new DmParingStatVo();
        dmParingStatVo.setBizTypeId(bizTypeId);
        List<DmParingStatVo> dmParingStatVos = dmDomainDuctStatCustDao.queryStatSignal(dmParingStatVo);
        for (DmParingStatVo vo : dmParingStatVos) {
            //开始统计
            if (dmStatNeedAmountVo.getColumnTotal() == 0) {
                DmStatVo statAmountParam = makeStatAmountParam(dmStatTableName, odsTableName, statTableSequence, 0, null, vo.getTaskCode(), bizCode);
                dmDomainDuctStatCustDao.statAmount(statAmountParam);
            } else {
                //只统计数量
                DmStatVo statCountParam = makeStatCountParam(dmStatTableName, odsTableName, statTableSequence, bizTypeId, 0, null, vo.getTaskCode(), bizCode);
                dmDomainDuctStatCustDao.statCount(statCountParam);
            }
        }

    }

    @Override
    public void paringStatAddCol(Long bizTypeId, String columnName, String statType) {
        if (StringUtils.isBlank(columnName) || StringUtils.isBlank(statType)) {
            log.info("[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:无效参数");
            return;
        }
        DmConfTableVo confTableVo = dmConfTableDao.findVoById(bizTypeId);
        if (null == confTableVo) {
            log.info("[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:无效模型");
            return;
        }
        String dmStatTableName = "DM_STAT_" + confTableVo.getBizCode();
        String columnType;
        if ("Oracle".equalsIgnoreCase(getDatabaseProductName())) {
            if (statType.equals("1")) {
                columnType = "VARCHAR2(1000)";
            } else if (statType.equals("2")) {
                columnType = "NUMBER(16,2)";
            } else {
                columnType = "";
            }
        } else if ("PostgreSQL".equalsIgnoreCase(getDatabaseProductName())) {
            if (statType.equals("1")) {
                columnType = "VARCHAR(1000)";
            } else if (statType.equals("2")) {
                columnType = "int8";
            } else {
                columnType = "";
            }
        } else {
            log.info("[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:不支持的数据库类型");
            return;
        }
        dmTableDealService.alterTableAddColumn(dmStatTableName, columnName, columnType, "", "dmuser");
    }

    @Override
    public void paringStatDelCol(Long bizTypeId, String columnName) {
        if (StringUtils.isBlank(columnName)) {
            log.info("[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:无效参数");
            return;
        }
        DmConfTableVo confTableVo = dmConfTableDao.findVoById(bizTypeId);
        if (null == confTableVo) {
            log.info("[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:无效模型");
            return;
        }
        String dmStatTableName = "DM_STAT_" + confTableVo.getBizCode();
        dmTableDealService.dropColumn(dmStatTableName, columnName, "dmuser");
    }

    @Override
    public void paringStatAll() {
        List<DmConfTableVo> tableList = dmConfTableDao.findVerifyTables();
        for(DmConfTableVo vo : tableList){
            this.paringStatReset(vo.getBizTypeId());
        }
    }

    @Override
    public DapModelStatResultVo getStatStatusCount(String tableName, String taskCode) {
        return dmDomainDuctStatCustDao.getStatStatusCount(tableName, taskCode);
    }

    private DmStatVo makeStatCountParam(String statTableName, String odsTableName, String statTableSequence, Long bizTypeId, Integer isHaveEntityCode, String entityCode, String taskCode, String bizCode) {
        DmStatVo param = dmDomainDuctStatCustDao.queryStatField(bizTypeId);
        if (null == param) {
            param = new DmStatVo();
        }
        DmStatVo dmStatVo = dmDomainDuctStatCustDao.querySumField(bizTypeId);
        if (null == dmStatVo) {
            dmStatVo = new DmStatVo();
        }
        param.setStatTableName(statTableName);
        param.setOdsTableName(odsTableName);
        param.setStatTableSequence(statTableSequence);
        param.setTaskCode(taskCode);
        param.setBizCode(bizCode);
        param.setSumColumns(dmStatVo.getSumColumns());
        param.setOriSumColumns(dmStatVo.getOriSumColumns());

        if (isHaveEntityCode > 0 && !bizCode.equals("BASE_ENTITY")) {
            param.setEntityCode(entityCode);
        }
        return param;
    }

    private DmStatVo makeStatAmountParam(String statTableName, String odsTableName, String statTableSequence, Integer isHaveEntityCode, String entityCode, String taskCode, String bizCode) {
        DmStatVo param = new DmStatVo();
        param.setStatTableName(statTableName);
        param.setOdsTableName(odsTableName);
        param.setStatTableSequence(statTableSequence);
        param.setTaskCode(taskCode);
        if (isHaveEntityCode > 0 && !bizCode.equals("BASE_ENTITY") && !bizCode.equals("BASE_ENTITY_MAPPING") ) {
            param.setEntityCode(entityCode);
        }
        return param;
    }

    /**
     * 获取当前连接的数据库类型
     *
     * @return
     */
    private String getDatabaseProductName() {
        String databaseProductName;
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            databaseProductName = metaData.getDatabaseProductName();
            return databaseProductName;
        } catch (SQLException e) {
            e.printStackTrace();
            return "";
        }
    }
}
