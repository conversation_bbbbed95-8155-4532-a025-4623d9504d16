package com.ss.ifrs.datamgr.dao.domain.icg.profitable;

public interface DmDomainProfitableToBbaDao {

    /*
     *A002计量单元发展期月度已赚保费
     */
    void calCmUnitDevelopAmount();

    /*
     *跟單IACF率
     */
    void calIacfRate();

    /*
     *预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
     */
    void calCmUnitExpectFutureAmount();

    /*
     *预期赔付现金流现值
     */
    void calCmUnitExpectLossAmount();

    /*
     *成本率
     */
    void calCmUnitCostRate();

    /*
     *盈亏结果
     */
    void Profitable();
}
