package com.ss.ifrs.datamgr.dao.domain.icg;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DmDomainIcgTreatyOutwardDao {
    String getCmunitNoSeqSql();

    Long getCmunitNoSeqNo();

    String getSegment1st();

    String getSegment2nd();

    String getSegment3rd();

    String getSegment4th();

    String getSegment5th();

    String getSegment6th();

    String getSegment7th();

    String getSegment8th();

    String getSegment9th();
}
