/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-07-26 10:01:58
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.datamgr.dao.joint;

import com.ss.ifrs.datamgr.pojo.joint.po.DmBussFileExchangeModel;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelLogQueryVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelLogVo;
import com.ss.ifrs.datamgr.pojo.joint.vo.fileexchange.DmBussFileExchangeModelVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-07-26 10:01:58<br/>
 * Description: 文件交换模型表 Dao类<br/>
 * Related Table Name: dm_buss_file_exchange_model<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmBussFileExchangeModelDao extends IDao<DmBussFileExchangeModel, Long> {

    List<DmBussFileExchangeModelVo> queryForVerify(Set<Long> signalIds);
    DmBussFileExchangeModelVo queryForVerifyByModelId(Long modelId);

    Page<DmBussFileExchangeModelLogVo> queryLog(DmBussFileExchangeModelLogQueryVo vo, Pageable page);

    void updateDealFlag(@Param("signalId") Long signalId, @Param("dealFlag") String dealFlag, @Param("bizCode") String bizCode);

    List<String> queryModelDependentModels(String bizCode);

    int queryDmModelDataTotal(String tableName);

    void updateDealEndTime(@Param("signalId") Long signalId,@Param("bizCode") String bizCode);

}