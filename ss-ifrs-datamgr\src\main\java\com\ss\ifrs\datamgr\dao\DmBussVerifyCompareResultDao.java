package com.ss.ifrs.datamgr.dao;

import com.ss.ifrs.datamgr.pojo.conf.vo.DmBussVerifyCompareResultVo;
import com.ss.ifrs.datamgr.pojo.conf.vo.DmConfVerifyRuleVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DmBussVerifyCompareResultDao {

    Page<DmBussVerifyCompareResultVo> searchPage(DmBussVerifyCompareResultVo dmBussVerifyCompareResultVo, Pageable pageParam);

    Integer insertDmBussCoreValue(DmBussVerifyCompareResultVo dmBussVerifyCompareResultVo);

    Integer updateDmBussCoreValue(DmBussVerifyCompareResultVo dmBussVerifyCompareResultVo);

    Integer deleteDmBussCoreValue(Long id);

    Integer deleteByYearMonth(@Param("yearMonths")List<String> yearMonths);

    Page<DmBussVerifyCompareResultVo> selectByTemplate(String yearMonth,Pageable pageParam);

    DmBussVerifyCompareResultVo queryByYearMonthAndRuleCode(@Param("yearMonth") String yearMonth, @Param("ruleCode") String ruleCode);

}
