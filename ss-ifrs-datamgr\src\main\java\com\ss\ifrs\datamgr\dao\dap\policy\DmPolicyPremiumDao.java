package com.ss.ifrs.datamgr.dao.dap.policy;
import com.ss.ifrs.datamgr.pojo.joint.vo.DmDataSearchVo;
import com.ss.ifrs.datamgr.pojo.dap.po.policy.DmPolicyPremium;
import com.ss.ifrs.datamgr.pojo.dap.vo.policy.DmPolicyPremiumVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * Create Date: 2020-12-18 18:21:54<br/>
 * Description: I17险种保费信息表 Dao类<br/>
 * Related Table Name: DM_POLICY_PREMIUM<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmPolicyPremiumDao extends IDao<DmPolicyPremium, Serializable> {
    Page<DmPolicyPremiumVo> findDataList(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmPolicyPremiumVo> findDataListProt(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmPolicyPremiumVo> findDataListOutwards(DmDataSearchVo searchVo, Pageable pageParam);
    Page<DmPolicyPremiumVo> findDataListProtOutwards(DmDataSearchVo searchVo, Pageable pageParam);

    Page<DmPolicyPremiumVo> findDataListMain(DmDataSearchVo searchVo, Pageable pageParam);

    List<DmPolicyPremiumVo> findDataByPolicyNo(DmPolicyPremium dmPolicyPremium);
}