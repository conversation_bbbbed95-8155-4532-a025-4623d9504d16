package com.ss.ifrs.actuarial.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel工具类，用于读取Excel文件
 */
public class ExcelUtil {
    private static final Logger LOG = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * 读取Excel文件的第一个工作表
     *
     * @param file Excel文件
     * @return 工作表数据，每行数据以Map形式返回，key为列标题，value为单元格值
     */
    public static List<Map<String, String>> readExcelFirstSheet(MultipartFile file) {
        try {
            List<Map<String, String>> dataList = new ArrayList<>();
            
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                private List<String> headList = new ArrayList<>();
                
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    headMap.forEach((key, value) -> headList.add(value));
                }
                
                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    Map<String, String> rowMap = new HashMap<>();
                    rowData.forEach((key, value) -> {
                        if (key < headList.size()) {
                            rowMap.put(headList.get(key), value);
                        }
                    });
                    
                    if (!rowMap.isEmpty()) {
                        dataList.add(rowMap);
                    }
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    LOG.info("第一个工作表读取完成，共 {} 行数据", dataList.size());
                }
            }).sheet(0).doRead();
            
            return dataList;
        } catch (IOException e) {
            LOG.error("读取Excel第一个工作表时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 读取Excel文件的第二个工作表
     *
     * @param file Excel文件
     * @return 工作表数据，每行数据以Map形式返回，key为列标题，value为单元格值
     */
    public static List<Map<String, String>> readExcelSecondSheet(MultipartFile file) {
        try {
            List<Map<String, String>> dataList = new ArrayList<>();
            
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                private List<String> headList = new ArrayList<>();
                
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    headMap.forEach((key, value) -> headList.add(value));
                }
                
                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    Map<String, String> rowMap = new HashMap<>();
                    rowData.forEach((key, value) -> {
                        if (key < headList.size()) {
                            rowMap.put(headList.get(key), value);
                        }
                    });
                    
                    if (!rowMap.isEmpty()) {
                        dataList.add(rowMap);
                    }
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    LOG.info("第二个工作表读取完成，共 {} 行数据", dataList.size());
                }
            }).sheet(1).doRead();
            
            return dataList;
        } catch (IOException e) {
            LOG.error("读取Excel第二个工作表时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 按列位置读取Excel文件的第一个工作表
     * 不依赖列名，而是按照列的位置索引读取，这样即使列名变更也不会影响数据读取
     *
     * @param file Excel文件
     * @return 工作表数据，每行数据以Map形式返回，key为列位置索引(0开始)，value为单元格值
     */
    public static List<Map<Integer, String>> readExcelFirstSheetByPosition(MultipartFile file) {
        try {
            List<Map<Integer, String>> dataList = new ArrayList<>();
            
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    // 保存表头信息，但不作为后续数据读取的键
                    LOG.debug("读取到表头: {}", headMap);
                }
                
                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    if (!rowData.isEmpty()) {
                        Map<Integer, String> rowMap = new HashMap<>(rowData);
                        dataList.add(rowMap);
                    }
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    LOG.info("第一个工作表按列位置读取完成，共 {} 行数据", dataList.size());
                }
            }).sheet(0).doRead();
            
            return dataList;
        } catch (IOException e) {
            LOG.error("按列位置读取Excel第一个工作表时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 按列位置读取Excel文件的第二个工作表
     * 不依赖列名，而是按照列的位置索引读取，这样即使列名变更也不会影响数据读取
     *
     * @param file Excel文件
     * @return 工作表数据，每行数据以Map形式返回，key为列位置索引(0开始)，value为单元格值
     */
    public static List<Map<Integer, String>> readExcelSecondSheetByPosition(MultipartFile file) {
        try {
            List<Map<Integer, String>> dataList = new ArrayList<>();
            
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    // 保存表头信息，但不作为后续数据读取的键
                    LOG.debug("读取到表头: {}", headMap);
                }
                
                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    if (!rowData.isEmpty()) {
                        Map<Integer, String> rowMap = new HashMap<>(rowData);
                        dataList.add(rowMap);
                    }
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    LOG.info("第二个工作表按列位置读取完成，共 {} 行数据", dataList.size());
                }
            }).sheet(1).doRead();
            
            return dataList;
        } catch (IOException e) {
            LOG.error("按列位置读取Excel第二个工作表时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 按列位置读取Excel文件的任意工作表
     * 不依赖列名，而是按照列的位置索引读取，这样即使列名变更也不会影响数据读取
     *
     * @param file Excel文件
     * @return 工作表数据，每行数据以Map形式返回，key为列位置索引(0开始)，value为单元格值
     */
    public static List<Map<Integer, String>> readExcelRandomSheetByPosition(MultipartFile file, int sheetNo) {
        try {
            List<Map<Integer, String>> dataList = new ArrayList<>();

            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    // 保存表头信息，但不作为后续数据读取的键
                    dataList.add(headMap);
                    LOG.debug("读取到表头: {}", headMap);
                }

                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    if (!rowData.isEmpty()) {
                        Map<Integer, String> rowMap = new HashMap<>(rowData);
                        dataList.add(rowMap);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    LOG.info("第二个工作表按列位置读取完成，共 {} 行数据", dataList.size());
                }
            }).sheet(sheetNo).doRead();

            return dataList;
        } catch (IOException e) {
            LOG.error("按列位置读取Excel第二个工作表时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }


    /**
     * 检查Excel表头是否包含所需的列
     *
     * @param dataList Excel数据列表
     * @param requiredColumns 必需的列名列表
     * @return 检查结果，如果所有必需的列都存在则返回true，否则返回false
     */
    public static boolean checkRequiredColumns(List<Map<String, String>> dataList, List<String> requiredColumns) {
        if (CollectionUtils.isEmpty(dataList) || CollectionUtils.isEmpty(requiredColumns)) {
            return false;
        }
        
        Map<String, String> firstRow = dataList.get(0);
        
        for (String column : requiredColumns) {
            if (!firstRow.containsKey(column)) {
                LOG.error("Excel文件缺少必要的列: {}", column);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 安全地从Map中获取值，如果键不存在则返回默认值
     *
     * @param map 数据Map
     * @param key 键
     * @param defaultValue 默认值
     * @return 如果键存在则返回对应的值，否则返回默认值
     */
    public static String getValueSafely(Map<String, String> map, String key, String defaultValue) {
        if (map == null || !map.containsKey(key)) {
            return defaultValue;
        }
        
        String value = map.get(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 安全地从Map中按列位置获取值，如果列位置不存在则返回默认值
     *
     * @param map 数据Map，key为列位置索引(0开始)
     * @param columnIndex 列位置索引
     * @param defaultValue 默认值
     * @return 如果列位置存在则返回对应的值，否则返回默认值
     */
    public static String getValueByPosition(Map<Integer, String> map, int columnIndex, String defaultValue) {
        if (map == null || !map.containsKey(columnIndex)) {
            return defaultValue;
        }
        
        String value = map.get(columnIndex);
        return value != null ? value : defaultValue;
    }
} 