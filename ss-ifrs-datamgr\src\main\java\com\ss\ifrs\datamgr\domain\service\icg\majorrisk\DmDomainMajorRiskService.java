package com.ss.ifrs.datamgr.domain.service.icg.majorrisk;

public interface DmDomainMajorRiskService {
    String getRuleParsing(Long cmUnitId, String codeCode, String businessModel, String yearMonth);

    String getFactorValueCalc(Long cmUnitId, Long entityId, String bussDefCode, Long ruleId, String ruleType,
                              String businessModel, String yearMonth ,String businessSubdivisionType);
}
