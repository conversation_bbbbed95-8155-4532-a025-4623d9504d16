package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfRiskClassVo;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 调用BPL险种的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsConfRiskClassFeignClient {
    @ApiOperation(value = "根据Code查找对象")
    @RequestMapping(value = "/risk_class_code/find_by_code/{riskClassCode}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
     BaseResponse<BbsConfRiskClassVo> select(@PathVariable("riskClassCode") String riskClassCode);
}
