package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcCfMainVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.LinkedHashMap;
import java.util.List;

public interface AtrBussDataImportService {
    Page<AtrBussBecfMainVo> searchLicPage(AtrBussBecfMainVo atrBussBecfMainVo, Pageable pageParam);

    void licExcelImport(MultipartFile file, AtrBussBecfMainVo atrBussBecfMainVo, Long userId) throws Exception ;

    void licDataConfirm(AtrBussBecfMainVo atrBussBecfMainVo, Long userId);

    List<AtrBussBecfMainVo> findLicList(AtrBussBecfMainVo atrBussBecfMainVo);

    AtrBussBecfMainVo findLicById(Long becfMainId);

    AtrBussBecfMainVo deleteLicByBecfId(Long becfMainId);


    Page<AtrBussLrcCfMainVo> searchLrcPage(AtrBussLrcCfMainVo atrBussLrcCfMainVo, Pageable pageParam);

    void lrcExcelImport(MultipartFile file, AtrBussLrcCfMainVo atrBussLrcCfMainVo, Long userId) throws Exception ;

    void lrcDataConfirm(AtrBussLrcCfMainVo atrBussLrcCfMainVo, Long userId);

    List<AtrBussLrcCfMainVo> findLrcList(AtrBussLrcCfMainVo atrBussLrcCfMainVo);

    AtrBussLrcCfMainVo findLrcById(Long lrcCfMainId);

    LinkedHashMap<String,Object> findPeriodHeaderDataById(Long lrcCfMainId);

    LinkedHashMap<String,Object> findPeriodDataById(Long lrcCfMainId);

    AtrBussLrcCfMainVo deleteLrcByLrcCfMainId(Long lrcCfMainId);

}
