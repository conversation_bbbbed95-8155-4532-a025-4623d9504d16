package com.ss.ifrs.actuarial;

import com.ss.ifrs.actuarial.listener.AfterConfigListener;
import com.ss.library.utils.SpringContextUtil;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.ContextLoaderListener;
import org.springframework.web.context.WebApplicationContext;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletException;

@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class})
@EnableEurekaClient
@ComponentScan({"com.ss.ifrs","com.ss.platform", "com.ss.library"})
@EnableSwagger2
@EnableAsync
@EnableFeignClients
public class ActuarialServiceApplication extends SpringBootServletInitializer {
	
	@LoadBalanced
	@Bean
	RestTemplate restTemplate() {
		return new RestTemplate();
	}
	
	/** 继承SpringBootServletInitializer，重写configure，表示使用外部tomcat */
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		// 此处指向当前工程main方法执行的启动类
		// 使用外置tomcat时，需在此处添加监听，用于加载配置文件后解密
		builder.application().addListeners(new AfterConfigListener());
		// 再放入ApplicationContext
		//SpringContextUtil.setApplicationContext(builder.context());
		return builder.sources(ActuarialServiceApplication.class);
	}

	/** 继承SpringBootServletInitializer，重写onStartup，获取上下文对象**/
	@Override
	public void onStartup(ServletContext servletContext) throws ServletException {
		/* SpringBootServletInitializer 中  onStartup 实现代码*/
		this.logger = LogFactory.getLog(getClass());
		WebApplicationContext rootAppContext = createRootApplicationContext(
				servletContext);
		if (rootAppContext != null) {
			servletContext.addListener(new ContextLoaderListener(rootAppContext) {
				@Override
				public void contextInitialized(ServletContextEvent event) {
					// no-op because the application context is already initialized
				}
			});
		}
		else {
			this.logger.debug("No ContextLoaderListener registered, as createRootApplicationContext() did not return an application context");
		}
		/* SpringBootServletInitializer 中  onStartup 实现代码*/

		/* 获取上下文 */
		SpringContextUtil.setApplicationContext(rootAppContext);
	}

	public static void main(String[] args) {
		
		SpringApplication springApplication = new SpringApplication( ActuarialServiceApplication.class );
		// 使用内置tomcat时，需在此处添加监听，用于加载配置文件后解密
		springApplication.addListeners(new AfterConfigListener());
		SpringContextUtil.setApplicationContext(springApplication.run(args));
	}
	
}
