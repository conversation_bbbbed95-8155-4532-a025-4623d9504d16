package com.ss.ifrs.datamgr.domain.service.buss;

import com.ss.ifrs.datamgr.domain.exception.DmVerifyException;
import com.ss.ifrs.datamgr.pojo.log.vo.DmLogDataVerifyVo;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmBussDataVerifyVo;

public interface DmDomainDataVerifyService {

    //1、校验入口
    void dataVerify(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException;

    //2、集中处理调用
    void dataVerifyDeal(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException;

    //3、校验前调用
    void dataVerifyPrepare(DmBussDataVerifyVo dmBussDataVerifyVo);

    //4、执行校验
    void dataVerifyTesting(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException;

    //5、校验完执行结果
    void dataVerifyAfter(DmBussDataVerifyVo dmBussDataVerifyVo);

    //6、插入tgt

    void dataVerifyValidPolicy(DmBussDataVerifyVo dmBussDataVerifyVo);

    //补偿机制
    void dataVerifyCompensate(DmBussDataVerifyVo dmBussDataVerifyVo) throws DmVerifyException;

    //写入日志
    void dataVerifyLog(DmLogDataVerifyVo dmLogDataVerifyVo, DmBussDataVerifyVo dmBussDataVerifyVo);

    //查询业务期间有效性
    Boolean checkValidYearMonthIs(Long entityId, String yearMonth, String bizCode);

}
