package com.ss.ifrs.datamgr.domain.service.buss;

import java.util.List;

public interface DmDomainBussPeriodService {

    String getValidYearMonth(Long entityId, String yearMonth, String bizCode);

    String getPreparingYearMonth(Long entityId);

    String getPreparingYearMonth(Long entityId, String bizCode);

    String getPreparedYearMonth(Long entityId);

    String getPreparedYearMonth(Long entityId, String bizCode);

    String getCurrentYearMonth(Long entityId);

    String getCurrentYearMonth(Long entityId, String bizCode);

    String getCurrentYearMonth(Long entityId, String yearMonth, String bizCode,String periodState,String readyState,String direction);

    String getMaxYearMonth(Long entityId);

    String getYearMonth(Long entityId, String yearMonth,String periodState);

    List<String> listCmunitIdentifyYear(Long entityId);
}
