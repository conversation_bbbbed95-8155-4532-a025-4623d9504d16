package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitablePaaService;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_FAC_OUT + "PROFITABLE_PAA")
public class DmDomainProfitableFoPaaServiceImpl implements DmDomainProfitablePaaService {

    @Override
    public void calCmUnitDevelopAmount(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmSetAmount(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calIacfRate(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmSetCostRate(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo) {

    }

    @Override
    public void Profitable(DmIcgProcVo dmIcgProcVo) {

    }
}
