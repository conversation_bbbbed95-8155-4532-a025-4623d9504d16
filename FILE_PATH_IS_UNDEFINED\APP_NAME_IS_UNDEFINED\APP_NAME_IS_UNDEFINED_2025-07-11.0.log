[2m2025-07-11 16:19:52.190[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:19:52.214[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.fuzzySearchPage
[2m2025-07-11 16:19:52.214[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLicCashFlowVo":{"businessSourceCode":"TO","confirmIs":"","currencyCode":"","drawType":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLicCashFlowVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-11 16:19:52.216[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"TO","confirmIs":"","currencyCode":"","drawType":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","yearMonth":"202501"}
[2m2025-07-11 16:19:52.216[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:19:52.216[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:19:52.216[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-11 16:19:52.218[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-1623875601
[2m2025-07-11 16:19:52.247[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-11 16:19:52.280[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 64ms, total num： 1.
[2m2025-07-11 16:19:52.282[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-11 16:19:52.283[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), TO(String)
[2m2025-07-11 16:19:52.310[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:19:52.310[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9491e9f295fd12e8,6120265b4ecfccd1,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 94
[2m2025-07-11 16:19:55.534[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:19:55.537[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:19:55.537[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:19:55.537[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:19:55.537[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:19:55.566[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:19:55.567[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:19:55.592[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:19:55.594[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 57
[2m2025-07-11 16:19:55.595[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:19:55.595[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findPortfolioData
[2m2025-07-11 16:19:55.595[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrDapDrawVo":{"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"","id":4,"portfolioNo":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrDapDrawVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-11 16:19:55.595[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"","id":4,"portfolioNo":"","yearMonth":"202501"}
[2m2025-07-11 16:19:55.595[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:19:55.596[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:19:55.596[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-11 16:19:55.596[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:856750834
[2m2025-07-11 16:19:55.596[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-11 16:19:55.687[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 91ms, total num： 2.
[2m2025-07-11 16:19:55.687[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName,b.PORTFOLIO_NO,b.ICG_NO FROM atr_buss_lic_g b left JOIN ATR_BUSS_LIC_ACTION a on a.action_no= b.action_no left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.action_no = ? and a.entity_id = ? and a.year_month = ? group by a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name,b.PORTFOLIO_NO,b.ICG_NO ORDER BY b.ICG_NO limit 10 
[2m2025-07-11 16:19:55.687[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), 1(Long), 202501(String)
[2m2025-07-11 16:19:55.744[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-11 16:19:55.744[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,01fc18aa11d10d33,1336a2249b1ceb09,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 149
[2m2025-07-11 16:20:01.206[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:01.207[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:20:01.207[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:20:01.207[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:01.207[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:01.227[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:20:01.228[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:20:01.252[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:20:01.252[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,4c395296fc42e053,b630ef7e69e53c15,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 45
[2m2025-07-11 16:20:01.615[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:01.615[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findId
[2m2025-07-11 16:20:01.615[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11","yearMonth":"202501"}
[2m2025-07-11 16:20:01.615[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:01.616[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:01.616[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==>  Preparing: select ID FROM atr_buss_lic_g WHERE entity_id = ? and ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? and YEAR_MONTH = ? limit 1 
[2m2025-07-11 16:20:01.616[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==> Parameters: 1(Long), 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:20:01.637[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:20:01.638[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,be5b446b7d2a8552,d539dddd83253467,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 23
[2m2025-07-11 16:20:01.747[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:01.747[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:20:01.747[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"1","param1":{"$ref":"$.atrAction"},"param2":"1"}
[2m2025-07-11 16:20:01.752[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:01.752[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:01.752[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:20:01.752[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 1(String)
[2m2025-07-11 16:20:01.794[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 64
[2m2025-07-11 16:20:01.794[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 46
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"2","param1":{"$ref":"$.atrAction"},"param2":"2"}
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:20:01.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 2(String)
[2m2025-07-11 16:20:01.824[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:20:01.824[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 29
[2m2025-07-11 16:20:01.824[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:01.824[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:20:01.824[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"3","param1":{"$ref":"$.atrAction"},"param2":"3"}
[2m2025-07-11 16:20:01.825[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:01.825[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:01.825[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:20:01.825[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 3(String)
[2m2025-07-11 16:20:01.848[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:20:01.848[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 24
[2m2025-07-11 16:20:01.848[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:01.848[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:20:01.848[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"4","param1":{"$ref":"$.atrAction"},"param2":"4"}
[2m2025-07-11 16:20:01.849[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:01.849[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:01.849[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:20:01.849[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 4(String)
[2m2025-07-11 16:20:01.873[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:20:01.873[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,32cb935365f61d7f,59d231c9ed85775a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 25
[2m2025-07-11 16:20:02.382[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:02.382[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:02.382[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.showExpectedClaimTotal
[2m2025-07-11 16:20:02.382[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findClaimPaid
[2m2025-07-11 16:20:02.382[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:20:02.382[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:20:02.383[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:02.383[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:02.383[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:02.383[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:02.408[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==>  Preparing: select icg_no,acc_year_month as ACCIDENT_YEAR_MONTH FROM atr_buss_lic_g WHERE ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? GROUP BY icg_no,acc_year_month ORDER BY icg_no,acc_year_month 
[2m2025-07-11 16:20:02.408[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==>  Preparing: select m.cf_type AS cfType, a.business_source_code AS businessSourceCode, a.year_month AS yearMonth, t.icg_no AS icgNo, t.acc_year_month AS accYearMonth, sum(case when m.amount_type = 'L1' then m.amount end) AS amountT1, sum(case when m.amount_type = 'L2' then m.amount end) as amountT2, sum(case when m.amount_type = 'L3' then m.amount end) as amountT3, sum(case when m.amount_type = 'L4' then m.amount end) as amountT4, sum(case when m.amount_type = 'C1' then m.amount end) as amountT5, sum(case when m.amount_type = 'C2' then m.amount end) as amountT6 FROM atr_buss_lic_action a left JOIN atr_buss_lic_g t on a.action_no = t.action_no left JOIN atr_buss_lic_g_amount m on t.id = m.main_id WHERE a.ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? group by m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month ORDER BY m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month 
[2m2025-07-11 16:20:02.408[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:20:02.408[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 77
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDao.findDevByVo
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"accidentYearMonth":"202501","actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:20:02.460[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:20:02.461[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==>  Preparing: select a.ACC_YEAR_MONTH, dev_no, ROUND(sum(case when d.cf_type = 'OS' then d.amount end),2) OS, ROUND(sum(case when d.cf_type = 'IBNR' then d.amount end),2) IBNR, ROUND(sum(case when d.cf_type = 'ULAE' then d.amount end),2) ULAE, ROUND(sum( case when d.cf_type = 'RD' then d.amount end),2) RD, ROUND(sum(case when d.cf_type = 'RA' then d.amount end),2) RA FROM atr_buss_lic_g a, atr_buss_lic_g_dev_amount d WHERE a.id = d.main_id and a.action_no = ? and a.PORTFOLIO_NO = ? and a.ICG_NO = ? and a.ACC_YEAR_MONTH = ? and d.amount_type = 'CUR_CF' group by a.ACC_YEAR_MONTH, dev_no ORDER BY dev_no 
[2m2025-07-11 16:20:02.461[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:20:02.560[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-11 16:20:02.560[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ca18ff1822f24c2d,1dc54b5e754d2785,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 100
[2m2025-07-11 16:20:02.661[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-11 16:20:02.661[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c2ca9e6e3a8dec8b,7b7b468ff397442f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 278
[2m2025-07-11 16:21:07.806[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 16:23:49.324[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:23:49.324[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:23:49.324[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:23:49.324[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:23:49.324[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:23:49.405[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:23:49.405[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:23:49.461[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:23:49.461[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,852a25d5d990554a,894d3b0d4791476f,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 137
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findId
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11","yearMonth":"202501"}
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==>  Preparing: select ID FROM atr_buss_lic_g WHERE entity_id = ? and ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? and YEAR_MONTH = ? limit 1 
[2m2025-07-11 16:23:49.713[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==> Parameters: 1(Long), 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:23:49.771[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:23:49.772[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,8ec88f4d49fcf1d1,43fdd09aec9b3e50,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 59
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findClaimPaid
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.showExpectedClaimTotal
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:23:50.467[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:23:50.492[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==>  Preparing: select icg_no,acc_year_month as ACCIDENT_YEAR_MONTH FROM atr_buss_lic_g WHERE ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? GROUP BY icg_no,acc_year_month ORDER BY icg_no,acc_year_month 
[2m2025-07-11 16:23:50.492[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:23:50.505[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==>  Preparing: select m.cf_type AS cfType, a.business_source_code AS businessSourceCode, a.year_month AS yearMonth, t.icg_no AS icgNo, t.acc_year_month AS accYearMonth, sum(case when m.amount_type = 'L1' then m.amount end) AS amountT1, sum(case when m.amount_type = 'L2' then m.amount end) as amountT2, sum(case when m.amount_type = 'L3' then m.amount end) as amountT3, sum(case when m.amount_type = 'L4' then m.amount end) as amountT4, sum(case when m.amount_type = 'C1' then m.amount end) as amountT5, sum(case when m.amount_type = 'C2' then m.amount end) as amountT6 FROM atr_buss_lic_action a left JOIN atr_buss_lic_g t on a.action_no = t.action_no left JOIN atr_buss_lic_g_amount m on t.id = m.main_id WHERE a.ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? group by m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month ORDER BY m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month 
[2m2025-07-11 16:23:50.506[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:23:50.611[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:23:50.612[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 145
[2m2025-07-11 16:23:50.612[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:23:50.612[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDao.findDevByVo
[2m2025-07-11 16:23:50.612[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"accidentYearMonth":"202501","actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:23:50.612[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:23:50.612[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:23:50.613[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==>  Preparing: select a.ACC_YEAR_MONTH, dev_no, ROUND(sum(case when d.cf_type = 'OS' then d.amount end),2) OS, ROUND(sum(case when d.cf_type = 'IBNR' then d.amount end),2) IBNR, ROUND(sum(case when d.cf_type = 'ULAE' then d.amount end),2) ULAE, ROUND(sum( case when d.cf_type = 'RD' then d.amount end),2) RD, ROUND(sum(case when d.cf_type = 'RA' then d.amount end),2) RA FROM atr_buss_lic_g a, atr_buss_lic_g_dev_amount d WHERE a.id = d.main_id and a.action_no = ? and a.PORTFOLIO_NO = ? and a.ICG_NO = ? and a.ACC_YEAR_MONTH = ? and d.amount_type = 'CUR_CF' group by a.ACC_YEAR_MONTH, dev_no ORDER BY dev_no 
[2m2025-07-11 16:23:50.613[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:23:50.704[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-11 16:23:50.704[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,04b942d0c2fad327,1e093a4f4f6ada14,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 92
[2m2025-07-11 16:23:50.724[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-11 16:23:50.724[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6dc85bf033a56260,b33befc80d72244f,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 257
[2m2025-07-11 16:23:53.614[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:23:53.617[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:23:53.618[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"1","param1":{"$ref":"$.atrAction"},"param2":"1"}
[2m2025-07-11 16:23:53.626[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:23:53.627[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:23:53.654[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:23:53.657[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 1(String)
[2m2025-07-11 16:23:53.845[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 64
[2m2025-07-11 16:23:53.847[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 228
[2m2025-07-11 16:24:18.055[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:18.055[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:18.055[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"2","param1":{"$ref":"$.atrAction"},"param2":"2"}
[2m2025-07-11 16:24:18.055[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:18.055[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:18.079[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:18.079[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 2(String)
[2m2025-07-11 16:24:18.108[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:24:18.108[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 53
[2m2025-07-11 16:24:20.184[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:20.184[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:20.184[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"3","param1":{"$ref":"$.atrAction"},"param2":"3"}
[2m2025-07-11 16:24:20.185[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:20.185[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:20.207[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:20.207[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 3(String)
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 46
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"4","param1":{"$ref":"$.atrAction"},"param2":"4"}
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:20.230[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:20.231[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 4(String)
[2m2025-07-11 16:24:20.256[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:24:20.257[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,15f29e73564eaaf3,de309d72c01781cd,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 27
[2m2025-07-11 16:24:43.342[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:43.342[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:24:43.344[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:24:43.344[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:43.344[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:43.365[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:24:43.365[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:24:43.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:24:43.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6286b1e7ca495d1e,837ad9dac382d1e3,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 45
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findId
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11","yearMonth":"202501"}
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==>  Preparing: select ID FROM atr_buss_lic_g WHERE entity_id = ? and ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? and YEAR_MONTH = ? limit 1 
[2m2025-07-11 16:24:43.655[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==> Parameters: 1(Long), 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:24:43.679[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:24:43.679[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9db6e56b2c03e294,7b5ade1f3e76af6c,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 24
[2m2025-07-11 16:24:44.042[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:44.042[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:44.042[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"1","param1":{"$ref":"$.atrAction"},"param2":"1"}
[2m2025-07-11 16:24:44.042[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:44.042[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:44.069[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:44.069[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 1(String)
[2m2025-07-11 16:24:44.122[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 64
[2m2025-07-11 16:24:44.122[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 80
[2m2025-07-11 16:24:44.122[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:44.122[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:44.122[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"2","param1":{"$ref":"$.atrAction"},"param2":"2"}
[2m2025-07-11 16:24:44.123[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:44.123[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:44.123[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:44.123[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 2(String)
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 22
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"3","param1":{"$ref":"$.atrAction"},"param2":"3"}
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:44.144[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 3(String)
[2m2025-07-11 16:24:44.168[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:24:44.168[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 24
[2m2025-07-11 16:24:44.168[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:24:44.168[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:24:44.169[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"4","param1":{"$ref":"$.atrAction"},"param2":"4"}
[2m2025-07-11 16:24:44.169[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:24:44.169[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:24:44.169[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:24:44.170[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 4(String)
[2m2025-07-11 16:24:44.193[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:24:44.193[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,dd557797743dce04,5743795b21fa4540,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 24
[2m2025-07-11 16:25:11.045[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[ATR housekeeper][0;39m [36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m IFRSHikariCPForATR - Thread starvation or clock leap detected (housekeeper delta=53s9ms319µs900ns).
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findClaimPaid
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.showExpectedClaimTotal
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:25:11.072[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:25:11.100[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==>  Preparing: select icg_no,acc_year_month as ACCIDENT_YEAR_MONTH FROM atr_buss_lic_g WHERE ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? GROUP BY icg_no,acc_year_month ORDER BY icg_no,acc_year_month 
[2m2025-07-11 16:25:11.100[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==>  Preparing: select m.cf_type AS cfType, a.business_source_code AS businessSourceCode, a.year_month AS yearMonth, t.icg_no AS icgNo, t.acc_year_month AS accYearMonth, sum(case when m.amount_type = 'L1' then m.amount end) AS amountT1, sum(case when m.amount_type = 'L2' then m.amount end) as amountT2, sum(case when m.amount_type = 'L3' then m.amount end) as amountT3, sum(case when m.amount_type = 'L4' then m.amount end) as amountT4, sum(case when m.amount_type = 'C1' then m.amount end) as amountT5, sum(case when m.amount_type = 'C2' then m.amount end) as amountT6 FROM atr_buss_lic_action a left JOIN atr_buss_lic_g t on a.action_no = t.action_no left JOIN atr_buss_lic_g_amount m on t.id = m.main_id WHERE a.ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? group by m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month ORDER BY m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month 
[2m2025-07-11 16:25:11.100[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:25:11.100[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:25:11.152[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:25:11.152[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 80
[2m2025-07-11 16:25:11.152[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:25:11.152[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDao.findDevByVo
[2m2025-07-11 16:25:11.152[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"accidentYearMonth":"202501","actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:25:11.153[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:25:11.153[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:25:11.153[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==>  Preparing: select a.ACC_YEAR_MONTH, dev_no, ROUND(sum(case when d.cf_type = 'OS' then d.amount end),2) OS, ROUND(sum(case when d.cf_type = 'IBNR' then d.amount end),2) IBNR, ROUND(sum(case when d.cf_type = 'ULAE' then d.amount end),2) ULAE, ROUND(sum( case when d.cf_type = 'RD' then d.amount end),2) RD, ROUND(sum(case when d.cf_type = 'RA' then d.amount end),2) RA FROM atr_buss_lic_g a, atr_buss_lic_g_dev_amount d WHERE a.id = d.main_id and a.action_no = ? and a.PORTFOLIO_NO = ? and a.ICG_NO = ? and a.ACC_YEAR_MONTH = ? and d.amount_type = 'CUR_CF' group by a.ACC_YEAR_MONTH, dev_no ORDER BY dev_no 
[2m2025-07-11 16:25:11.153[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:25:11.236[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-11 16:25:11.236[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,180e6752c9f5c5aa,e00d3526fa51917e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 84
[2m2025-07-11 16:25:11.266[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-11 16:25:11.266[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6484cbf40cf4bd8,a7500606b7d50928,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 194
[2m2025-07-11 16:25:20.104[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:25:20.104[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:25:20.104[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:25:20.105[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:25:20.105[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:25:20.125[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:25:20.125[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:25:20.151[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:25:20.151[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,aed9e73ebadc7ff8,a8000c967b68a00e,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 46
[2m2025-07-11 16:25:20.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:25:20.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findId
[2m2025-07-11 16:25:20.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11","yearMonth":"202501"}
[2m2025-07-11 16:25:20.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:25:20.389[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:25:20.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==>  Preparing: select ID FROM atr_buss_lic_g WHERE entity_id = ? and ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? and YEAR_MONTH = ? limit 1 
[2m2025-07-11 16:25:20.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==> Parameters: 1(Long), 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:25:20.449[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:25:20.450[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,226be0aa7a579718,7c0be690b0fb1a51,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 61
[2m2025-07-11 16:25:20.785[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:25:20.785[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:25:20.786[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"1","param1":{"$ref":"$.atrAction"},"param2":"1"}
[2m2025-07-11 16:25:20.786[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:25:20.786[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:25:20.811[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:25:20.812[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 1(String)
[2m2025-07-11 16:25:20.847[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 64
[2m2025-07-11 16:25:20.847[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 61
[2m2025-07-11 16:26:58.900[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[ATR housekeeper][0;39m [36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m IFRSHikariCPForATR - Thread starvation or clock leap detected (housekeeper delta=1m47s856ms201µs800ns).
[2m2025-07-11 16:26:58.900[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 16:26:59.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:26:59.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.showExpectedClaimTotal
[2m2025-07-11 16:26:59.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:26:59.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:26:59.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:26:59.110[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:26:59.110[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findClaimPaid
[2m2025-07-11 16:26:59.111[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:26:59.111[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:26:59.111[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:09.566[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==>  Preparing: select icg_no,acc_year_month as ACCIDENT_YEAR_MONTH FROM atr_buss_lic_g WHERE ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? GROUP BY icg_no,acc_year_month ORDER BY icg_no,acc_year_month 
[2m2025-07-11 16:29:09.582[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:29:09.571[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[ATR housekeeper][0;39m [36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m IFRSHikariCPForATR - Thread starvation or clock leap detected (housekeeper delta=2m10s670ms155µs100ns).
[2m2025-07-11 16:29:09.652[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:29:09.662[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:29:09.662[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"2","param1":{"$ref":"$.atrAction"},"param2":"2"}
[2m2025-07-11 16:29:09.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:29:09.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:09.689[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==>  Preparing: select m.cf_type AS cfType, a.business_source_code AS businessSourceCode, a.year_month AS yearMonth, t.icg_no AS icgNo, t.acc_year_month AS accYearMonth, sum(case when m.amount_type = 'L1' then m.amount end) AS amountT1, sum(case when m.amount_type = 'L2' then m.amount end) as amountT2, sum(case when m.amount_type = 'L3' then m.amount end) as amountT3, sum(case when m.amount_type = 'L4' then m.amount end) as amountT4, sum(case when m.amount_type = 'C1' then m.amount end) as amountT5, sum(case when m.amount_type = 'C2' then m.amount end) as amountT6 FROM atr_buss_lic_action a left JOIN atr_buss_lic_g t on a.action_no = t.action_no left JOIN atr_buss_lic_g_amount m on t.id = m.main_id WHERE a.ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? group by m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month ORDER BY m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month 
[2m2025-07-11 16:29:09.689[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:29:09.689[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 130578
[2m2025-07-11 16:29:09.689[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:29:09.693[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:29:09.693[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDao.findDevByVo
[2m2025-07-11 16:29:09.693[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"accidentYearMonth":"202501","actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:29:09.738[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:29:09.738[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:09.817[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:29:09.817[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 2(String)
[2m2025-07-11 16:29:09.855[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:29:09.856[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 194
[2m2025-07-11 16:29:09.858[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==>  Preparing: select a.ACC_YEAR_MONTH, dev_no, ROUND(sum(case when d.cf_type = 'OS' then d.amount end),2) OS, ROUND(sum(case when d.cf_type = 'IBNR' then d.amount end),2) IBNR, ROUND(sum(case when d.cf_type = 'ULAE' then d.amount end),2) ULAE, ROUND(sum( case when d.cf_type = 'RD' then d.amount end),2) RD, ROUND(sum(case when d.cf_type = 'RA' then d.amount end),2) RA FROM atr_buss_lic_g a, atr_buss_lic_g_dev_amount d WHERE a.id = d.main_id and a.action_no = ? and a.PORTFOLIO_NO = ? and a.ICG_NO = ? and a.ACC_YEAR_MONTH = ? and d.amount_type = 'CUR_CF' group by a.ACC_YEAR_MONTH, dev_no ORDER BY dev_no 
[2m2025-07-11 16:29:09.860[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:29:09.926[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-11 16:29:09.928[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,35230d8d64c850b8,c02c4064fd62d6a4,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 130879
[2m2025-07-11 16:29:12.473[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:29:12.473[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:29:12.473[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"3","param1":{"$ref":"$.atrAction"},"param2":"3"}
[2m2025-07-11 16:29:12.473[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-11 16:29:12.473[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,69c21ac3ee956a4a,87c0d0d0c5168cf7,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 2780
[2m2025-07-11 16:29:12.474[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:29:12.474[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:12.474[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:29:12.476[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 3(String)
[2m2025-07-11 16:29:12.505[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:29:12.505[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 32
[2m2025-07-11 16:29:14.758[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:29:14.758[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:29:14.759[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"4","param1":{"$ref":"$.atrAction"},"param2":"4"}
[2m2025-07-11 16:29:14.759[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:29:14.759[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:14.803[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:29:14.804[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 4(String)
[2m2025-07-11 16:29:14.828[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:29:14.828[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,13a730878959d679,fb84a43ce4c6eeb2,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 69
[2m2025-07-11 16:29:38.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:29:38.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:29:38.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:29:38.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:29:38.421[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:38.442[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:29:38.442[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:29:38.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:29:38.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 47
[2m2025-07-11 16:29:38.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:29:38.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findPortfolioData
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrDapDrawVo":{"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"","id":4,"portfolioNo":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrDapDrawVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"","id":4,"portfolioNo":"","yearMonth":"202501"}
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:856750834
[2m2025-07-11 16:29:38.469[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-11 16:29:38.521[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 52ms, total num： 2.
[2m2025-07-11 16:29:38.522[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName,b.PORTFOLIO_NO,b.ICG_NO FROM atr_buss_lic_g b left JOIN ATR_BUSS_LIC_ACTION a on a.action_no= b.action_no left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.action_no = ? and a.entity_id = ? and a.year_month = ? group by a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name,b.PORTFOLIO_NO,b.ICG_NO ORDER BY b.ICG_NO limit 10 
[2m2025-07-11 16:29:38.522[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), 1(Long), 202501(String)
[2m2025-07-11 16:29:38.572[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-11 16:29:38.572[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,12774516ad26acdd,a0d93a7ea1f5dc40,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-5][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 103
[2m2025-07-11 16:31:58.904[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 16:36:20.337[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:36:20.337[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:36:20.337[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:36:20.337[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:36:20.337[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:36:20.362[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:36:20.362[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:36:20.386[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 50
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findPortfolioData
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrDapDrawVo":{"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"","id":4,"portfolioNo":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrDapDrawVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"","id":4,"portfolioNo":"","yearMonth":"202501"}
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:856750834
[2m2025-07-11 16:36:20.387[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-11 16:36:20.433[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 46ms, total num： 2.
[2m2025-07-11 16:36:20.433[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName,b.PORTFOLIO_NO,b.ICG_NO FROM atr_buss_lic_g b left JOIN ATR_BUSS_LIC_ACTION a on a.action_no= b.action_no left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.action_no = ? and a.entity_id = ? and a.year_month = ? group by a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name,b.PORTFOLIO_NO,b.ICG_NO ORDER BY b.ICG_NO limit 10 
[2m2025-07-11 16:36:20.433[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), 1(Long), 202501(String)
[2m2025-07-11 16:36:20.479[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-11 16:36:20.479[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,3533529290872cc4,bf74ae2fe696360a,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 92
[2m2025-07-11 16:36:22.084[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:36:22.084[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao.findByid
[2m2025-07-11 16:36:22.084[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 4
[2m2025-07-11 16:36:22.084[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:36:22.084[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:36:22.107[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LIC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-11 16:36:22.107[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 4(Long)
[2m2025-07-11 16:36:22.137[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:36:22.137[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e6557d7077f5e963,356c486b8d329589,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 53
[2m2025-07-11 16:36:22.712[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:36:22.712[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findId
[2m2025-07-11 16:36:22.712[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","entityId":1,"icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11","yearMonth":"202501"}
[2m2025-07-11 16:36:22.712[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:36:22.712[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:36:22.734[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==>  Preparing: select ID FROM atr_buss_lic_g WHERE entity_id = ? and ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? and YEAR_MONTH = ? limit 1 
[2m2025-07-11 16:36:22.734[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m ==> Parameters: 1(Long), 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:36:22.769[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.lic.AtrBussLicGDao.findId [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:36:22.770[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,ef72a74c586ec74d,de92c52bf82df163,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 58
[2m2025-07-11 16:36:22.808[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:36:22.808[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:36:22.809[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"1","param1":{"$ref":"$.atrAction"},"param2":"1"}
[2m2025-07-11 16:36:22.810[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:36:22.810[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:36:22.810[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:36:22.810[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 1(String)
[2m2025-07-11 16:36:22.851[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 64
[2m2025-07-11 16:36:22.851[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 42
[2m2025-07-11 16:38:29.207[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[ATR housekeeper][0;39m [36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m IFRSHikariCPForATR - Thread starvation or clock leap detected (housekeeper delta=2m18s492ms27µs).
[2m2025-07-11 16:38:29.209[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 16:38:29.227[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:38:29.227[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.showExpectedClaimTotal
[2m2025-07-11 16:38:29.227[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:38:29.228[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:38:29.228[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:38:29.232[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:38:29.232[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao.findClaimPaid
[2m2025-07-11 16:38:29.233[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:38:29.234[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:38:29.234[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:38:34.911[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:38:34.911[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:38:34.911[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"2","param1":{"$ref":"$.atrAction"},"param2":"2"}
[2m2025-07-11 16:38:34.914[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:38:34.914[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:38:35.176[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==>  Preparing: select m.cf_type AS cfType, a.business_source_code AS businessSourceCode, a.year_month AS yearMonth, t.icg_no AS icgNo, t.acc_year_month AS accYearMonth, sum(case when m.amount_type = 'L1' then m.amount end) AS amountT1, sum(case when m.amount_type = 'L2' then m.amount end) as amountT2, sum(case when m.amount_type = 'L3' then m.amount end) as amountT3, sum(case when m.amount_type = 'L4' then m.amount end) as amountT4, sum(case when m.amount_type = 'C1' then m.amount end) as amountT5, sum(case when m.amount_type = 'C2' then m.amount end) as amountT6 FROM atr_buss_lic_action a left JOIN atr_buss_lic_g t on a.action_no = t.action_no left JOIN atr_buss_lic_g_amount m on t.id = m.main_id WHERE a.ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? group by m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month ORDER BY m.cf_type, a.business_source_code, a.year_month, t.icg_no, t.acc_year_month 
[2m2025-07-11 16:38:35.176[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:38:35.306[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==>  Preparing: select icg_no,acc_year_month as ACCIDENT_YEAR_MONTH FROM atr_buss_lic_g WHERE ACTION_NO = ? and PORTFOLIO_NO = ? and ICG_NO = ? GROUP BY icg_no,acc_year_month ORDER BY icg_no,acc_year_month 
[2m2025-07-11 16:38:35.307[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String)
[2m2025-07-11 16:38:35.392[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.i.a.d.b.p.l.A.showExpectedClaimTotal[0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-11 16:38:35.392[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9cda76bfae237789,8a77b5ec63e0aeb4,false][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-7][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 6165
[2m2025-07-11 16:38:35.392[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:38:35.392[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 2(String)
[2m2025-07-11 16:38:35.414[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findClaimPaid         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-11 16:38:35.414[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 6180
[2m2025-07-11 16:38:35.414[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:38:35.414[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDao.findDevByVo
[2m2025-07-11 16:38:35.414[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"accidentYearMonth":"202501","actionNo":"20250711_133305_VLN6D","businessSourceCode":"TO","calcType":"1","icgNo":"TOBLDX11FS20244","portfolioNo":"TOBLDX11"}
[2m2025-07-11 16:38:35.415[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:38:35.415[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:38:35.415[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==>  Preparing: select a.ACC_YEAR_MONTH, dev_no, ROUND(sum(case when d.cf_type = 'OS' then d.amount end),2) OS, ROUND(sum(case when d.cf_type = 'IBNR' then d.amount end),2) IBNR, ROUND(sum(case when d.cf_type = 'ULAE' then d.amount end),2) ULAE, ROUND(sum( case when d.cf_type = 'RD' then d.amount end),2) RD, ROUND(sum(case when d.cf_type = 'RA' then d.amount end),2) RA FROM atr_buss_lic_g a, atr_buss_lic_g_dev_amount d WHERE a.id = d.main_id and a.action_no = ? and a.PORTFOLIO_NO = ? and a.ICG_NO = ? and a.ACC_YEAR_MONTH = ? and d.amount_type = 'CUR_CF' group by a.ACC_YEAR_MONTH, dev_no ORDER BY dev_no 
[2m2025-07-11 16:38:35.415[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11(String), TOBLDX11FS20244(String), 202501(String)
[2m2025-07-11 16:38:35.416[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:38:35.416[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 505
[2m2025-07-11 16:38:35.417[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:38:35.417[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:38:35.417[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"3","param1":{"$ref":"$.atrAction"},"param2":"3"}
[2m2025-07-11 16:38:35.417[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:38:35.417[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:38:35.418[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:38:35.418[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 3(String)
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 29
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao.findBussQuotaVoByAtrActionVo
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrAction":{"actionNo":"20250711_133305_VLN6D","dimensionValue":"TOBLDX11FS20244"},"quotaGroup":"4","param1":{"$ref":"$.atrAction"},"param2":"4"}
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==>  Preparing: select acqd.quota_def_id as quotaDefId, acqd.quota_code as quotaCode, acqd.quota_c_name as quotaCName, acqd.quota_e_name as quotaEName, acqd.QUOTA_L_NAME as quotaLName, acqd.quota_type as quotaType, acqd.quota_value_type as quotaValueType, acqd.code_type as codeType, abqv.quota_value || '' as value, abqv.risk_class_code as riskClassCode FROM ATR_BUSS_QUOTA_VALUE abqv LEFT JOIN ATR_CONF_QUOTA_DEF acqd ON abqv.QUOTA_CODE = acqd.QUOTA_CODE WHERE abqv.ACTION_NO = ? AND abqv.dimension_value = ? AND acqd.quota_group = ? and abqv.DEV_NO <= 1 ORDER BY abqv.risk_class_code ASC 
[2m2025-07-11 16:38:35.446[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m ==> Parameters: 20250711_133305_VLN6D(String), TOBLDX11FS20244(String), 4(String)
[2m2025-07-11 16:38:35.470[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36ms.i.a.d.b.A.findBussQuotaVoByAtrActionVo[0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-11 16:38:35.470[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c8bdd1cca892a829,bf5d5f7ffe3bec29,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-6][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 24
[2m2025-07-11 16:38:35.516[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.findDevByVo           [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-11 16:38:35.516[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e350c39835f6ff79,46198185cd300980,true][0;39m [35m42344[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 101
[2m2025-07-11 16:39:00.024[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[     Thread-173][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
[2m2025-07-11 16:39:00.032[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-IFRS-ACTUARIAL with eureka with status DOWN
[2m2025-07-11 16:39:00.032[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Saw local status change event StatusChangeEvent [timestamp=1752223140032, current=DOWN, previous=UP]
[2m2025-07-11 16:39:00.033[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608: registering service...
[2m2025-07-11 16:39:00.066[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - registration status: 204
[2m2025-07-11 16:39:00.078[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m42344[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'dataSliceThreadPool'
[2m2025-07-11 17:20:02.486[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 17:25:02.491[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 17:30:02.499[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 17:35:02.511[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-11 17:36:13.590[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[     Thread-149][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
[2m2025-07-11 17:36:13.712[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-IFRS-ACTUARIAL with eureka with status DOWN
[2m2025-07-11 17:36:13.712[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Saw local status change event StatusChangeEvent [timestamp=1752226573712, current=DOWN, previous=UP]
[2m2025-07-11 17:36:13.712[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608: registering service...
[2m2025-07-11 17:36:13.740[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - registration status: 204
[2m2025-07-11 17:36:15.418[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'dataSliceThreadPool'
[2m2025-07-11 17:36:15.835[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'ruleThreadPool'
[2m2025-07-11 17:36:15.853[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Shutdown hook removed for: NFLoadBalancer-PingTimer-SS-PLATFORM-COMMON
[2m2025-07-11 17:36:15.879[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Exception caught (might be ok if at shutdown)

java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:239)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-11 17:36:15.893[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown initiated...
[2m2025-07-11 17:36:15.899[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown completed.
[2m2025-07-11 17:36:15.901[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-11 17:36:18.916[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Unregistering ...
[2m2025-07-11 17:36:21.007[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted

[2m2025-07-11 17:36:21.008[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-11 17:36:23.058[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted

[2m2025-07-11 17:36:23.059[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-11 17:36:23.074[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-11 17:36:23.119[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m38416[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
