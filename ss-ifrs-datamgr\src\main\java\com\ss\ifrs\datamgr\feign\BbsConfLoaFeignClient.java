package com.ss.ifrs.datamgr.feign;

import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.platform.pojo.bbs.vo.BbsConfProductVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BbsConfProductFeignClient
 * @description 调用BPL系统业务线配置API
 * @date 2022/8/2 15:44
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsConfLoaFeignClient {

    /**
     * @description 验证Loa是否存在
     * @param
     * @return com.ss.platform.pojo.base.po.BaseResponse<java.util.Map<java.lang.String,java.lang.String>>
     * @throw
     * <AUTHOR>
     * @date   2022/8/2 15:52
     */
    @RequestMapping(value = "/conf_loa/validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, String>> validateLoaCode(@RequestBody BbsConfLoaVo vo);
}
