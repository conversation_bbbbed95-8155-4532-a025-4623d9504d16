package com.ss.ifrs.datamgr.domain.service.impl.joint;

import com.ss.ifrs.datamgr.annotation.TrackDataMgrProcess;
import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.conf.DmConfDrawTypeDao;
import com.ss.ifrs.datamgr.domain.abst.DmDomainDataDraw;
import com.ss.ifrs.datamgr.domain.service.joint.DmDataDrawEtlService;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfControl;
import com.ss.ifrs.datamgr.pojo.conf.po.DmConfDrawType;
import com.ss.ifrs.datamgr.pojo.domain.vo.dap.DmDataDrawVo;
import com.ss.ifrs.datamgr.service.conf.DmConfControlService;
import com.ss.library.utils.SpringApplicationContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
public class DmDataDrawEtlServiceImpl extends DmDomainDataDraw implements DmDataDrawEtlService {

    @Autowired
    DmConfControlService dmConfControlService;
    @Autowired
    DmConfDrawTypeDao dmConfDrawTypeDao;

    @Autowired
    private ApplicationContext applicationContext;
    /**
     * ETL方式数据校验校验处理
     * 1. 业务分为常规业务及年结业务
     * - 常规业务的月份在12个月中
     * - 年结业务的任务号中的月份字段为JS，且信号表中的year_month字段为当前年的最后一个月
     *
     * @param dmDataDrawVo
     */
    @TrackDataMgrProcess(DmConstant.ProcCode.DM_VERIFY_DATA)
    @Override
    public String dataDrawAction(DmDataDrawVo dmDataDrawVo) {
        // 总控开关
        DmConfControl dmConfControl = dmConfControlService.searchConfigControl();
        // ETL开关
        DmConfDrawType dmConfDrawType = dmConfDrawTypeDao.findDataByType(DmConstant.DrawType.ETL);
        if (DmConstant.ControlType.Open.equals(dmConfControl.getOpenedIs())
                && DmConstant.ControlType.Open.equals(dmConfDrawType.getOpenedIs())) {
            dmDataDrawVo.setDrawType(DmConstant.DrawType.ETL);
            dataVerifyBlend(dmDataDrawVo);
            return "";
        }
        else{
            return "Control not turned on to ETL button !";
        }
    }

    /**
     * 处理常规业务以及年结业务
     */
    private void dataVerifyBlend(DmDataDrawVo dmDataDrawVo) {
        DmDataDrawEtlServiceImpl dmDataDrawEtlService = applicationContext.getBean(DmDataDrawEtlServiceImpl.class);

        // 对常规业务进行校验
        dmDataDrawEtlService.queueDataVerify(dmDataDrawVo);
        // 处理JS业务
        String year = dmDataDrawVo.getYearMonth().substring(0, 4);
        String yearMonth = Integer.parseInt(year) - 1 + "12";
        dmDataDrawVo.setYearMonth(yearMonth);
        // 对年结业务进行校验
        dmDataDrawEtlService.queueDataVerify(dmDataDrawVo);

    }


}
