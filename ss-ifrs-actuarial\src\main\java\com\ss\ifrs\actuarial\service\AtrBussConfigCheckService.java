package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import org.springframework.transaction.UnexpectedRollbackException;

import java.util.Map;

public interface AtrBussConfigCheckService {

    /**
     * @Description: 配置检查功能:
     * @Author: yinxh.
     * @CreateDate: 2020/12/25 16:42
     */
    Map<String, Boolean> config<PERSON>heck(AtrConfBussPeriodVo atrConfBussPeriodVo, Long userId) throws UnexpectedRollbackException;

}
