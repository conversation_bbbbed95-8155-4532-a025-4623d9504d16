package com.ss.ifrs.datamgr.dao.dap.rein;

import com.ss.ifrs.datamgr.pojo.dap.po.rein.DmReinsTreatyPaymentPlan;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.List;

/**
 * Create Date: 2022-09-21 11:30:58<br/>
 * Description: 合约缴费计划配置表 Dao类<br/>
 * Related Table Name: DM_REINS_TREATY_PAYMENT_PLAN<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface DmReinsTreatyPaymentPlanDao extends IDao<DmReinsTreatyPaymentPlan, Serializable> {
}