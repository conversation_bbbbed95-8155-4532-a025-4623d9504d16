package com.ss.ifrs.datamgr.domain.service.icg.profitable;

import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;

public interface DmDomainProfitableBbaService {

    /*
     *计量单元发展期月度已赚保费
     */
    void calCmUnitDevelopAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *跟單IACF率
     */
    void calIacfRate(DmIcgProcVo dmIcgProcVo);

    /*
     *预期未来现金流现值, 维持费用, 跟单IACF费用,非跟单IACF费用
     */
    void calCmUnitExpectFutureAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *预期赔付现金流现值
     */
    void calCmUnitExpectLossAmount(DmIcgProcVo dmIcgProcVo);

    /*
     *基本情景成本率，压力情景成本率
     */
    void calCmUnitCostRate(DmIcgProcVo dmIcgProcVo);

    /*
     *盈亏结果
     */
    void Profitable(DmIcgProcVo dmIcgProcVo);
}
