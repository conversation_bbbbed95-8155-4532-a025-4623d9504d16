package com.ss.ifrs.datamgr.dao.push;

import com.ss.ifrs.datamgr.pojo.push.DataPushVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DmBussDataPushExpDao {

    String getExpSyncPlanType(Long entityId);
    void deleteExpDapFinancialDataDetailByCurrentYearMonth(@Param("vo") DataPushVo vo);
    void deleteExpDapFinancialDataByCurrentYearMonth(@Param("vo") DataPushVo vo);
    //情况一、科目只存在专项数据取所有数据(按部门分组写主表和明细)，有部门专项和无部门会各自生成一条
    void insertExpDapFinancialDataScenarioOne(@Param("vo") DataPushVo vo);
    void insertExpDapFinancialDataDetailScenarioOne(@Param("vo") DataPushVo vo);
    //情况二、科目余额不存在专项数据，生成一条数据
    void insertExpDapFinancialDataScenarioTwo(@Param("vo") DataPushVo vo);
    void insertExpDapFinancialDataDetailScenarioTwo(@Param("vo") DataPushVo vo);
    //情况三、科目余额存在专项数据，且专项数据不存在【无部门】数据时，会新增一条新的主表数据
    void insertExpDapFinancialDataScenarioThree(@Param("vo") DataPushVo vo);
    void insertExpDapFinancialDataDetailScenarioThree(@Param("vo") DataPushVo vo);
    //情况四、科目余额存在专项数据，且专项数不存在【无部门】数据时
    void insertExpDapFinancialDataDetailScenarioFour(@Param("vo") DataPushVo vo);
    void updateExpDapFinancialDataScenarioFour(@Param("vo") DataPushVo vo);

    //清空当前业务年月exp计量单元数据
    void deleteExpDapCmUnitDataByCurrentYearMonth(@Param("vo") DataPushVo vo);
    //直接业务计量单元保费数据
    void insertExpDDCmUnitData(@Param("vo") DataPushVo vo);
    //写入合约分入计量单元保费数据
    void insertExpTICmUnitData(@Param("vo") DataPushVo vo);
    //写入直保计量单元赔款数据
    void insertExpDDCmUnitClaimData(@Param("vo") DataPushVo vo);
    //写入合约分入计量单元赔款数据
    void insertExpTICmUnitClaimData(@Param("vo") DataPushVo vo);



}
