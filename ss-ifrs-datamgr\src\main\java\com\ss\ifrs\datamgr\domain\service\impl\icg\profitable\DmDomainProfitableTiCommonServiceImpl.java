package com.ss.ifrs.datamgr.domain.service.impl.icg.profitable;

import com.ss.ifrs.datamgr.constant.DmConstant;
import com.ss.ifrs.datamgr.dao.domain.icg.profitable.DmDomainProfitableTiCommonDao;
import com.ss.ifrs.datamgr.domain.service.icg.profitable.DmDomainProfitableCommonService;
import com.ss.ifrs.datamgr.feign.BbsConfAccountSetClient;
import com.ss.ifrs.datamgr.pojo.domain.vo.buss.DmIcgProcVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service(DmConstant.BusinessModel.MODEL_TREATY_IN + "PROFITABLE_COMMON")
public class DmDomainProfitableTiCommonServiceImpl implements DmDomainProfitableCommonService {

    @Autowired
    private DmDomainProfitableTiCommonDao dmDomainProfitableTiCommonDao;

    @Autowired
    private BbsConfAccountSetClient bbsConfAccountSetClient;

    @Override
    public void prepareQuota(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiCommonDao.deleteDuctProfitableQuota(dmIcgProcVo);
        dmDomainProfitableTiCommonDao.prepareQuota(dmIcgProcVo);

    }

    @Override
    public void prepareDevelopQuota(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiCommonDao.deleteDuctProfitableDevelopQuota(dmIcgProcVo);
        dmDomainProfitableTiCommonDao.prepareDevelopQuota(dmIcgProcVo);
    }

    @Override
    public void prepareStressLossRate(DmIcgProcVo dmIcgProcVo) {
        dmDomainProfitableTiCommonDao.prepareStressLossRate(dmIcgProcVo);
    }

    @Override
    public void prepareInterestRate(DmIcgProcVo dmIcgProcVo) {
        //使用会计期间的BookI17账套 dm_pack_buss_accountperiod.func_get_accountperiod_currency(entity_id)
        //TODO dmIcgProcVo.setCurrencyCode(dmDomainProfitableTiCommonDao.getAccountPeriodCurrency(dmIcgProcVo));
//        BbsConfAccountSetVo byCenterId = bbsConfAccountSetClient.findByCenterId(dmIcgProcVo.getEntityId()).getResData();
        dmIcgProcVo.setCurrencyCode("IDR");
        dmDomainProfitableTiCommonDao.prepareInterestRate(dmIcgProcVo);
    }

    @Override
    public void checkFactor(DmIcgProcVo dmIcgProcVo) {
        dmIcgProcVo.setCurrencyCode("IDR");
        //假设配置检查
        dmDomainProfitableTiCommonDao.checkBbaAssumingConfiguration(dmIcgProcVo);
        dmDomainProfitableTiCommonDao.checkPaaAssumingConfiguration(dmIcgProcVo);
        //压力情景配置查询，BBA时提示异常
        dmDomainProfitableTiCommonDao.checkStressScenarioConfiguration(dmIcgProcVo);
        //无风险曲线,确认结果是确认已采用的
        dmDomainProfitableTiCommonDao.checkBbaInterestRateConfiguration(dmIcgProcVo);
        dmDomainProfitableTiCommonDao.checkPaaInterestRateConfiguration(dmIcgProcVo);
    }
}
