package com.ss.ifrs.datamgr.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfProductVo;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BbsConfProductFeignClient
 * @description 调用BPL系统产品配置API
 * @date 2022/8/2 15:44
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsConfProductFeignClient {
	
	/**
	 * @description 验证Product是否存在
	 * @param
	 * @return com.ss.platform.pojo.base.po.BaseResponse<java.util.Map<java.lang.String,java.lang.String>>
	 * @throw
	 * <AUTHOR>
	 * @date   2022/8/2 15:52
	 */
	@RequestMapping(value = "/conf_product/validate_code", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Map<String, String>> validateProductCode(@RequestBody BbsConfProductVo bbsConfProductVo);

	@ApiOperation(value = "根据产品代码查询产品")
	@RequestMapping(value = "/conf_product/find_by_product_code/{productCode}", method = RequestMethod.GET)
	BbsConfProductVo findByProductCode(@RequestParam(value = "productCode") String productCode);
}
