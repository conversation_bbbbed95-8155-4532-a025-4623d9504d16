package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrBussRPGKey;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo.*;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 预期保费现金流 service （临分分出业务）
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussLrcFoService extends AbstractAtrBussLrcService {

    private final Map<AtrBussRPGKey, AtrBussLrcFoIcg> icgMap = new HashMap<>();

    /*  part index start */
    private final IndexFactory<BigDecimal> prePaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidNetFeeIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidNetFeeIndex = new IndexFactory<>();
    private final IndexFactory<AtrDuctLrcFoIcuPre> preIcuIndex = new IndexFactory<>();


    // 统计新旧保费，index 0 为旧保费,index 1 为新保费
    private final Map<List<String>, BigDecimal[]> icgPremiumIndex = new HashMap<>();
    /*  part index end */

    public void entry(String actionNo, Long entityId, String yearMonth) {
        initEnvParams(actionNo, entityId, yearMonth, "FO");
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            initAbp(abp);
            logDebug("collectData");
            collectData();
            logDebug("calcIcu");
            calcIcu();
            logDebug("calcIcg");
            calcIcg();
            logDebug("saveIcgPremium");
            saveIcgPremium();
            logDebug("abp-end");
            abp.end();
            logDebug("end");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException(e);
        }
    }

    private void saveIcgPremium(){
        icgPremiumIndex.forEach((key, value) -> {
            AtrBussFoLrcIcgPremium atrBussFoLrcIcgPremium = new AtrBussFoLrcIcgPremium();
            atrBussFoLrcIcgPremium.setActionNo(actionNo);
            atrBussFoLrcIcgPremium.setYearMonth(yearMonth);
            atrBussFoLrcIcgPremium.setPortfolioNo(key.get(0));
            atrBussFoLrcIcgPremium.setIcgNo(key.get(1));
            atrBussFoLrcIcgPremium.setRiskClassCode(key.get(2));
            atrBussFoLrcIcgPremium.setOldPremium(value[0]);
            atrBussFoLrcIcgPremium.setNewPremium(value[1]);
            abp.insert(atrBussFoLrcIcgPremium);
        });
    }

    private void calcIcg() {
        ArrayList<AtrBussLrcFoIcg> icgVos = new ArrayList<>(icgMap.values());
        ThreadUtil.runThreadsThrow(icgVos, this::calcIcg, () -> EcfUtil.THREADS_CALC);
    }

    private void calcIcg(AtrBussLrcFoIcg icg) {
        long mainId = icgMainIdGen.incrementAndGet();

        int remainingMonths = icg.getRemainingMonths();
        Map<Integer, BigDecimal> devEdPremiumMap = icg.getDevEdPremiumMap();
        Map<Integer, BigDecimal> devNetFeeMap = icg.getDevNetFeeMap();
        Map<Integer, BigDecimal> devEdNetFeeMap = icg.getDevEdNetFeeMap();
        
        // 初始化发展期并设置所有属性（合并三个循环提高效率）
        List<AtrBussLrcFoIcgDev> devVos = new ArrayList<>();
        for (int i = 0; i <= remainingMonths; i++) {
            AtrBussLrcFoIcgDev devVo = new AtrBussLrcFoIcgDev();
            devVos.add(devVo);

            // 第一个循环的逻辑：设置基本属性
            devVo.setMainId(mainId);
            devVo.setDevNo(i);
            devVo.setYearMonth(yearMonth);
            devVo.setEdPremium(devEdPremiumMap.get(i));

            // 第二个循环的逻辑：计算已赚比例
            BigDecimal edPremium = devVo.getEdPremium();
            if (edPremium == null || edPremium.compareTo(BigDecimal.ZERO) == 0) {
                devVo.setEdRate(BigDecimal.ZERO);
            } else {
                // 计算从当前位置到remainingMonths的累计edPremium
                BigDecimal sumEdPremium = BigDecimal.ZERO;
                for (int k = i; k < remainingMonths; k++) {
                    sumEdPremium = sumEdPremium.add(nvl(devEdPremiumMap.get(k)));
                }
                if (sumEdPremium.compareTo(BigDecimal.ZERO) == 0) {
                    devVo.setEdRate(BigDecimal.ZERO);
                } else {
                    devVo.setEdRate(roundR(edPremium.divide(sumEdPremium, 15, RoundingMode.HALF_UP)));
                }
            }
            devVo.setEdNetFee(devEdNetFeeMap.get(i));

            // 第三个循环的逻辑：设置接收保费和净费用
            devVo.setRecvPremium(icg.getDevRecvPremiumMap().get(i));
            devVo.setNetFee(devNetFeeMap.get(i));
        }

        icg.setId(mainId);
        abp.insert(icg);
        devVos.forEach(abp::insert);
    }


    private void calcIcu() {
        for (int partNo = 0; partNo < parts; partNo++) {
            Map<String, Object> paramMap = new HashMap<>(commonParamMap);
            paramMap.put("pn", partNo);

            List<AtrBussLrcFoIcu> baseVos = new ArrayList<>();

            List<String> marks = new ArrayList<>();
            marks.add("collectPreEdPremium");
            marks.add("collectDapPaid");
            marks.add("collectPreIcu");
            marks.add("getPartBaseVos");
            ThreadUtil.runThreadsThrow(marks, (mark) -> {
                logDebug(mark, "start");
                if ("collectPreEdPremium".equals(mark)) {
                    collectPreEdPremium(paramMap);
                } else if ("collectDapPaid".equals(mark)) {
                    collectDapPaid(paramMap);
                } else if ("collectPreIcu".equals(mark)) {
                    collectPreIcu(paramMap);
                } else if ("getPartBaseVos".equals(mark)) {
                    baseVos.addAll(atrBussLrcFoDao.getPartBaseVos(paramMap));
                }
                logDebug(mark, "end");
            }, marks::size);

            logDebug("calcIcu-" + partNo);
            for (AtrBussLrcFoIcu baseVo : baseVos) {
                calcIcu(baseVo, icgMap);
            }
            logDebug("calcIcu-" + partNo + "-end");
        }
    }

    private void calcIcu(AtrBussLrcFoIcu icu, Map<AtrBussRPGKey, AtrBussLrcFoIcg> icgMap) {
        long mainId = icuMainIdGen.incrementAndGet();
        BigDecimal premium = icu.getPremium();
        BigDecimal netFee = nvl(icu.getNetFee());
        Date effectiveDate = icu.getEffectiveDate();
        Date expiryDate = icu.getExpiryDate();

        List<?> unitKey = createUnitKey(icu);

        // 剩余月份
        int remainingMonths = 0;
        if (Dates.toChar(expiryDate, "yyyyMM").compareTo(yearMonth) > 0) {
            remainingMonths = Dates.monthsBetween(yearMonth, expiryDate) + 1;
        }

        // 最大发展期
        int maxDevNo = Math.max(1, remainingMonths);


        // 往期累计实收保费
        BigDecimal preAccumPaidPremium = prePaidPremiumIndex.one(unitKey, BigDecimal.ZERO);
        // 获取上期ICU数据
        AtrDuctLrcFoIcuPre preIcu = preIcuIndex.one(unitKey, new AtrDuctLrcFoIcuPre());
        // 上期累计已赚
        BigDecimal preAccumEdPremium = nvl(preIcu.getPreCumlEdPremium()).add(nvl(preIcu.getCurEdPremium()));
        // 往期累计净额手续费
        BigDecimal preAccumNetFee = prePaidNetFeeIndex.one(unitKey, BigDecimal.ZERO);
        // 上期累计已赚净额手续费
        BigDecimal preAccumEdNetFee = nvl(preIcu.getPreCumlEdNetFee()).add(nvl(preIcu.getCurEdNetFee()));
        // 当期实付
        BigDecimal curPaidPremium = curPaidPremiumIndex.one(unitKey, BigDecimal.ZERO);
        // 当期实收 net_fee
        BigDecimal curPaidNetFee = curPaidNetFeeIndex.one(unitKey, BigDecimal.ZERO);
        // 是否已过期
        boolean expiryIs = !expiryDate.after(evDate);
        // 承保总天数
        int totalDays = Dates.daysBetween(effectiveDate, expiryDate) + 1;

        calcIcgPremium(icu.getPortfolioNo(), icu.getIcgNo(),icu.getRiskClassCode(), icu.getContractDate(), premium);

        // 初始化发展期
        List<AtrBussLrcFoIcuDev> devVos = new ArrayList<>();
        for (int i = 0; i < maxDevNo; i++) {
            AtrBussLrcFoIcuDev dev = new AtrBussLrcFoIcuDev();
            devVos.add(dev);
            dev.setMainId(mainId);
            dev.setDevNo(i);
            dev.setYearMonth(yearMonth);

            Date devDate = devDate(i);
            Date devDateBom = Dates.truncMonth(devDate);

            // 已赚保费及相关
            if (i <= remainingMonths) {
                BigDecimal days = BigDecimal.ZERO;
                if (totalDays > 0) {
                    if (expiryIs) {
                        if (icu.getDapYearMonth().equals(yearMonth) && i == 0) {
                            days = BigDecimal.valueOf(totalDays);
                        }else {
                            days =  BigDecimal.valueOf(Math.max(Dates.daysBetween(devDateBom,expiryDate),0) + 1);
                        }
                    } else {
                        if (effectiveDate.after(devDate)) {
                            days = BigDecimal.ZERO;
                        } else if (!effectiveDate.before(devDateBom)) {
                            if (!expiryDate.after(devDate)) {
                                days = BigDecimal.valueOf(totalDays);
                            } else {
                                days = BigDecimal.valueOf(Dates.daysBetween(effectiveDate, devDate) + 1);
                            }
                        } else {
                            if (!expiryDate.after(devDate)) {
                                days = BigDecimal.valueOf(Dates.daysBetween(devDateBom, expiryDate) + 1);
                            } else {
                                days = BigDecimal.valueOf(new DateTime(devDate).dayOfMonth().getMaximumValue());
                            }
                        }
                    }
                }

                dev.setEdRate(roundR(days.divide(BigDecimal.valueOf(totalDays), 15, RoundingMode.HALF_UP)));
                dev.setEdPremium(round(premium.multiply(dev.getEdRate())));
                dev.setEdNetFee(round(netFee.multiply(dev.getEdRate())));
            }

            if (i == 0) {
                // 应收保费
                dev.setRecvPremium(curPaidPremium);
                // net fee
                dev.setNetFee(curPaidNetFee);
            }
        }

        // 处理尾差
        handleAmountTailDifference(devVos, premium, netFee, preAccumEdPremium, preAccumEdNetFee);
        
        // 校验保费和净额结算的一致性
        validateIcuConsistency(icu.getPolicyNo(), icu.getEndorseSeqNo(), icu.getKindCode(),
                premium, preAccumPaidPremium, preAccumNetFee, devVos,preAccumEdNetFee);

        icu.setId(mainId);
        icu.setActionNo(actionNo);
        icu.setEntityId(entityId);
        icu.setYearMonth(yearMonth);
        icu.setRemainingMonths(remainingMonths);
        icu.setPreCumlPaidPremium(preAccumPaidPremium);
        icu.setPreCumlPaidNetFee(preAccumNetFee);
        icu.setPreCumlEdPremium(preAccumEdPremium);
        icu.setPreCumlEdNetFee(preAccumEdNetFee);
        icu.setCurEdPremium(devVos.get(0).getEdPremium());
        icu.setCurEdNetFee(devVos.get(0).getEdNetFee());

        icu.setInvAmount(BigDecimal.ZERO);

        abp.insert(icu);

        devVos.forEach(abp::insert);

        collectIcg(icgMap, icu, devVos);
    }

    /**
     * 处理各种金额的尾差
     * 
     * @param devVos 发展期列表
     * @param premium 保费
     * @param netFee 净额结算手续费
     * @param preAccumEdPremium 上期累计已赚保费
     * @param preAccumEdNetFee 上期累计已赚净额结算手续费
     */
    private void handleAmountTailDifference(List<AtrBussLrcFoIcuDev> devVos, 
                                          BigDecimal premium, BigDecimal netFee,
                                          BigDecimal preAccumEdPremium, BigDecimal preAccumEdNetFee) {
        // 已赚保费尾差处理
        AtrBussLrcFoIcuDev targetDevVo = null;
        BigDecimal maxEdPremium = BigDecimal.ZERO;
        
        for (AtrBussLrcFoIcuDev dev : devVos) {
            if (dev.getEdPremium() != null && dev.getEdPremium().compareTo(BigDecimal.ZERO) > 0) {
                if (targetDevVo == null || dev.getEdPremium().compareTo(maxEdPremium) > 0) {
                    targetDevVo = dev;
                    maxEdPremium = dev.getEdPremium();
                }
            }
        }
        BigDecimal edPremiumSum = devVos.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetDevVo != null && premium != null) {
            BigDecimal gap = premium.subtract(preAccumEdPremium).subtract(edPremiumSum);
            targetDevVo.setEdPremium(round(targetDevVo.getEdPremium().add(gap)));
        }
        
        // 净额结算手续费尾差处理
        AtrBussLrcFoIcuDev targetNetFeeDevVo = null;
        BigDecimal maxEdNetFee = BigDecimal.ZERO;
        for (AtrBussLrcFoIcuDev dev : devVos) {
            if (dev.getEdNetFee() != null && dev.getEdNetFee().compareTo(BigDecimal.ZERO) > 0) {
                if (targetNetFeeDevVo == null || dev.getEdNetFee().compareTo(maxEdNetFee) > 0) {
                    targetNetFeeDevVo = dev;
                    maxEdNetFee = dev.getEdNetFee();
                }
            }
        }
        BigDecimal edNetFeeSum = devVos.stream()
                .map(dev -> nvl(dev.getEdNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetNetFeeDevVo != null && netFee != null) {
            BigDecimal netFeeGap = netFee.subtract(preAccumEdNetFee).subtract(edNetFeeSum);
            targetNetFeeDevVo.setEdNetFee(round(targetNetFeeDevVo.getEdNetFee().add(netFeeGap)));
        }
    }
    
    /**
     * 分出保费和净额结算一致性校验
     *
     * @param policyNo 保单号
     * @param endorseSeqNo 批单序号
     * @param kindCode 险别编码
     * @param premium 保费
     * @param preAccumPaidPremium 上期累计实收保费
     * @param preAccumNetFee 上期累计实收净额结算
     * @param devVos 发展期列表
     */
    private void validateIcuConsistency(String policyNo, String endorseSeqNo, String kindCode, 
                                        BigDecimal premium, BigDecimal preAccumPaidPremium,
                                        BigDecimal preAccumNetFee, List<AtrBussLrcFoIcuDev> devVos,BigDecimal preAccumEdNetFee) {
        // 校验1：分出保费一致性校验
        // 累计支付的分出保费 + 未来分出保费现金流 = 累计增量已赚分出保费
        BigDecimal recvPremium = devVos.stream()
                .map(dev -> nvl(dev.getRecvPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal edPremiumSum = devVos.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        if ((preAccumPaidPremium.add(recvPremium)).compareTo(premium) != 0) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的分出保费为:%s与累计支付和未来分出保费现金流汇总为:%s不一致"
                    , policyNo, endorseSeqNo, kindCode, premium.toString(), preAccumPaidPremium.add(recvPremium).toString()));
        }
        
        // 校验2：分出净额结算一致性校验
        // 累计实收净额结算捆绑分保费用 + 未来净额结算捆绑分保费用现金流 = 累计增量已赚分出净额结算
        BigDecimal netFeeSum = devVos.stream()
                .map(dev -> nvl(dev.getNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal edNetFeeSum = devVos.stream()
                .map(dev -> nvl(dev.getEdNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        if (preAccumNetFee.add(netFeeSum).compareTo(edNetFeeSum.add(preAccumEdNetFee)) != 0) {
            throw new RuntimeException(String.format("保单号为%s,批单号为%s,险别编码为%s的分出净额结算不一致，累计实收净额结算+未来净额结算为:%s,累计已赚净额结算为:%s"
                    , policyNo, endorseSeqNo, kindCode, preAccumNetFee.add(netFeeSum).toString(), edNetFeeSum.add(preAccumEdNetFee).toString()));
        }
    }

    // 新旧保费汇总，合同确认日期为当月则为新保费，旧保费采用上一个月的
    private void calcIcgPremium(String portfolioNo, String icgNo, String riskClassCode, Date contractDate, BigDecimal premium) {
        List<String> key = Arrays.asList(portfolioNo, icgNo, riskClassCode);
        if (Dates.toChar(contractDate, "yyyyMM").equals(yearMonth)) {
            if (icgPremiumIndex.containsKey(key)) {
                BigDecimal[] values = icgPremiumIndex.get(key);
                values[1] = values[1].add(premium);
                icgPremiumIndex.put(key, values);
            } else {
                icgPremiumIndex.put(key, new BigDecimal[]{BigDecimal.ZERO, premium});
            }
        }
    }

    private void collectIcg(Map<AtrBussRPGKey, AtrBussLrcFoIcg> icgMap, AtrBussLrcFoIcu icu,
                            List<AtrBussLrcFoIcuDev> icuDevs) {
        String riskClassCode = icu.getRiskClassCode();
        String portfolioNo = icu.getPortfolioNo();
        String icgNo = icu.getIcgNo();
        AtrBussRPGKey key = new AtrBussRPGKey();
        key.setRiskClassCode(riskClassCode);
        key.setPortfolioNo(portfolioNo);
        key.setIcgNo(icgNo);
        AtrBussLrcFoIcg icg = icgMap.get(key);
        if (icg == null) {
            icg = new AtrBussLrcFoIcg();
            BeanUtils.copyProperties(icu, icg);
            icg.setRemainingMonths(icu.getRemainingMonths());
            icg.setTotalPremium(icu.getPremium());
            icg.setTotalNetFee(icu.getNetFee());
            icg.setMaxClmQuotaDevNo(getMaxClmPatternDevNo(riskClassCode, icgNo));
            icgMap.put(key, icg);
        } else {
            if (icg.getRemainingMonths() < icu.getRemainingMonths()) {
                icg.setRemainingMonths(icu.getRemainingMonths());
            }
            icg.setTotalPremium(icg.getTotalPremium().add(icu.getPremium()));
            icg.setTotalNetFee(icg.getTotalNetFee().add(icu.getNetFee()));
        }

        for (AtrBussLrcFoIcuDev icuDev : icuDevs) {
            Integer devNo = icuDev.getDevNo();
            putDevValue(icg.getDevEdPremiumMap(), devNo, nvl(icuDev.getEdPremium()));
            putDevValue(icg.getDevRecvPremiumMap(), devNo, nvl(icuDev.getRecvPremium()));
            putDevValue(icg.getDevNetFeeMap(), devNo, nvl(icuDev.getNetFee()));
            putDevValue(icg.getDevEdNetFeeMap(), devNo, nvl(icuDev.getEdNetFee()));
        }
    }

    private void collectDapPaid(Map<?, ?> paramMap) {
        prePaidNetFeeIndex.clear();
        curPaidNetFeeIndex.clear();
        prePaidPremiumIndex.clear();
        curPaidPremiumIndex.clear();

        List<AtrDapLrcFoPaid> vos = atrBussLrcFoDao.findDapPaid(paramMap);
        for (AtrDapLrcFoPaid vo : vos) {
            String yearMonth = vo.getYearMonth();
            List<?> key = createUnitKey(vo);
            if (yearMonth.compareTo(this.yearMonth) < 0) {
                prePaidNetFeeIndex.plus(key, vo.getNetFee());
                prePaidPremiumIndex.plus(key, vo.getPremium());
            } else if (yearMonth.equals(this.yearMonth)) {
                curPaidPremiumIndex.plus(key, vo.getPremium());
                curPaidNetFeeIndex.plus(key, vo.getNetFee());
            }
        }
    }

    private void collectPreEdPremium(Map<String, Object> paramMap) {
        // 因为之前代码已注释掉，此处保留方法框架，实际业务逻辑已被移除
    }

    private void collectPreIcu(Map<String, Object> paramMap) {
        preIcuIndex.clear();
        List<AtrDuctLrcFoIcuPre> vos = atrBussLrcFoDao.findPreIcu(paramMap);
        for (AtrDuctLrcFoIcuPre vo : vos) {
            preIcuIndex.add(createUnitKey(vo), vo);
        }
    }

    private void collectData() {
        initMainIdGen();
        logDebug("partitionBaseData");
        partitionBaseData();
        logDebug("collectPreIcgPremium");
        collectPreIcgPremium();
    }

    private void partitionBaseData() {
        atrBussLrcFoDao.truncateBaseData();
        atrBussLrcFoDao.partitionBaseData(commonParamMap);
    }

    private void collectPreIcgPremium() {
        List<AtrBussFoLrcIcgPremium> atrBussFoLrcIcgPremiums = atrBussLrcFoDao.listPreIcgPremium(commonParamMap);

        atrBussFoLrcIcgPremiums.forEach(item -> {
            List<String> key = Arrays.asList(item.getPortfolioNo(), item.getIcgNo(), item.getRiskClassCode());
            icgPremiumIndex.put(key, new BigDecimal[]{item.getNewPremium().add(item.getOldPremium()), BigDecimal.ZERO});
        });
    }


    private void initMainIdGen() {
        icuMainIdGen = new AtomicLong(atrBussLrcFoDao.getIcuMaxMainId());
        icgMainIdGen = new AtomicLong(atrBussLrcFoDao.getIcgMaxMainId());
    }

    @Override
    protected void initAbp(AsyncBatchProcessor abp) {
        super.initAbp(abp);
        abp.addType(AtrBussLrcFoIcu.class);
        abp.addType(AtrBussLrcFoIcuDev.class);
        abp.addType(AtrBussLrcFoIcg.class);
        abp.addType(AtrBussLrcFoIcgDev.class);
        abp.addType(AtrBussFoLrcIcgPremium.class);
    }

    private List<?> createUnitKey(Object vo) {
        List<Object> key = new ArrayList<>();
        key.add(EcfUtil.readField(vo, "policyNo"));
        key.add(EcfUtil.readField(vo, "endorseSeqNo"));
        key.add(EcfUtil.readField(vo, "kindCode"));
        return key;
    }

    /**
     * 计算FO过渡期已赚保费
     * 
     * @param yearMonth 评估期（格式：yyyyMM）
     * @param entityId 机构ID
     */
    public void calculateEdPremium(String yearMonth, Long entityId) {
        // 初始化环境参数
        String actionNo = EcfUtil.createActionNo();
        initEnvParams(actionNo, entityId, yearMonth, "FO");
        
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            // 初始化批处理器
            abp.addType(AtrBussFoTransitionEdPremium.class);
            
            // 设置分区数量
            final int parts = 20; // 分区数量
            
            // 获取评估期开始和结束日期
            DateTime evDateBom = new DateTime(Dates.toDate(yearMonth + "01"));
            DateTime evDateEom = evDateBom.dayOfMonth().withMaximumValue();
            Date evStartDate = evDateBom.toDate();
            Date evEndDate = evDateEom.toDate();
            int daysInMonth = evDateEom.getDayOfMonth(); // 当月天数

            // 清空临时表
            atrBussLrcFoDao.truncateTransitionTempTable();
            
            // 构建插入临时表所需参数
            Map<String, Object> tempDataParams = new HashMap<>(commonParamMap);
            tempDataParams.put("parts", parts);
            tempDataParams.put("evStartDate", evStartDate);

            // 向临时表插入需要计算的保单数据
            atrBussLrcFoDao.insertTransitionTempData(tempDataParams);

            // 按分区处理数据
            for (int partNo = 0; partNo < parts; partNo++) {
                Map<String, Object> partParams = new HashMap<>(commonParamMap);
                partParams.put("pn", partNo);
                
                // 获取当前分区的保单数据
                List<AtrBussLrcFoIcu> baseVos = atrBussLrcFoDao.getTransitionPartBaseVos(partParams);
                if (baseVos.isEmpty()) {
                    continue; // 跳过空分区
                }
                
                // 创建线程安全的结果集合
                final List<AtrBussFoTransitionEdPremium> resultList = Collections.synchronizedList(new ArrayList<>());
                
                // 使用并发计算
                ThreadUtil.runThreadsThrow(baseVos, vo -> {
                    // 计算已赚保费
                    AtrBussFoTransitionEdPremium edPremium = calculateSingleEdPremium(
                        vo, entityId, yearMonth, evStartDate, evEndDate, daysInMonth);
                    
                    // 添加到线程安全的结果列表
                    if (edPremium != null) {
                        resultList.add(edPremium);
                    }
                }, () -> EcfUtil.THREADS_CALC); // 使用EcfUtil中定义的计算线程数
                
                // 批量插入当前分区的已赚保费结果
                if (!resultList.isEmpty()) {
                    // 使用AsyncBatchProcessor插入数据
                    resultList.forEach(abp::insert);
                }
                
                // 清理已处理的临时数据
                atrBussLrcFoDao.deleteProcessedTransitionData(partParams);
            }
            
            abp.end();
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("计算FO业务过渡期已赚保费异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 计算单个保单的已赚保费
     * 
     * @param vo 保单数据
     * @param entityId 实体ID
     * @param yearMonth 评估期
     * @param evStartDate 评估期开始日期
     * @param evEndDate 评估期结束日期
     * @param daysInMonth 当月天数
     * @return 已赚保费对象
     */
    private AtrBussFoTransitionEdPremium calculateSingleEdPremium(
            AtrBussLrcFoIcu vo, Long entityId, String yearMonth, 
            Date evStartDate, Date evEndDate, int daysInMonth) {
        
        // 获取基本信息
        String policyNo = vo.getPolicyNo();
        String endorseSeqNo = vo.getEndorseSeqNo();
        String kindCode = vo.getKindCode();
        BigDecimal premium = nvl(vo.getPremium());
        Date effectiveDate = vo.getEffectiveDate();  // 起保日期
        Date expiryDate = vo.getExpiryDate();        // 终保日期
        
        // 承保总天数
        int totalDays = Dates.daysBetween(effectiveDate, expiryDate) + 1;
        if (totalDays <= 0) {
            return null; // 无效的保单日期，跳过
        }
        
        // 计算当期已赚天数
        double earnedDays;
        
        // 情况1: 保险期间完全在评估期内（起保日期和终保日期都在评估期内）
        if (!effectiveDate.before(evStartDate) && !effectiveDate.after(evEndDate) &&
            !expiryDate.before(evStartDate) && !expiryDate.after(evEndDate)) {
            // 计算公式：保险期间总天数 / 保险期间总天数 * premium = premium
            earnedDays = totalDays;
        } 
        // 情况2: 起保日期在当前评估期内，终保日期大于当前评估期
        else if (!effectiveDate.before(evStartDate) && !effectiveDate.after(evEndDate) &&
                 expiryDate.after(evEndDate)) {
            // 计算公式：（当前评估期的最后一天-起保日期）的天数 / (终保日期-起保日期+1) * premium
            earnedDays = Dates.daysBetween(effectiveDate, evEndDate) + 1;
        } 
        // 情况3: 起保日期小于当前评估期，终保日期在当前评估期内
        else if (effectiveDate.before(evStartDate) &&
                !expiryDate.before(evStartDate) && !expiryDate.after(evEndDate)) {
            // 计算公式：（终保日期-当前评估期的第一天+1）/ (终保日期-起保日期+1) * premium
            earnedDays = Dates.daysBetween(evStartDate, expiryDate) + 1;
        } 
        // 情况4: 起保日期小于当前评估期且终保日期大于当前评估期（跨期保单）
        else if (effectiveDate.before(evStartDate) && expiryDate.after(evEndDate)) {
            // 计算公式：（当前评估期的最后一天-当前评估期的第一天+1）/ (终保日期-起保日期+1) * premium
            earnedDays = daysInMonth;
        } 
        // 情况5: 保险期间完全在评估期外
        else {
            // 没有已赚部分
            return null;
        }
        
        // 计算已赚保费比例
        BigDecimal edRate = earnedDays <= 0 ? BigDecimal.ZERO : 
                BigDecimal.valueOf(earnedDays).divide(BigDecimal.valueOf(totalDays), 15, RoundingMode.HALF_UP);
        
        // 计算已赚保费
        BigDecimal edPremium = round(premium.multiply(edRate));
        
        // 创建已赚保费对象
        AtrBussFoTransitionEdPremium result = new AtrBussFoTransitionEdPremium();
        result.setEntityId(entityId);
        result.setPolicyNo(policyNo);
        result.setEndorseSeqNo(endorseSeqNo);
        result.setKindCode(kindCode);
        result.setYearMonth(yearMonth);
        result.setEdPremium(edPremium);
        
        return result;
    }

    /**
     * 备份并清理历史保单数据
     * 
     * <p>只清理atr_dap_fo_paid表的数据。清理条件依赖于AtrBussLrcDdService已创建的临时表atr_temp_policies_to_clean。
     * 确保在调用此方法前已经执行了AtrBussLrcDdService.backupAndCleanHistoricalData方法。
     * 
     * <p>会同时备份以下表：
     * <ul>
     * <li>atr_dap_fo_paid → atr_dap_fo_paid_bak</li>
     * </ul>
     *
     */
    public void backupAndCleanHistoricalData() {
        try {
            // 创建备份表并删除相关数据
            atrBussLrcFoDao.createPaidBackupTable();
            atrBussLrcFoDao.deletePaidData();
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("备份和清理FO历史保单数据异常: " + e.getMessage(), e);
        }
    }

    /**
     * 判断过渡期已赚保费表中是否存在指定机构的数据
     * 
     * @param entityId 机构ID
     * @return 存在返回true，不存在返回false
     */
    public boolean hasTransitionData(Long entityId) {
        return atrBussLrcFoDao.hasTransitionData(entityId);
    }

}