package com.ss.ifrs.datamgr.feign;

import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.DataSyncReqVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 现行账套Feign调用
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsDataSyncClient {

    @ApiOperation(value = "同步数据")
    @RequestMapping(value = "/dataSync/sync_conf_data", method = RequestMethod.POST)
    BaseResponse<Object> syncConfData(@RequestBody DataSyncReqVo dataSyncReqVo);
}
